# Copyright (C) 2025 __x__
# This file is distributed under the same license as the __x__ package.
msgid ""
msgstr ""
"Project-Id-Version: __x__\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: cornerstone/_boot/pro.php:148, cornerstone/_boot/x.php:150
msgid "Validation"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:8, framework/legacy/functions/theme-options-controls.php:121
msgid "3D"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:9, framework/legacy/functions/theme-options-controls.php:122
msgid "Flat"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:10, framework/legacy/functions/theme-options-controls.php:123
msgid "Transparent"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:14, cornerstone/includes/theme-options/woocommerce.php:63, framework/legacy/functions/theme-options-controls.php:127
msgid "Square"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:15, cornerstone/includes/theme-options/woocommerce.php:64, framework/legacy/functions/theme-options-controls.php:128
msgid "Rounded"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:16, framework/legacy/functions/theme-options-controls.php:129
msgid "Pill"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:20, framework/legacy/functions/theme-options-controls.php:133
msgid "Mini"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:21, framework/legacy/functions/theme-options-controls.php:134
msgid "Small"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:22, cornerstone/includes/theme-options/fontawesome.php:44, framework/legacy/functions/theme-options-controls.php:135
msgid "Regular"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:23, framework/legacy/functions/theme-options-controls.php:136
msgid "Large"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:24, framework/legacy/functions/theme-options-controls.php:137
msgid "Extra Large"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:25, framework/legacy/functions/theme-options-controls.php:138
msgid "Jumbo"
msgstr ""

#: cornerstone/includes/config/theme-option-standalone-controls.php:173, framework/legacy/functions/theme-options-controls.php:358, framework/functions/pro/stacks/blank/blank.php:58
msgid "Setup"
msgstr ""

#: cornerstone/includes/elements/values.php:247
msgid "<span>This content will show up directly in its container.</span>"
msgstr ""

#: cornerstone/includes/elements/values.php:257
msgid "<div style=\"padding: 25px; line-height: 1.4; text-align: center;\">Add any HTML or custom content here.</div>"
msgstr ""

#: cornerstone/includes/elements/values.php:323, cornerstone/includes/elements/values.php:334
msgid "← Back"
msgstr ""

#: cornerstone/includes/elements/values.php:372, cornerstone/includes/elements/definitions/deprecated-search-dropdown.php:108, cornerstone/includes/elements/definitions/deprecated-search-modal.php:105, framework/legacy/functions/theme-options-controls.php:375, framework/legacy/templates/searchform.php:11, framework/legacy/templates/searchform.php:20, framework/legacy/functions/plugins/visual-composer.php:4009, framework/legacy/cranium/headers/functions/navbar.php:175
msgid "Search"
msgstr ""

#: cornerstone/includes/elements/values.php:551
msgid "Input your text here! The text element is intended for longform copy that could potentially include multiple paragraphs."
msgstr ""

#: cornerstone/includes/elements/values.php:570
msgid "Short and Sweet Headlines are Best!"
msgstr ""

#: cornerstone/includes/elements/values.php:595
msgid "Subheadline space"
msgstr ""

#: cornerstone/includes/elements/values.php:686
msgid "Learn More"
msgstr ""

#: cornerstone/includes/elements/values.php:744
msgid "Discover Now"
msgstr ""

#: cornerstone/includes/elements/values.php:745
msgid "We Have Answers"
msgstr ""

#: cornerstone/includes/elements/values.php:1669
msgid "Your Items"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:336, framework/legacy/stack-defaults.php:56
msgid "Sort Portfolio"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:397, framework/legacy/stack-defaults.php:242
msgid "See it Live!"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:398, framework/legacy/stack-defaults.php:241
msgid "Launch Project"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:421, framework/legacy/stack-defaults.php:240
msgid "Skills"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:439, framework/legacy/stack-defaults.php:243
msgid "Share this Project"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:579
msgid "Portfolio Item Settings"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:580
msgid "Select the appropriate options for your portfolio item."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:586, framework/legacy/functions/admin/meta-entries.php:32, framework/legacy/functions/admin/meta-entries.php:308
msgid "Body CSS Class(es)"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:587, framework/legacy/functions/admin/meta-entries.php:33, framework/legacy/functions/admin/meta-entries.php:309
msgid "Add a custom CSS class to the &lt;body&gt; element. Separate multiple class names with a space."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:593, framework/legacy/functions/admin/meta-entries.php:39, framework/legacy/functions/admin/meta-entries.php:322
msgid "Alternate Index Title"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:594, framework/legacy/functions/admin/meta-entries.php:40, framework/legacy/functions/admin/meta-entries.php:323
msgid "Filling out this text input will replace the standard title on all index pages (i.e. blog, category archives, search, et cetera) with this one."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:600
msgid "Portfolio Parent"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:601
msgid "Assign the parent portfolio page for this portfolio item. This will be used in various places throughout the theme such as your breadcrumbs. If \"Default\" is selected then the first page with the \"Layout - Portfolio\" template assigned to it will be used."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:607
msgid "Media Type"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:608
msgid "Select which kind of media you want to display for your portfolio. If selecting a \"Gallery,\" simply upload your images to this post and organize them in the order you want them to display."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:615
msgid "Featured Content"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:616
msgid "Select \"Media\" if you would like to show your video or gallery on the index page in place of the featured image. Note: will always use \"Thumbnail\" in Ethos due to Stack styling."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:623
msgid "Project Link"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:624
msgid "Provide an external link to the project you worked on if one is available."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:630, framework/legacy/functions/admin/meta-entries.php:60, framework/legacy/functions/admin/meta-entries.php:329
msgid "Background Image(s)"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:631, framework/legacy/functions/admin/meta-entries.php:61, framework/legacy/functions/admin/meta-entries.php:330
msgid "Click the button to upload your background image(s), or enter them in manually using the text field above. Loading multiple background images will create a slideshow effect. To clear, delete the image URLs from the text field and save your page."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:637, framework/legacy/functions/admin/meta-entries.php:67, framework/legacy/functions/admin/meta-entries.php:336
msgid "Background Image(s) Fade"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:638, framework/legacy/functions/admin/meta-entries.php:68, framework/legacy/functions/admin/meta-entries.php:337
msgid "Set a time in milliseconds for your image(s) to fade in. To disable this feature, set the value to \"0.\""
msgstr ""

#: cornerstone/includes/extend/portfolio.php:644, framework/legacy/functions/admin/meta-entries.php:74, framework/legacy/functions/admin/meta-entries.php:343
msgid "Background Images Duration"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:645, framework/legacy/functions/admin/meta-entries.php:75, framework/legacy/functions/admin/meta-entries.php:344
msgid "Only applicable if multiple images are selected, creating a background image slider. Set a time in milliseconds for your images to remain on screen."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:660
msgid "Video Portfolio Item Settings"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:661
msgid "These settings enable you to embed videos into your portfolio items."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:667
msgid "Video Aspect Ratio"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:668
msgid "If selecting \"Video,\" choose the aspect ratio you would like for your video."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:675, framework/legacy/functions/admin/meta-entries.php:427
msgid "M4V File URL"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:676
msgid "If selecting \"Video,\" place the URL to your .m4v video file here."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:682, framework/legacy/functions/admin/meta-entries.php:434
msgid "OGV File URL"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:683
msgid "If selecting \"Video,\" place the URL to your .ogv video file here."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:689, framework/legacy/functions/admin/meta-entries.php:441
msgid "Embedded Video Code"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:690, framework/legacy/functions/admin/meta-entries.php:442
msgid "If you are using something other than self hosted video such as YouTube, Vimeo, or Wistia, paste the embed code here. This field will override the above."
msgstr ""

#: cornerstone/includes/integration/cs-tgma.php:89, framework/legacy/functions/updates/class-x-tgmpa-integration.php:85
msgid "No plugin specified."
msgstr ""

#: cornerstone/includes/integration/cs-tgma.php:93, framework/legacy/functions/updates/class-x-tgmpa-integration.php:89
msgid "Your user account does not have permission to install plugins."
msgstr ""

#: cornerstone/includes/integration/cs-tgma.php:100, framework/legacy/functions/updates/class-x-tgmpa-integration.php:96
msgid "Plugin not registered."
msgstr ""

#: cornerstone/includes/integration/cs-tgma.php:105, framework/legacy/functions/updates/class-x-tgmpa-integration.php:101
msgid "Plugin already installed."
msgstr ""

#: cornerstone/includes/integration/cs-tgma.php:110, framework/legacy/functions/updates/class-x-tgmpa-integration.php:106
msgid "Your WordPress file permissions do not allow plugins to be installed."
msgstr ""

#: cornerstone/includes/integration/scroll-top.php:9, cornerstone/includes/theme-options/woocommerce.php:12
msgid "Left"
msgstr ""

#: cornerstone/includes/integration/scroll-top.php:10, cornerstone/includes/theme-options/woocommerce.php:13
msgid "Right"
msgstr ""

#: cornerstone/includes/integration/scroll-top.php:26
msgid "Scroll Top Anchor"
msgstr ""

#: cornerstone/includes/integration/scroll-top.php:29
msgid "Once activated, set the value (%) for how far down the page your users will need to scroll for it to appear. For example, if you want the scroll top anchor to appear once your users have scrolled halfway down your page, you would enter \"50\" into the field."
msgstr ""

#: cornerstone/includes/integration/scroll-top.php:34, framework/legacy/functions/theme-options-controls.php:1084
msgid "Position"
msgstr ""

#: cornerstone/includes/integration/scroll-top.php:41
msgid "When to Show"
msgstr ""

#: cornerstone/includes/integration/scroll-top.php:72
msgid "Back to Top"
msgstr ""

#: cornerstone/includes/theme-options/extensions.php:9, framework/legacy/functions/theme-options-controls.php:378, framework/legacy/functions/theme-options-controls.php:1610
msgid "Miscellaneous"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:16
msgid "Font Awesome"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:17
msgid "Below is a list of the various Font Awesome icon types. Enable or disable them depending on your preferences for usage (for example, if you only plan on using the \"Light\" icons, you can disable all other weights for a slight performance boost in Webfont mode). Keep in mind that completely disabling all Font Awesome icons means that you will not be able to utilize any of the icon pickers throughout our builders and that the markup for icons will still be output to the frontend of your site."
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:29
msgid "Element load types"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:30
msgid "Ability to change the Icon load type for individual elements that support it. This will also remove the SVG directory if this is disabled and load type is set to Webfonts. SVG requires PHP zip installed"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:38
msgid "Solid"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:50, framework/legacy/functions/theme-options-controls.php:79
msgid "Light"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:58
msgid "Sharp Regular"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:66
msgid "Sharp Light"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:74
msgid "Sharp Solid"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:81
msgid "Brands"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:14, cornerstone/includes/theme-options/layout.php:21, cornerstone/includes/theme-options/woocommerce.php:18, framework/legacy/functions/theme-options-controls.php:85, framework/legacy/functions/admin/meta-entries.php:276
msgid "Fullwidth"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:15
msgid "Boxed"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:19
msgid "Content / Sidebar"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:20
msgid "Sidebar / Content"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:77
msgid "Specify the values for various fundamental features of your site such as the primary width, max width, et cetera."
msgstr ""

#: cornerstone/includes/theme-options/layout.php:82
msgid "Site Layout"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:88
msgid "Content Layout"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:94
msgid "Site Width"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:100
msgid "Site Max Width"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:105
msgid "Content Width"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:112
msgid "Sidebar Width"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:119, framework/legacy/functions/plugins/visual-composer.php:4333, framework/legacy/functions/plugins/visual-composer.php:4497, plugins/tco-test-custom-element/tco-test-custom-element.php:104
msgid "Background Color"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:124, framework/legacy/functions/plugins/visual-composer.php:4341, framework/legacy/functions/plugins/visual-composer.php:4505
msgid "Background Pattern"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:130, framework/legacy/functions/plugins/visual-composer.php:4349, framework/legacy/functions/plugins/visual-composer.php:4513
msgid "Background Image"
msgstr ""

#: cornerstone/includes/theme-options/layout.php:135
msgid "Background Image Fade"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:11
msgid "Portfolio"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:17
msgid "Setting your custom portfolio slug allows you to create a unique URL structure for your archive pages that suits your needs. When you update it, remember to save your Permalinks again to avoid any potential errors."
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:22
msgid "Enable Portfolio"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:28
msgid "Custom URL Slug"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:34
msgid "Crop Featured"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:41, framework/legacy/functions/theme-options-controls.php:1570
msgid "Post Meta"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:47
msgid "Labels"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:48
msgid "Set the titles and labels for various parts of the portfolio here."
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:54
msgid "Tag List Title"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:59
msgid "Launch Title"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:64
msgid "Launch Button"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:69
msgid "Share Title"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:74, cornerstone/includes/theme-options/social.php:11, framework/legacy/functions/theme-options-controls.php:1479, framework/legacy/functions/plugins/buddypress.php:478, framework/legacy/functions/plugins/visual-composer.php:2582, framework/legacy/functions/plugins/visual-composer.php:3274, framework/legacy/functions/plugins/visual-composer.php:3449
msgid "Social"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:75
msgid "Enable various social sharing options for your portfolio items here."
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:83, cornerstone/includes/theme-options/social.php:22, framework/legacy/functions/plugins/visual-composer.php:3463
msgid "Facebook"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:89, framework/legacy/functions/plugins/visual-composer.php:3473
msgid "Twitter"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:95, cornerstone/includes/theme-options/social.php:42, framework/legacy/functions/plugins/visual-composer.php:3483
msgid "LinkedIn"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:101, cornerstone/includes/theme-options/social.php:72, framework/legacy/functions/plugins/visual-composer.php:3493
msgid "Pinterest"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:107, framework/legacy/functions/plugins/visual-composer.php:3503
msgid "Reddit"
msgstr ""

#: cornerstone/includes/theme-options/portfolio.php:113, framework/legacy/functions/frontend/ethos.php:419, framework/legacy/functions/frontend/icon.php:206, framework/legacy/functions/plugins/visual-composer.php:3513
msgid "Email"
msgstr ""

#: cornerstone/includes/theme-options/social.php:17
msgid "Set the URLs for your social media profiles here to be used in the topbar and bottom footer. Adding in a link will make its respective icon show up without needing to do anything else. Keep in mind that these sections are not necessarily intended for a lot of items, so adding all icons could create some layout issues. It is typically best to keep your selections here to a minimum for structural purposes and for usability purposes so you do not overwhelm your visitors."
msgstr ""

#: cornerstone/includes/theme-options/social.php:27
msgid "X"
msgstr ""

#: cornerstone/includes/theme-options/social.php:32
msgid "Tiktok"
msgstr ""

#: cornerstone/includes/theme-options/social.php:37
msgid "Bluesky"
msgstr ""

#: cornerstone/includes/theme-options/social.php:47
msgid "XING"
msgstr ""

#: cornerstone/includes/theme-options/social.php:52
msgid "Foursquare"
msgstr ""

#: cornerstone/includes/theme-options/social.php:57
msgid "YouTube"
msgstr ""

#: cornerstone/includes/theme-options/social.php:62
msgid "Vimeo"
msgstr ""

#: cornerstone/includes/theme-options/social.php:67
msgid "Instagram"
msgstr ""

#: cornerstone/includes/theme-options/social.php:77
msgid "Dribbble"
msgstr ""

#: cornerstone/includes/theme-options/social.php:82
msgid "Flickr"
msgstr ""

#: cornerstone/includes/theme-options/social.php:87
msgid "GitHub"
msgstr ""

#: cornerstone/includes/theme-options/social.php:92
msgid "Behance"
msgstr ""

#: cornerstone/includes/theme-options/social.php:97
msgid "Tumblr"
msgstr ""

#: cornerstone/includes/theme-options/social.php:102
msgid "Whatsapp"
msgstr ""

#: cornerstone/includes/theme-options/social.php:107
msgid "SoundCloud"
msgstr ""

#: cornerstone/includes/theme-options/social.php:112, framework/legacy/templates/buddypress/activity/index.php:71, framework/legacy/templates/buddypress/groups/single/activity.php:3
msgid "RSS Feed"
msgstr ""

#: cornerstone/includes/theme-options/social.php:117, cornerstone/includes/theme-options/social.php:123
msgid "Open Graph"
msgstr ""

#: cornerstone/includes/theme-options/social.php:118
msgid "X outputs standard Open Graph tags for your content. If you are employing another solution for this, you can disable X's Open Graph tag output here."
msgstr ""

#: cornerstone/includes/theme-options/social.php:128
msgid "Social Fallback"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:27
msgid "Stepped"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:28
msgid "Scaling"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:32
msgid "px"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:33
msgid "em"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:48, framework/legacy/functions/plugins/visual-composer.php:466, framework/legacy/functions/plugins/visual-composer.php:539, framework/legacy/functions/plugins/visual-composer.php:984, framework/legacy/functions/plugins/visual-composer.php:1566, framework/legacy/functions/plugins/visual-composer.php:1612, framework/legacy/functions/plugins/visual-composer.php:3703, framework/legacy/functions/plugins/visual-composer.php:3856, framework/legacy/functions/plugins/visual-composer.php:3961
msgid "Typography"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:54
msgid "Enable Font Manager"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:56
msgid "Assign your own font selections instead of directly using System or Google Fonts."
msgstr ""

#: cornerstone/includes/theme-options/typography.php:60
msgid "Root Font Size"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:61
msgid "Select the method for outputting your site's root font size, then adjust the settings to suit your design. \"Stepped\" mode allows you to set a font size at each of your site's breakpoints, whereas \"Scaling\" will dynamically scale between a range of minimum and maximum font sizes and breakpoints that you specify."
msgstr ""

#: cornerstone/includes/theme-options/typography.php:66
msgid "Mode"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:72
msgid "Unit"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:79
msgid "XS Breakpoint"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:85
msgid "SM Breakpoint"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:91
msgid "MD Breakpoint"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:97
msgid "LG Breakpoint"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:103
msgid "XL Breakpoint"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:109
msgid "Font Size Unit"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:116
msgid "Minimum Font Size"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:122
msgid "Maximum Font Size"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:128
msgid "Lower Limit"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:134
msgid "Upper Limit"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:140
msgid "Body and Content"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:141
msgid "\"Content Font Size\" will affect the sizing of all copy inside a post or page content area. It uses rems, which are a unit relative to your root font size. For example, if your root font size is 10px and you want your content font size to be 12px, you would enter \"1.2\" as a value. Headings are set with percentages and sized proportionally to these settings."
msgstr ""

#: cornerstone/includes/theme-options/typography.php:146, cornerstone/includes/theme-options/typography.php:152, cornerstone/includes/theme-options/typography.php:202, cornerstone/includes/theme-options/typography.php:208, framework/legacy/functions/theme-options-controls.php:1169, framework/legacy/functions/theme-options-controls.php:1175, framework/legacy/functions/theme-options-controls.php:1274, framework/legacy/functions/theme-options-controls.php:1280
msgid "Font Family"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:162, cornerstone/includes/theme-options/typography.php:168, cornerstone/includes/theme-options/typography.php:218, cornerstone/includes/theme-options/typography.php:224, framework/legacy/functions/theme-options-controls.php:1185, framework/legacy/functions/theme-options-controls.php:1191, framework/legacy/functions/theme-options-controls.php:1290, framework/legacy/functions/theme-options-controls.php:1296
msgid "Font Weight"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:178, cornerstone/includes/theme-options/typography.php:270, framework/legacy/functions/theme-options-controls.php:1207, framework/legacy/functions/theme-options-controls.php:1312
msgid "Italic"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:185, cornerstone/includes/theme-options/typography.php:282, cornerstone/includes/theme-options/typography.php:303, framework/legacy/functions/theme-options-controls.php:1029, framework/legacy/functions/theme-options-controls.php:1220, framework/legacy/functions/theme-options-controls.php:1328
msgid "Color"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:190
msgid "Content Font Size"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:196
msgid "Headings"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:197
msgid "The letter spacing controls for each heading level will only affect that heading if it does not have a \"looks like\" class or if the \"looks like\" class matches that level. For example, if you have an &lt;h1&gt; with no modifier class, the &lt;h1&gt; slider will affect that heading. However, if your &lt;h1&gt; has an .h2 modifier class, then the &lt;h2&gt; slider will take over as it is supposed to appear as an &lt;h2&gt;."
msgstr ""

#: cornerstone/includes/theme-options/typography.php:234
msgid "h1 Letter Spacing"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:240
msgid "h2 Letter Spacing"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:246
msgid "h3 Letter Spacing"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:252
msgid "h4 Letter Spacing"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:258
msgid "h5 Letter Spacing"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:264
msgid "h6 Letter Spacing"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:276, framework/legacy/functions/theme-options-controls.php:1214, framework/legacy/functions/theme-options-controls.php:1319
msgid "Uppercase"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:287
msgid "Widget Icons"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:293
msgid "Site Links"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:294
msgid "Site link colors are also used as accents for various elements throughout your site, so make sure to select something you really enjoy and keep an eye out for how it affects your design."
msgstr ""

#: cornerstone/includes/theme-options/typography.php:313, framework/functions/pro/stacks/blank/blank.php:83
msgid "Use OEmbed"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:318, framework/functions/pro/stacks/blank/blank.php:89
msgid "OEmbed for Internal Links"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:330
msgid "Google Subsets"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:337
msgid "Cyrillic"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:344
msgid "Greek"
msgstr ""

#: cornerstone/includes/theme-options/typography.php:351
msgid "Vietnamese"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce-ajax-cart.php:18
msgid "AJAX<br/>Color"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce-ajax-cart.php:27
msgid "AJAX Background"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce-ajax-cart.php:41, cornerstone/includes/theme-options/woocommerce.php:282
msgid "Cart"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce-ajax-cart.php:55, cornerstone/includes/theme-options/woocommerce.php:95, framework/legacy/functions/theme-options-controls.php:366
msgid "WooCommerce"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:17, framework/legacy/functions/theme-options-controls.php:84
msgid "Global"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:29, framework/functions/plugins/cornerstone.php:16, framework/legacy/functions/plugins/visual-composer.php:3915
msgid "Icon"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:30
msgid "Total"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:31
msgid "Count"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:51, framework/legacy/functions/theme-options-controls.php:154
msgid "Inline"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:52, framework/legacy/functions/theme-options-controls.php:155
msgid "Stacked"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:56
msgid "Single (Inner)"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:57
msgid "Single (Outer)"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:58
msgid "Double (Inner / Outer)"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:59
msgid "Double (Outer / Inner)"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:72
msgid "Cross Sells"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:78
msgid "Cross Sells Columns"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:85
msgid "Cross Sells Count"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:101, framework/legacy/functions/theme-options-controls.php:386
msgid "Navbar Cart"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:104
msgid "Enable a cart in your navigation that you can customize to showcase the information you want your users to see as they add merchandise to their cart (e.g. item count, subtotal, et cetera)."
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:109
msgid "Hide When Empty"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:116, framework/legacy/functions/plugins/visual-composer.php:611, framework/legacy/functions/plugins/visual-composer.php:917, framework/legacy/functions/plugins/visual-composer.php:3561, framework/legacy/functions/plugins/visual-composer.php:3642, framework/legacy/functions/plugins/visual-composer.php:4056
msgid "Information"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:123, framework/legacy/functions/theme-options-controls.php:1008, framework/legacy/functions/theme-options-controls.php:1519, framework/legacy/functions/theme-options-controls.php:1545, framework/legacy/functions/plugins/visual-composer.php:349, framework/legacy/functions/plugins/visual-composer.php:401, framework/legacy/functions/plugins/visual-composer.php:444, framework/legacy/functions/plugins/visual-composer.php:517, framework/legacy/functions/plugins/visual-composer.php:589, framework/legacy/functions/plugins/visual-composer.php:674, framework/legacy/functions/plugins/visual-composer.php:736, framework/legacy/functions/plugins/visual-composer.php:843, framework/legacy/functions/plugins/visual-composer.php:962, framework/legacy/functions/plugins/visual-composer.php:1014, framework/legacy/functions/plugins/visual-composer.php:1237, framework/legacy/functions/plugins/visual-composer.php:1296, framework/legacy/functions/plugins/visual-composer.php:1350, framework/legacy/functions/plugins/visual-composer.php:1377, framework/legacy/functions/plugins/visual-composer.php:1544, framework/legacy/functions/plugins/visual-composer.php:1590, framework/legacy/functions/plugins/visual-composer.php:1652, framework/legacy/functions/plugins/visual-composer.php:1704, framework/legacy/functions/plugins/visual-composer.php:1825, framework/legacy/functions/plugins/visual-composer.php:1902, framework/legacy/functions/plugins/visual-composer.php:1948, framework/legacy/functions/plugins/visual-composer.php:2028, framework/legacy/functions/plugins/visual-composer.php:2101, framework/legacy/functions/plugins/visual-composer.php:2164, framework/legacy/functions/plugins/visual-composer.php:2210, framework/legacy/functions/plugins/visual-composer.php:2266, framework/legacy/functions/plugins/visual-composer.php:2328, framework/legacy/functions/plugins/visual-composer.php:2454, framework/legacy/functions/plugins/visual-composer.php:2508, framework/legacy/functions/plugins/visual-composer.php:2560, framework/legacy/functions/plugins/visual-composer.php:2677, framework/legacy/functions/plugins/visual-composer.php:2746, framework/legacy/functions/plugins/visual-composer.php:2798, framework/legacy/functions/plugins/visual-composer.php:2859, framework/legacy/functions/plugins/visual-composer.php:2964, framework/legacy/functions/plugins/visual-composer.php:3080, framework/legacy/functions/plugins/visual-composer.php:3147, framework/legacy/functions/plugins/visual-composer.php:3295, framework/legacy/functions/plugins/visual-composer.php:3427, framework/legacy/functions/plugins/visual-composer.php:3539, framework/legacy/functions/plugins/visual-composer.php:3620, framework/legacy/functions/plugins/visual-composer.php:3681, framework/legacy/functions/plugins/visual-composer.php:3788, framework/legacy/functions/plugins/visual-composer.php:3834, framework/legacy/functions/plugins/visual-composer.php:3939, framework/legacy/functions/plugins/visual-composer.php:4034, framework/legacy/functions/plugins/visual-composer.php:4150, framework/legacy/functions/plugins/visual-composer.php:4405, framework/legacy/functions/plugins/visual-composer.php:4569, framework/legacy/functions/plugins/visual-composer.php:4644, framework/legacy/functions/plugins/visual-composer.php:4719
msgid "Style"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:130, cornerstone/includes/theme-options/woocommerce.php:191, framework/legacy/functions/theme-options-controls.php:1125, framework/legacy/functions/theme-options-controls.php:1525, framework/legacy/functions/theme-options-controls.php:1551, framework/legacy/functions/theme-options-controls.php:1644, framework/legacy/functions/theme-options-controls.php:1705, framework/legacy/functions/admin/meta-entries.php:271
msgid "Layout"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:137, framework/legacy/functions/theme-options-controls.php:1384, framework/legacy/functions/plugins/visual-composer.php:488, framework/legacy/functions/plugins/visual-composer.php:561, framework/legacy/functions/plugins/visual-composer.php:2991, framework/legacy/functions/plugins/visual-composer.php:3339, framework/legacy/functions/plugins/visual-composer.php:3578, framework/legacy/functions/plugins/visual-composer.php:3717, framework/legacy/functions/plugins/visual-composer.php:3870
msgid "Alignment"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:143
msgid "Inner Content"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:163
msgid "Outer Content"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:184
msgid "Shop"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:186
msgid "This section handles all options regarding your WooCommerce setup. The \"Placeholder Thumbnail\" will show up for items in your shop that do not yet have a featured image assigned. Make sure that the thumbanil you provide matches the image dimensions you specify in WooCommerce's Customizer settings."
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:197, cornerstone/includes/elements/definitions/classic-row-v2.php:121, framework/legacy/functions/theme-options-controls.php:383, framework/legacy/functions/theme-options-controls.php:1531, framework/legacy/functions/theme-options-controls.php:1557, framework/legacy/functions/admin/meta-entries.php:263, framework/legacy/functions/plugins/visual-composer.php:2828, framework/legacy/functions/plugins/visual-composer.php:3591
msgid "Columns"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:203, framework/legacy/functions/theme-options-controls.php:842, framework/legacy/functions/theme-options-controls.php:900, framework/legacy/functions/theme-options-controls.php:930, framework/legacy/functions/admin/meta-entries.php:279
msgid "Posts Per Page"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:208
msgid "Placeholder Thumbnail"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:214
msgid "Single Product (Tabs)"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:216
msgid "All options available in this section pertain to the layout of your individual product pages. Enable or disable the sections you want to use to achieve the layout you want."
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:221
msgid "Description Tab"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:228
msgid "Additional Info Tab"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:235
msgid "Reviews Tab"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:243
msgid "Single Product (Related Products)"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:249
msgid "Related Columns"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:256
msgid "Related Count"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:263
msgid "Single Product (Upsells)"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:269
msgid "Upsells Columns"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:276
msgid "Upsells Count"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:283
msgid "All options available in this section pertain to the layout of your cart page. Enable or disable the sections you want to use to achieve the layout you want."
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:288, framework/legacy/functions/theme-options-controls.php:384
msgid "Widgets"
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:289
msgid "Select the placement of your product images in the various WooCommerce widgets that provide them. Right alignment is better if your items have longer titles to avoid staggered word wrapping."
msgstr ""

#: cornerstone/includes/theme-options/woocommerce.php:294
msgid "Image Alignment"
msgstr ""

#: cornerstone/includes/classes/Elements/NavMenu.php:113
msgid "Go Back One Level"
msgstr ""

#: cornerstone/includes/classes/Elements/NavMenu.php:223
msgid "Toggle Collapsed Sub Menu"
msgstr ""

#: cornerstone/includes/classes/Elements/NavMenu.php:245
msgid "Toggle Layered Sub Menu"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:333
msgctxt "User Display Name (User Login)"
msgid "%s (%s)"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:506
msgid "Date"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:507
msgid "Modified Date"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:508, framework/legacy/functions/theme-options-controls.php:563, framework/legacy/functions/theme-options-controls.php:580, framework/legacy/functions/theme-options-controls.php:781, framework/legacy/functions/theme-options-controls.php:798, framework/legacy/functions/theme-options-controls.php:809, framework/legacy/functions/theme-options-controls.php:820, framework/legacy/functions/plugins/visual-composer.php:1130, framework/legacy/functions/plugins/visual-composer.php:1437, framework/legacy/functions/plugins/visual-composer.php:1994, framework/legacy/functions/plugins/visual-composer.php:2130, framework/legacy/functions/plugins/visual-composer.php:2897, framework/legacy/functions/plugins/visual-composer.php:3004, framework/legacy/functions/plugins/visual-composer.php:3303, framework/legacy/functions/plugins/visual-composer.php:3351, framework/legacy/functions/plugins/visual-composer.php:3454, framework/legacy/functions/plugins/visual-composer.php:3569, framework/legacy/functions/plugins/visual-composer.php:3649
msgid "Title"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:509
msgid "Slug"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:510, framework/legacy/functions/plugins/visual-composer.php:2587
msgid "Post Type"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:511, framework/legacy/functions/plugins/visual-composer.php:333, framework/legacy/functions/plugins/visual-composer.php:385, framework/legacy/functions/plugins/visual-composer.php:428, framework/legacy/functions/plugins/visual-composer.php:501, framework/legacy/functions/plugins/visual-composer.php:573, framework/legacy/functions/plugins/visual-composer.php:658, framework/legacy/functions/plugins/visual-composer.php:720, framework/legacy/functions/plugins/visual-composer.php:946, framework/legacy/functions/plugins/visual-composer.php:998, framework/legacy/functions/plugins/visual-composer.php:1221, framework/legacy/functions/plugins/visual-composer.php:1280, framework/legacy/functions/plugins/visual-composer.php:1334, framework/legacy/functions/plugins/visual-composer.php:1528, framework/legacy/functions/plugins/visual-composer.php:1574, framework/legacy/functions/plugins/visual-composer.php:1636, framework/legacy/functions/plugins/visual-composer.php:1688, framework/legacy/functions/plugins/visual-composer.php:1809, framework/legacy/functions/plugins/visual-composer.php:1886, framework/legacy/functions/plugins/visual-composer.php:1932, framework/legacy/functions/plugins/visual-composer.php:2012, framework/legacy/functions/plugins/visual-composer.php:2085, framework/legacy/functions/plugins/visual-composer.php:2148, framework/legacy/functions/plugins/visual-composer.php:2194, framework/legacy/functions/plugins/visual-composer.php:2312, framework/legacy/functions/plugins/visual-composer.php:2438, framework/legacy/functions/plugins/visual-composer.php:2492, framework/legacy/functions/plugins/visual-composer.php:2544, framework/legacy/functions/plugins/visual-composer.php:2661, framework/legacy/functions/plugins/visual-composer.php:2730, framework/legacy/functions/plugins/visual-composer.php:2782, framework/legacy/functions/plugins/visual-composer.php:2843, framework/legacy/functions/plugins/visual-composer.php:2948, framework/legacy/functions/plugins/visual-composer.php:3064, framework/legacy/functions/plugins/visual-composer.php:3131, framework/legacy/functions/plugins/visual-composer.php:3279, framework/legacy/functions/plugins/visual-composer.php:3411, framework/legacy/functions/plugins/visual-composer.php:3523, framework/legacy/functions/plugins/visual-composer.php:3604, framework/legacy/functions/plugins/visual-composer.php:3665, framework/legacy/functions/plugins/visual-composer.php:3772, framework/legacy/functions/plugins/visual-composer.php:3818, framework/legacy/functions/plugins/visual-composer.php:3923, framework/legacy/functions/plugins/visual-composer.php:4018, framework/legacy/functions/plugins/visual-composer.php:4134, framework/legacy/functions/plugins/visual-composer.php:4626, framework/legacy/functions/plugins/visual-composer.php:4701
msgid "ID"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:512, framework/legacy/functions/plugins/visual-composer.php:1986
msgid "Parent ID"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:513, framework/legacy/functions/theme-options-controls.php:95, framework/legacy/functions/plugins/visual-composer.php:2398
msgid "Random"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:514
msgid "Comment Count"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:515
msgid "Page Order (Menu)"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:516
msgid "Relevance"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:517, framework/legacy/functions/frontend/ethos.php:348, framework/legacy/functions/plugins/visual-composer.php:3270
msgid "Author"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:518
msgid "Meta Value"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:519
msgid "Meta Value Number"
msgstr ""

#: cornerstone/includes/classes/Services/Locator.php:520
msgid "Ignore (Use Meta or Plugin)"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:103, framework/legacy/functions/admin/class-validation-extensions.php:33
msgid "Error encountered."
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:104, framework/legacy/functions/theme-options-controls.php:1752, framework/legacy/functions/theme-options-controls.php:1784, framework/legacy/functions/admin/class-validation-extensions.php:34, framework/legacy/templates/buddypress/members/activate.php:27
msgid "Activate"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:105, framework/legacy/functions/admin/class-validation-extensions.php:35
msgid "Installed & Activated"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:106, framework/legacy/functions/admin/class-validation-extensions.php:36, framework/legacy/functions/admin/class-validation.php:81, framework/legacy/functions/admin/class-validation.php:139
msgid "Go Back"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:107, framework/legacy/functions/admin/class-validation-extensions.php:37
msgid "Installing&hellip;"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:108, framework/legacy/functions/admin/class-validation-extensions.php:38
msgid "Activating&hellip;"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:109, framework/legacy/functions/admin/class-validation-extensions.php:39
msgid "Waiting to install&hellip;"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:110, framework/legacy/functions/admin/class-validation-extensions.php:40
msgid "Waiting to activate&hellip;"
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:342, framework/functions/plugins/cornerstone.php:100, framework/legacy/functions/admin/class-validation.php:232
msgid "How do I unlock this feature?"
msgstr ""

#: cornerstone/includes/elements/control-partials/effects.php:850
msgid "The mask-composite property will only work when two or more images are applied."
msgstr ""

#: cornerstone/includes/elements/control-partials/effects.php:865
msgid "As these features are more experimental in nature and may not behave consistently from browser to browser, we recommend using them in a &ldquo;progressive enhancement&rdquo; capacity for your designs."
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-consumer.php:29
msgid "Consume data from the closest Looper Provider, or the main query."
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:33
msgid "Begin a new dynamic content data source to loop over."
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:416
msgid "Custom PHP must be used to integrate with cs_looper_custom_my_data hook."
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:427
msgid "Content must be valid JSON. It will be decoded and passed as $params into your hook."
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:440
msgid "Content must be valid JSON with the top level being an array of objects. The object keys will be available in Dynamic Content."
msgstr ""

#: cornerstone/includes/elements/control-partials/omega.php:160
msgid "Will apply styles from elements of the same type"
msgstr ""

#: cornerstone/includes/elements/control-partials/separator.php:40
msgid "Angle In"
msgstr ""

#: cornerstone/includes/elements/control-partials/separator.php:41
msgid "Angle Out"
msgstr ""

#: cornerstone/includes/elements/control-partials/separator.php:42
msgid "Curve In"
msgstr ""

#: cornerstone/includes/elements/control-partials/separator.php:43
msgid "Curve Out"
msgstr ""

#: cornerstone/includes/elements/control-partials/separator.php:51
msgid "Point Align"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion-item.php:25
msgid "This is the accordion body content. It is typically best to keep this area short and to the point so it isn't too overwhelming."
msgstr ""

#: cornerstone/includes/elements/definitions/alert.php:27
msgid "This is where the content for your alert goes. Best to keep it short and sweet!"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:171, cornerstone/includes/elements/definitions/classic-row-v2.php:154
msgid "Font Size &amp; Z-Index"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:181
msgid "Fade In Effect"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:204, framework/legacy/functions/frontend/ethos.php:90, framework/legacy/functions/frontend/ethos.php:68
msgid "In"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:205
msgid "In From Top"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:206
msgid "In From Left"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:207
msgid "In From Right"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:208
msgid "In From Bottom"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:215
msgid "Duration &amp; Animation"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:226
msgid "Animation Offset"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:245, cornerstone/includes/elements/definitions/classic-column-v2.php:262, cornerstone/includes/elements/definitions/classic-row-v2.php:226, cornerstone/includes/elements/definitions/classic-row-v2.php:243, framework/legacy/functions/theme-options-controls.php:183, framework/legacy/functions/theme-options-controls.php:1038
msgid "Background"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:255, cornerstone/includes/elements/definitions/classic-row-v2.php:236
msgid "Advanced"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:272, cornerstone/includes/elements/definitions/classic-row-v2.php:253
msgid "Text Align"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:309, cornerstone/includes/elements/definitions/classic-row-v2.php:292
msgid "Formatting"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-column-v2.php:331
msgid "Classic Column (v2)"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-row-v2.php:128
msgid "Base Font Size"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-row-v2.php:144
msgid "Z-Index"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-row-v2.php:164, framework/legacy/functions/plugins/visual-composer.php:4272, framework/legacy/functions/plugins/visual-composer.php:4436
msgid "Inner Container"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-row-v2.php:208
msgid "Width &amp; Max Width"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-row-v2.php:219, framework/legacy/functions/plugins/visual-composer.php:4385, framework/legacy/functions/plugins/visual-composer.php:4549
msgid "Marginless Columns"
msgstr ""

#: cornerstone/includes/elements/definitions/classic-row-v2.php:324
msgid "Classic Row (v2)"
msgstr ""

#: cornerstone/includes/elements/definitions/countdown.php:36
msgid "Countdown ends in {{d}} days, {{h}} hours, and {{m}} minutes."
msgstr ""

#: cornerstone/includes/elements/definitions/creative-cta.php:36
msgid "Got Questions?"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area-dropdown.php:104, cornerstone/includes/elements/definitions/layout-dropdown.php:148
msgid "Dropdown"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area-dropdown.php:119
msgid "Content Area Dropdown"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area-modal.php:100, cornerstone/includes/elements/definitions/layout-modal.php:120
msgid "Modal"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area-modal.php:115
msgid "Content Area Modal"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area-off-canvas.php:100, cornerstone/includes/elements/definitions/layout-off-canvas.php:120
msgid "Off Canvas"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area-off-canvas.php:115
msgid "Content Area Off Canvas"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area.php:87, framework/legacy/functions/theme-options-controls.php:1486
msgid "Content Area"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-responsive-text.php:114, framework/legacy/functions/plugins/visual-composer.php:3957
msgid "Responsive Text"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-search-dropdown.php:120
msgid "Search Dropdown"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-search-modal.php:117
msgid "Search Modal"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-tp-wc-cart-dropdown.php:139
msgid "Cart Dropdown"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-tp-wc-cart-modal.php:133
msgid "Cart Modal"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-tp-wc-cart-off-canvas.php:136
msgid "Cart Off Canvas"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:547
msgid "Form Integration"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:392
msgid "When using an \"Inline\" layout only, individual slides can have Scroll Effects applied to augment their entrance and exit states within their slider context."
msgstr ""

#: cornerstone/includes/elements/definitions/statbar.php:55
msgid "HTML &amp; CSS"
msgstr ""

#: cornerstone/includes/elements/definitions/tab.php:25
msgid "This is the tab body content. It is typically best to keep this area short and to the point so it isn't too overwhelming."
msgstr ""

#: cornerstone/includes/elements/definitions/tp-bbp-dropdown.php:140
msgid "bbPress Dropdown"
msgstr ""

#: cornerstone/includes/elements/definitions/tp-bp-dropdown.php:164
msgid "BuddyPress Dropdown"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:267
msgid "Click + to begin creating a Column Template."
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:279
msgid "Click + to begin creating a Row Template."
msgstr ""

#: cornerstone/includes/functions/CustomStacks/comments.php:11, framework/legacy/functions/frontend/ethos.php:334, framework/legacy/functions/frontend/icon.php:78, framework/legacy/functions/frontend/integrity.php:170, framework/legacy/functions/frontend/renew.php:172
msgid "Pingback:"
msgstr ""

#: cornerstone/includes/functions/CustomStacks/comments.php:11, framework/legacy/functions/frontend/ethos.php:334, framework/legacy/functions/frontend/icon.php:78, framework/legacy/functions/frontend/integrity.php:170, framework/legacy/functions/frontend/renew.php:172
msgid "(Edit)"
msgstr ""

#: cornerstone/includes/functions/CustomStacks/comments.php:46, framework/legacy/functions/frontend/ethos.php:376, framework/legacy/functions/frontend/icon.php:110, framework/legacy/functions/frontend/integrity.php:206, framework/legacy/functions/frontend/renew.php:207
msgid "Rated %d out of 5"
msgstr ""

#: cornerstone/includes/functions/CustomStacks/comments.php:47, framework/legacy/functions/frontend/ethos.php:377, framework/legacy/functions/frontend/icon.php:111, framework/legacy/functions/frontend/integrity.php:207, framework/legacy/functions/frontend/renew.php:208
msgid "out of 5"
msgstr ""

#: cornerstone/includes/functions/CustomStacks/comments.php:54, framework/legacy/functions/frontend/icon.php:89, framework/legacy/functions/frontend/integrity.php:197, framework/legacy/functions/frontend/renew.php:215
msgid "%1$s at %2$s"
msgstr ""

#: cornerstone/includes/functions/CustomStacks/comments.php:63, framework/legacy/functions/frontend/ethos.php:383, framework/legacy/functions/frontend/icon.php:124, framework/legacy/functions/frontend/integrity.php:213, framework/legacy/functions/frontend/renew.php:224
msgid "Your comment is awaiting moderation."
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:46
msgid "Max"
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:54
msgid "Refresh Validation"
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:86, cornerstone/includes/integration/Max/views/page-home-box-max.php:151, framework/legacy/functions/admin/class-validation.php:38, framework/legacy/functions/admin/markup/page-home-box-approved-plugins.php:48, framework/legacy/functions/admin/markup/page-home-box-extensions.php:51
msgid "Details"
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:93, cornerstone/includes/integration/Max/views/page-home-box-max.php:157
msgid "Purchase"
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:90, framework/legacy/functions/admin/markup/page-home-box-approved-plugins.php:50, framework/legacy/functions/admin/markup/page-home-box-extensions.php:53
msgid "Install"
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:154
msgid "Access"
msgstr ""

#: framework/legacy/setup.php:271
msgid "Header "
msgstr ""

#: framework/legacy/setup.php:273
msgid "Widgetized header area."
msgstr ""

#: framework/legacy/setup.php:288
msgid "Footer "
msgstr ""

#: framework/legacy/setup.php:290
msgid "Widgetized footer area."
msgstr ""

#: framework/legacy/stack-defaults.php:41, framework/legacy/stack-defaults.php:71
msgid "The Blog"
msgstr ""

#: framework/legacy/stack-defaults.php:42
msgid "The Shop"
msgstr ""

#: framework/legacy/stack-defaults.php:55
msgid "Welcome to our little corner of the Internet. Kick your feet up and stay a while."
msgstr ""

#: framework/legacy/stack-defaults.php:60
msgid "Welcome to our online store. Take some time to browse through our items."
msgstr ""

#: framework/legacy/stack-defaults.php:259, framework/legacy/functions/theme-options-controls.php:1727
msgid "Activity"
msgstr ""

#: framework/legacy/stack-defaults.php:260, framework/legacy/functions/theme-options-controls.php:1732, framework/legacy/functions/theme-options-controls.php:1764
msgid "Groups"
msgstr ""

#: framework/legacy/stack-defaults.php:261, framework/legacy/functions/theme-options-controls.php:1737, framework/legacy/functions/theme-options-controls.php:1769, framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:33
msgid "Sites"
msgstr ""

#: framework/legacy/stack-defaults.php:262, framework/legacy/functions/theme-options-controls.php:1742, framework/legacy/functions/theme-options-controls.php:1774, framework/legacy/templates/buddypress/groups/single/admin.php:247
msgid "Members"
msgstr ""

#: framework/legacy/stack-defaults.php:263
msgid "Create An Account"
msgstr ""

#: framework/legacy/stack-defaults.php:264
msgid "Activate Your Account"
msgstr ""

#: framework/legacy/stack-defaults.php:265
msgid "Meet new people, get involved, and stay connected."
msgstr ""

#: framework/legacy/stack-defaults.php:266
msgid "Find others with similar interests and get plugged in."
msgstr ""

#: framework/legacy/stack-defaults.php:267
msgid "See what others are writing about. Learn something new and exciting today!"
msgstr ""

#: framework/legacy/stack-defaults.php:268
msgid "Meet your new online community. Kick up your feet and stay awhile."
msgstr ""

#: framework/legacy/stack-defaults.php:269
msgid "Just fill in the fields below and we'll get a new account set up for you in no time!"
msgstr ""

#: framework/legacy/stack-defaults.php:270
msgid "You're almost there! Simply enter your activation code below and we'll take care of the rest."
msgstr ""

#: framework/functions/plugins/cornerstone.php:14
msgid "Integrity"
msgstr ""

#: framework/functions/plugins/cornerstone.php:15
msgid "Renew"
msgstr ""

#: framework/functions/plugins/cornerstone.php:17
msgid "Ethos"
msgstr ""

#: framework/functions/plugins/cornerstone.php:78, framework/legacy/functions/admin/markup/page-home-box-validation.php:34
msgid "You&apos;re almost finished!"
msgstr ""

#: framework/functions/plugins/cornerstone.php:79
msgid "Great to see you&apos;re using Cornerstone with X, but it is ​<strong>not validated</strong>. Once X is validated, Cornerstone will automatically be validated as well. You&apos;ll also have instant access to support, automatic updates, custom templates, and more."
msgstr ""

#: framework/functions/plugins/cornerstone.php:80
msgid "CLICK HERE TO VALIDATE"
msgstr ""

#: framework/functions/plugins/cornerstone.php:101
msgid "By validating X. Once X is validated, Cornerstone will automatically be validated as well.<br><br>You can validate X <a href=\"%s\">here</a>."
msgstr ""

#: framework/functions/plugins/cornerstone.php:156
msgid "We see this is an X site, would you like to purchase another X license? Cornerstone is always included for free with X."
msgstr ""

#: framework/functions/plugins/cornerstone.php:158
msgid "Purchase X (includes Cornerstone)"
msgstr ""

#: framework/functions/plugins/cornerstone.php:159
msgid "Just Cornerstone"
msgstr ""

#: framework/functions/pro/migration.php:179
msgid "Congratulations, you&apos;ve successfully updated Pro! <strong><a href=\"%s\" target=\"_blank\">Release Notes</a></strong>."
msgstr ""

#: framework/functions/pro/migration.php:209, framework/functions/x/migration.php:440
msgid "Take the Slider Element to the next level with <strong><a href=\"%s\" target=\"_blank\">Modern Sliders</a></strong> — our new course and expansion pack!"
msgstr ""

#: framework/functions/x/migration.php:321
msgid "Congratulations, you&apos;ve successfully updated X! <strong><a href=\"%s\" target=\"_blank\">Release Notes</a></strong>. Make sure you also update Cornerstone!"
msgstr ""

#: framework/legacy/functions/setup.php:102
msgid "Primary Menu"
msgstr ""

#: framework/legacy/functions/setup.php:103
msgid "Footer Menu"
msgstr ""

#: framework/legacy/functions/setup.php:119
msgid "Main Sidebar"
msgstr ""

#: framework/legacy/functions/setup.php:121
msgid "Appears on posts and pages that include the sidebar."
msgstr ""

#: framework/legacy/functions/setup.php:159, framework/legacy/functions/setup.php:157, framework/legacy/functions/setup.php:178, framework/legacy/functions/plugins/buddypress.php:69
msgid "Read More"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:80
msgid "Dark"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:89, framework/legacy/functions/theme-options-controls.php:108
msgid "Standard"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:90
msgid "Creative"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:94
msgid "Most Commented"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:96, framework/legacy/functions/plugins/visual-composer.php:2905
msgid "Featured"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:109
msgid "Masonry"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:142
msgid "Static Top"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:143
msgid "Fixed Top"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:144
msgid "Fixed Left"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:145
msgid "Fixed Right"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:149
msgid "On (no submenu support)"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:150, plugins/tco-test-custom-element/tco-test-custom-element.php:82
msgid "Off"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:182, framework/legacy/functions/theme-options-controls.php:1152, framework/legacy/functions/plugins/visual-composer.php:471, framework/legacy/functions/plugins/visual-composer.php:544, framework/legacy/functions/plugins/visual-composer.php:616, framework/legacy/functions/plugins/visual-composer.php:989, framework/legacy/functions/plugins/visual-composer.php:1041, framework/legacy/functions/plugins/visual-composer.php:1325, framework/legacy/functions/plugins/visual-composer.php:1619, framework/legacy/functions/plugins/visual-composer.php:1679, framework/legacy/functions/plugins/visual-composer.php:1977, framework/legacy/functions/plugins/visual-composer.php:2239, framework/legacy/functions/plugins/visual-composer.php:2483, framework/legacy/functions/plugins/visual-composer.php:2535, framework/legacy/functions/plugins/visual-composer.php:2888, framework/legacy/functions/plugins/visual-composer.php:3107, framework/legacy/functions/plugins/visual-composer.php:3708, framework/legacy/functions/plugins/visual-composer.php:3861, framework/legacy/functions/plugins/visual-composer.php:4168, framework/legacy/functions/plugins/visual-composer.php:4177
msgid "Text"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:184, framework/legacy/functions/admin/meta-boxes.php:173
msgid "Select"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:359
msgid "Blog Options"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:360
msgid "Portfolio Options"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:361
msgid "Shop Options"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:362
msgid "Typography Options"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:363
msgid "Post Carousel"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:364
msgid "Post Slider (Blog)"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:365
msgid "Post Slider (Archive)"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:367
msgid "Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:368
msgid "Navbar"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:369
msgid "Logo and Navigation"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:370
msgid "Logo"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:371
msgid "Logo (Image)"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:372
msgid "Logo (Alignment)"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:373
msgid "Links"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:374
msgid "Links (Alignment)"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:376
msgid "Mobile Button"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:377
msgid "Widgetbar"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:379
msgid "Archives"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:380, framework/legacy/functions/plugins/bbpress.php:466, framework/legacy/functions/plugins/visual-composer.php:1259, framework/legacy/functions/plugins/visual-composer.php:1318, framework/legacy/functions/plugins/visual-composer.php:1674, framework/legacy/functions/plugins/visual-composer.php:1924, framework/legacy/functions/plugins/visual-composer.php:1970, framework/legacy/functions/plugins/visual-composer.php:2050, framework/legacy/functions/plugins/visual-composer.php:2123, framework/legacy/functions/plugins/visual-composer.php:2186, framework/legacy/functions/plugins/visual-composer.php:2232, framework/legacy/functions/plugins/visual-composer.php:2288, framework/legacy/functions/plugins/visual-composer.php:2530, framework/legacy/functions/plugins/visual-composer.php:4013, framework/legacy/functions/plugins/visual-composer.php:4172
msgid "Content"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:381
msgid "Titles"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:382
msgid "Subtitles"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:385, framework/legacy/functions/plugins/visual-composer.php:72
msgid "Enable"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:403
msgid "Stack Options"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:420
msgid "Design"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:427
msgid "Transparent Topbar"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:434
msgid "Transparent Navbar"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:441
msgid "Transparent Footer"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:456, framework/legacy/functions/theme-options-controls.php:1420
msgid "Topbar"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:463, framework/legacy/functions/theme-options-controls.php:520
msgid "Topbar Background"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:469
msgid "Logobar Background"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:475, framework/legacy/functions/theme-options-controls.php:526
msgid "Navbar Background"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:481
msgid "Toggle"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:490, framework/legacy/functions/theme-options-controls.php:1406
msgid "Toggle Background"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:497
msgid "Entry Icons"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:503
msgid "Footer"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:509
msgid "Footer Background"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:532
msgid "Widget Headings"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:538
msgid "Widget Text"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:556, framework/legacy/functions/theme-options-controls.php:774
msgid "Header"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:569, framework/legacy/functions/theme-options-controls.php:787
msgid "Subtitle"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:586
msgid "Entry Icon"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:593
msgid "Horizontal Alignment"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:600
msgid "Vertical Alignment"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:612
msgid "Post Icons"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:618
msgid "Standard<br/>Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:634
msgid "Image<br/>Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:650
msgid "Gallery<br/>Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:666
msgid "Video<br/>Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:682
msgid "Audio<br/>Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:698
msgid "Quote<br/>Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:714
msgid "Link<br/>Colors"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:735
msgid "Filterable Index"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:741
msgid "Category IDs"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:754
msgid "Sort Button Text"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:759
msgid "Index Sharing"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:835, framework/legacy/functions/theme-options-controls.php:893, framework/legacy/functions/theme-options-controls.php:923
msgid "Display"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:849
msgid "Extra Large<br/>Count"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:856
msgid "Large<br/>Count"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:863
msgid "Medium<br/>Count"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:870
msgid "Small<br/>Count"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:877
msgid "Extra Small<br/>Count"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:907, framework/legacy/functions/theme-options-controls.php:937, framework/legacy/functions/plugins/visual-composer.php:810
msgid "Height"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:962, framework/functions/pro/stacks/starter/starter.php:58
msgid "Layout and Design"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:996
msgid "Buttons"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1003
msgid "The button styles specified here will be utilized for native WordPress elements such as comment forms, add to cart actions, et cetera."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1014, framework/legacy/functions/plugins/visual-composer.php:1050
msgid "Shape"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1020, framework/legacy/functions/theme-options-controls.php:1378, framework/legacy/functions/plugins/visual-composer.php:376, framework/legacy/functions/plugins/visual-composer.php:1063
msgid "Size"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1048, framework/legacy/functions/plugins/visual-composer.php:4314, framework/legacy/functions/plugins/visual-composer.php:4478
msgid "Border"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1057
msgid "Bottom"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1072
msgid "Headers"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1078
msgid "\"Top Height\" must still be set even when using \"Fixed Left\" or \"Fixed Right\" positioning because on tablet and mobile devices, the menu is pushed to the top."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1090
msgid "Scrolling"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1097
msgid "Top Height"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1103
msgid "Side Width"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1112
msgid "Subindicator"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1113
msgid "The icon to use when displaying menus that have mutliple levels"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1119
msgid "Selecting \"Inline\" for your logo and navigation layout will place them both in the navbar. Selecting \"Stacked\" will place the logo in a separate section above the navbar."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1131
msgid "Logobar Top"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1138
msgid "Logobar Bottom"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1146
msgid "Your logo will show up as the site title by default, but can be overwritten below (it is also used as the alt text should you choose to use an image). Alternately, if you would like to use an image, upload it below. Logo alignment can also be adjusted in this section."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1157
msgid "Hidden &lt;h1&gt;"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1163, framework/legacy/functions/theme-options-controls.php:1268
msgid "Font Size"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1201, framework/legacy/functions/theme-options-controls.php:1306
msgid "Letter Spacing"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1226
msgid "To make your logo retina ready, enter in the width of your uploaded image in the \"Logo Width (px)\" field and we'll take care of all the calculations for you. If you want your logo to stay the original size that was uploaded, leave the field blank."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1232, framework/legacy/functions/plugins/visual-composer.php:1368
msgid "Image"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1237
msgid "Image Width (px)"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1243
msgid "Use the following controls to vertically align your logo as desired. Make sure to adjust your top alignment even if your navbar is fixed to a side as it will reformat to the top on smaller screens (this control will be hidden if you do not have a side navigation position selected)."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1249, framework/legacy/functions/theme-options-controls.php:1341
msgid "Top Alignment"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1255, framework/legacy/functions/theme-options-controls.php:1347
msgid "Side Alignment"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1262
msgid "Alter the appearance of the top-level navbar links for your site here and their alignment and spacing in the section below."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1335
msgid "Customize the vertical alignment of your links for both top and side navbar positions as well as alter the vertical spacing between links for top navbar positions with the \"Link Spacing\" control."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1353
msgid "Top Spacing"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1361
msgid "Activate search functionality for the navbar. If activated, an icon will appear that when clicked will activate the search modal."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1372
msgid "Adjust the vertical alignment and size of the mobile button that appears on smaller screen sizes in your navbar."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1391, framework/legacy/functions/theme-options-controls.php:1414
msgid "Specify how many widget areas should appear in the collapsible Widgetbar and select the colors for its associated toggle."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1397, framework/legacy/functions/theme-options-controls.php:1458
msgid "Widget Areas"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1426
msgid "Topbar Content"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1433
msgid "Crumbs"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1447
msgid "Footers"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1465
msgid "Bottom Footer"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1472
msgid "Menu"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1507
msgid "Blog"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1514
msgid "Adjust the style and layout of your blog using the settings below. This will only affect the posts index page of your blog and will not alter any archives."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1540
msgid "Adjust the style and layout of your archive pages using the settings below."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1565
msgid "Selecting the \"Full Post on Index\" option below will allow the entire contents of your posts to be shown on the post index pages for all stacks. Deselecting this option will allow you to set the length of your excerpt."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1576
msgid "Full Post on Index"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1582
msgid "Excerpt Length"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1630
msgid "bbPress"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1636
msgid "This section handles all options regarding your bbPress setup. Select your content layout, section titles, along with plenty of other options to get bbPress up and running. The \"Layout\" option allows you to keep your sidebar if you have already selected \"Content Left, Sidebar Right\" or \"Sidebar Left, Content Right\" for your \"Content Layout\" option, or remove the sidebar completely if desired."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1637, framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:40
msgid "Templates"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1650
msgid "Topic/Reply Quicktags"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1656, framework/legacy/functions/theme-options-controls.php:1712
msgid "Navbar Menu"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1679
msgid "BuddyPress"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1685
msgid "This section handles all options regarding your BuddyPress setup. Select your content layout, section titles, along with plenty of other options to get BuddyPress up and running. The \"Layout\" option allows you to keep your sidebar if you have already selected \"Content Left, Sidebar Right\" or \"Sidebar Left, Content Right\" for your \"Content Layout\" option, or remove the sidebar completely if desired."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1691
msgid "Enabled"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1697
msgid "Templates Enabled"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1721
msgid "Set the titles for the various \"components\" in BuddyPress (e.g. groups list, registration, et cetera). Keep in mind that the \"Sites Title\" isn't utilized unless you have WordPress Multisite setup on your installation. Additionally, while they might not be present as actual titles on some pages, they are still used as labels in other areas such as the breadcrumbs, so keep this in mind when selecting inputs here."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1747, framework/legacy/functions/theme-options-controls.php:1779, framework/legacy/functions/plugins/buddypress.php:345, framework/legacy/functions/plugins/buddypress.php:396
msgid "Register"
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1758
msgid "Set the subtitles for the various \"components\" in BuddyPress (e.g. groups list, registration, et cetera). Keep in mind that the \"Sites Subtitle\" isn't utilized unless you have WordPress Multisite setup on your installation. Additionally, subtitles are not utilized across every Stack but are left here for ease of management."
msgstr ""

#: framework/legacy/functions/theme-options-controls.php:1809
msgid "Options"
msgstr ""

#: framework/legacy/templates/template-blank-1.php:3
msgid "Blank - Container | Header, Footer"
msgstr ""

#: framework/legacy/templates/template-blank-2.php:3
msgid "Blank - Container | Header, No Footer"
msgstr ""

#: framework/legacy/templates/template-blank-3.php:3
msgid "Blank - Container | No Header, No Footer"
msgstr ""

#: framework/legacy/templates/template-blank-4.php:3
msgid "Blank - No Container | Header, Footer"
msgstr ""

#: framework/legacy/templates/template-blank-5.php:3
msgid "Blank - No Container | Header, No Footer"
msgstr ""

#: framework/legacy/templates/template-blank-6.php:3
msgid "Blank - No Container | No Header, No Footer"
msgstr ""

#: framework/legacy/templates/template-blank-7.php:3
msgid "Blank - Container | No Header, Footer"
msgstr ""

#: framework/legacy/templates/template-blank-8.php:3
msgid "Blank - No Container | No Header, Footer"
msgstr ""

#: framework/legacy/templates/template-layout-content-sidebar.php:3
msgid "Layout - Content Left, Sidebar Right"
msgstr ""

#: framework/legacy/templates/template-layout-full-width.php:3
msgid "Layout - Fullwidth"
msgstr ""

#: framework/legacy/templates/template-layout-portfolio.php:3
msgid "Layout - Portfolio"
msgstr ""

#: framework/legacy/templates/template-layout-sidebar-content.php:3
msgid "Layout - Sidebar Left, Content Right"
msgstr ""

#: framework/views/custom/wp-comments.php:28, framework/views/renew/wp-comments.php:28
msgid "One Comment on %2$s"
msgid_plural "%1$s Comments on %2$s"
msgstr[0] ""
msgstr[1] ""

#: framework/views/custom/wp-comments.php:46, framework/views/ethos/wp-comments.php:41, framework/views/icon/wp-comments.php:38, framework/views/integrity/wp-comments.php:38, framework/views/renew/wp-comments.php:46
msgid "Comment navigation"
msgstr ""

#: framework/views/custom/wp-comments.php:47, framework/views/ethos/wp-comments.php:42, framework/views/icon/wp-comments.php:39, framework/views/integrity/wp-comments.php:39, framework/views/renew/wp-comments.php:47
msgid "&larr; Older Comments"
msgstr ""

#: framework/views/custom/wp-comments.php:48, framework/views/ethos/wp-comments.php:43, framework/views/icon/wp-comments.php:40, framework/views/integrity/wp-comments.php:40, framework/views/renew/wp-comments.php:48
msgid "Newer Comments &rarr;"
msgstr ""

#: framework/views/custom/wp-comments.php:53, framework/views/ethos/wp-comments.php:48, framework/views/icon/wp-comments.php:45, framework/views/integrity/wp-comments.php:45, framework/views/renew/wp-comments.php:53
msgid "Comments are closed."
msgstr ""

#: framework/views/custom/wp-comments.php:62, framework/views/ethos/wp-comments.php:58, framework/views/icon/wp-comments.php:54, framework/views/integrity/wp-comments.php:54, framework/views/renew/wp-comments.php:62
msgid "Submit"
msgstr ""

#: framework/views/ethos/content-link.php:31, framework/views/icon/content-link.php:25, framework/views/icon/content-link.php:20, framework/views/integrity/content-link.php:27, framework/views/integrity/content-link.php:22, framework/views/renew/content-link.php:29, framework/views/renew/content-link.php:22
msgid "Shared link from post: \"%s\""
msgstr ""

#: framework/views/ethos/wp-comments.php:29, framework/views/icon/wp-comments.php:26, framework/views/integrity/wp-comments.php:26, framework/legacy/templates/buddypress/activity/index.php:84, framework/legacy/templates/buddypress/members/single/activity.php:28
msgid "Comments"
msgstr ""

#: framework/views/ethos/wp-comments.php:59
msgid "Leave a Comment</span>"
msgstr ""

#: framework/views/ethos/wp-comments.php:61
msgctxt "noun"
msgid "Comment"
msgstr ""

#: framework/views/ethos/wp-comments.php:62
msgctxt "noun"
msgid "Your Comment *"
msgstr ""

#: framework/views/ethos/_content-post-header.php:17, framework/views/icon/content-link.php:26, framework/views/icon/content-page.php:25, framework/views/icon/_content-post-header.php:20, framework/views/integrity/content-image.php:25, framework/views/integrity/content-link.php:26, framework/views/integrity/content-page.php:29, framework/views/integrity/content-portfolio.php:36, framework/views/integrity/content-quote.php:28, framework/views/integrity/_content-post-header.php:16, framework/views/renew/content-link.php:28, framework/views/renew/content-page.php:21, framework/views/renew/content-portfolio.php:21, framework/views/renew/content-quote.php:27, framework/views/renew/_content-post-header.php:20, framework/legacy/functions/frontend/featured.php:57
msgid "Permalink to: \"%s\""
msgstr ""

#: framework/views/ethos/_index-featured.php:22, framework/views/ethos/_index-featured.php:20, framework/views/ethos/_post-slider.php:84, framework/legacy/functions/frontend/ethos.php:245, framework/legacy/functions/frontend/ethos.php:243
msgid "View Post"
msgstr ""

#: framework/views/ethos/_index.php:19
msgid "Filter by Topic"
msgstr ""

#: framework/views/ethos/_index.php:24
msgid "All"
msgstr ""

#: framework/views/ethos/_index.php:56
msgid "See All %s Posts"
msgstr ""

#: framework/views/global/_content-404.php:11
msgid "The page you are looking for is no longer here, or never existed in the first place (bummer). You can try searching for what you are looking for using the form below. If that still doesn't provide the results you are looking for, you can always start over from the home page."
msgstr ""

#: framework/views/global/_content-none.php:14
msgid "Nothing to Show Right Now"
msgstr ""

#: framework/views/global/_content-none.php:17
msgid "It appears whatever you were looking for is no longer here or perhaps wasn't here to begin with. You might want to try starting over from the homepage to see if you can find what you're after from there."
msgstr ""

#: framework/views/renew/_content-post-footer.php:13
msgid "Tags:"
msgstr ""

#: framework/functions/pro/i18n/theme-overview.php:4
msgid "This site is ​<strong>not validated</strong>​. <a href=\"%s\">Fix</a>"
msgstr ""

#: framework/functions/pro/i18n/theme-overview.php:5
msgid "Your license is <strong class=\"tco-c-nope\">not validated</strong>. Place your Themeco license to unlock automatic updates, access to support, and Extensions. <a href=\"https://theme.co/docs/product-validation/\" target=\"_blank\">Learn more</a> about product validation or <a href=\"https://theme.co/account/dashboard/\" target=\"_blank\">manage licenses</a> directly in your Themeco account."
msgstr ""

#: framework/functions/pro/i18n/theme-overview.php:6
msgid "By revoking validation, you will no longer receive automatic updates or the ability to install Extensions. The site will still be linked in your Themeco account, so you can re-validate at anytime.<br/><br/> Visit \"Licenses\" in your Themeco account to transfer a license to another site."
msgstr ""

#: framework/functions/pro/i18n/theme-overview.php:7
msgid "A separate license is required for each site."
msgstr ""

#: framework/functions/pro/i18n/theme-overview.php:8
msgid "Enter your license into the validation <a %s href=\"#\">field above</a>. If you do not have a license or need to get another, you can <a href=\"https://theme.co/go/join-validation.php\" target=\"_blank\">purchase</a> one."
msgstr ""

#: framework/functions/x/i18n/theme-overview.php:4
msgid "This license is ​<strong>not validated</strong>​. <a href=\"%s\">Fix</a>"
msgstr ""

#: framework/functions/x/i18n/theme-overview.php:5
msgid "Your license is <strong class=\"tco-c-nope\">not validated</strong>. Place your Envato purchase code or Themeco license to unlock automatic updates, access to support, and Extensions. <a href=\"https://theme.co/docs/product-validation/\" target=\"_blank\">Learn more</a> about product validation or <a href=\"https://theme.co/account/dashboard/\" target=\"_blank\">manage licenses</a> directly in your Themeco account. <a href=\"https://theme.co/pricing\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to Pro</a>."
msgstr ""

#: framework/functions/x/i18n/theme-overview.php:6
msgid "By revoking validation, you will no longer receive automatic updates, access to demo content, or the ability to install Extensions. The site will still be linked in your Themeco account, so you can re-validate at anytime.<br/><br/> Visit \"Licenses\" in your Themeco account to transfer a license to another site."
msgstr ""

#: framework/functions/x/i18n/theme-overview.php:7
msgid "A separate license is required for each site. Each purchase includes a Cornerstone license."
msgstr ""

#: framework/functions/x/i18n/theme-overview.php:8
msgid "If you have purchased X from ThemeForest already, you can find your purchase code <a href=\"https://theme.co/app/uploads/images/find-item-purchase-code.png\" target=\"_blank\">here</a>. If you do not have a license or need to get another, you can <a href=\"https://theme.co/go/join.php\" target=\"_blank\">purchase</a> one.<br><br>Once you have a purchase code you can <a %s href=\"#\">enter</a> it in the input at the top of this page. <a href=\"https://theme.co/pricing\" target=\"_blank\" rel=\"noopener noreferrer\">Upgrade to Pro</a>."
msgstr ""

#: framework/functions/x/validation/class-validation-cornerstone.php:50
msgid "You&apos;re almost ready to start using X. Please <a href=\"%s\">click here to install and activate</a> the required <strong>Cornerstone</strong> plugin. "
msgstr ""

#: framework/functions/x/validation/class-validation-cornerstone.php:63
msgid "You&apos;re almost ready to start using X. Please <a href=\"%s\">click here to activate</a> the required <strong>Cornerstone</strong> plugin. "
msgstr ""

#: framework/functions/x/validation/class-validation-cornerstone.php:73
msgid "Cornerstone activated!"
msgstr ""

#: framework/functions/x/validation/class-validation-cornerstone.php:88
msgid "Cornerstone successfully installed!"
msgstr ""

#: framework/functions/x/validation/class-validation-cornerstone.php:83
msgid "Unable to install Cornerstone. %s"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:114
msgid "Yes, Proceed"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:115
msgid "No, Take me back"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:116
msgid "Modern browser required to use importer."
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:117
msgid "Importing&hellip;"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:118
msgid "All set! Settings imported."
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:119
msgid "The uploaded file was not a valid XCS export."
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:120
msgid "This will overwrite your Theme Options and is not reversible unless you have previously made a backup of your settings. Are you sure you want to proceed?"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:121
msgid "Downloading XCS file&hellip;"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:122
msgid "Resetting&hellip;"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:123
msgid "Theme Options settings successfully reset!"
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:124
msgid "Unable to reset Theme Options."
msgstr ""

#: framework/legacy/functions/admin/class-validation-theme-options-manager.php:125
msgid "This will reset your Theme Options and is not reversible unless you have previously made a backup of your settings. Are you sure you want to proceed?"
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:88
msgid "Nothing to report."
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:89
msgid "New version available!"
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:90
msgid "Unable to check for updates. Try again later."
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:91
msgid "Checking&hellip;"
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:197
msgid "API Key Successfuly Validated!"
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:216, framework/legacy/functions/admin/class-validation-updates.php:220
msgid "<a href=\"%s\">Validate %s to enable automatic updates</a>"
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:224
msgid "%s is not validated. <a href=\"%s\">Validate %s to enable automatic updates</a>"
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:228
msgid "<a href=\"%s\">Validate %s to enable automatic updates</a>."
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:236
msgid "%s is not validated. <a href=\"%s\">Validate %s to enable automatic updates.</a>"
msgstr ""

#: framework/legacy/functions/admin/class-validation-updates.php:253
msgid "Could not retrieve extensions list. For assistance, please start by reviewing our article on troubleshooting <a href=\"https://theme.co/docs/problems-with-product-validation/\">connection issues.</a>"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:39, framework/legacy/templates/buddypress/groups/create.php:271
msgid "Back"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:40
msgid "Yep"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:41
msgid "Nope"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:76
msgid "Verifying license&hellip;"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:77
msgid "<strong>Uh oh</strong>, we couldn&apos;t check if this license was valid. <a data-tco-error-details href=\"#\">Details.</a>"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:79
msgid "<strong>Congratulations!</strong> Your site is validated. Addons are now unlocked."
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:88
msgid "Yes, revoke validation"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:89
msgid "Stay validated"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:90
msgid "Revoking&hellip;"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:92
msgid "<strong>Validation revoked.</strong> You can re-assign licenses from <a href=\"%s\" target=\"_blank\">Manage Licenses</a>."
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:138
msgid "We've checked your code, but it <strong>doesn't appear to be a valid license</strong>. Please double check the code and try again or <a href='https://theme.co/pricing' target='_blank' rel='noreferrer noopener'>purchase a new license</a>."
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:147
msgid "This looks like a <strong>brand new license that hasn&apos;t been added to a Themeco account yet.</strong> Login to your existing account or register a new one to continue."
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:148
msgid "Login or Register"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:156
msgid "Your code is valid, but <strong>we couldn&apos;t automatically link it to your site.</strong> You can add this site from within your Themeco account."
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:157, framework/legacy/functions/admin/class-validation.php:168, framework/legacy/functions/admin/markup/page-home.php:91
msgid "Manage Licenses"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:167
msgid "Your code is valid but looks like it has <strong>already been used on another site.</strong> You can revoke and re-assign within your Themeco account."
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:177
msgid "<strong>Congratulations,</strong> your site is now validated!"
msgstr ""

#: framework/legacy/functions/admin/class-validation.php:219
msgid "Setup Now"
msgstr ""

#: framework/legacy/functions/admin/customizer.php:38
msgid "Customize %s in Theme Options"
msgstr ""

#: framework/legacy/functions/admin/meta-boxes.php:117
msgid "Browse"
msgstr ""

#: framework/legacy/functions/admin/meta-boxes.php:135
msgid "Select Background Image(s)"
msgstr ""

#: framework/legacy/functions/admin/meta-boxes.php:172
msgid "Insert Media"
msgstr ""

#: framework/legacy/functions/admin/meta-boxes.php:222
msgid "Default"
msgstr ""

#: framework/legacy/functions/admin/meta-boxes.php:239
msgid "All Categories"
msgstr ""

#: framework/legacy/functions/admin/meta-boxes.php:267, framework/legacy/functions/admin/meta-boxes.php:285
msgid "Deactivated"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:25
msgid "Page Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:26
msgid "Here you will find various options you can use to create different page layouts and styles."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:46
msgid "Disable Page Title"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:47
msgid "Select to disable the page title. Disabling the page title provides greater stylistic flexibility on individual pages."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:53
msgid "One Page Navigation"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:54
msgid "To activate your one page navigation, select a menu from the dropdown. To deactivate one page navigation, set the dropdown back to \"Deactivated.\""
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:92
msgid "Icon Page Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:93
msgid "Here you will find some options specific to Icon that you can use to create different page layouts."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:99
msgid "Blank Template Sidebar"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:100
msgid "Because of Icon's unique layout, there may be times where you wish to show a sidebar on your blank templates. If that is the case, select \"Yes\" to activate your sidebar."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:104, framework/legacy/templates/buddypress/members/register.php:149
msgid "No"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:104, framework/legacy/templates/buddypress/members/register.php:148
msgid "Yes"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:120
msgid "Slider Settings: Above Masthead"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:121
msgid "Select your options to display a slider above the masthead."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:127, framework/legacy/functions/admin/meta-entries.php:188, framework/legacy/functions/plugins/visual-composer.php:2346
msgid "Slider"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:128, framework/legacy/functions/admin/meta-entries.php:189
msgid "To activate your slider, select an option from the dropdown. To deactivate your slider, set the dropdown back to \"Deactivated.\""
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:134, framework/legacy/functions/admin/meta-entries.php:195
msgid "Optional Background Video"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:135, framework/legacy/functions/admin/meta-entries.php:196
msgid "Include your video URL(s) here. If using multiple sources, separate them using the pipe character (|) and place fallbacks towards the end (i.e. .webm then .mp4 then .ogv)."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:141, framework/legacy/functions/admin/meta-entries.php:202
msgid "Video Poster Image (For Mobile)"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:142, framework/legacy/functions/admin/meta-entries.php:203
msgid "Click the button to upload your video poster image to show on mobile devices, or enter it in manually using the text field above. Only select one image for this field. To clear, delete the image URL from the text field and save your page."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:148, framework/legacy/functions/admin/meta-entries.php:209
msgid "Enable Scroll Bottom Anchor"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:149, framework/legacy/functions/admin/meta-entries.php:210
msgid "Select to enable the scroll bottom anchor for your slider."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:155, framework/legacy/functions/admin/meta-entries.php:216
msgid "Scroll Bottom Anchor Alignment"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:156, framework/legacy/functions/admin/meta-entries.php:217
msgid "Select the alignment of the scroll bottom anchor for your slider."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:163, framework/legacy/functions/admin/meta-entries.php:224
msgid "Scroll Bottom Anchor Color"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:164, framework/legacy/functions/admin/meta-entries.php:225
msgid "Select the color of the scroll bottom anchor for your slider."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:170, framework/legacy/functions/admin/meta-entries.php:231
msgid "Scroll Bottom Anchor Color Hover"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:171, framework/legacy/functions/admin/meta-entries.php:232
msgid "Select the hover color of the scroll bottom anchor for your slider."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:181
msgid "Slider Settings: Below Masthead"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:182
msgid "Select your options to display a slider below the masthead."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:249
msgid "Portfolio Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:250
msgid "Here you will find various options you can use to setup your portfolio."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:256
msgid "Category Select"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:257
msgid "To select multiple nonconsecutive pages or posts, hold down \"CTRL\" (Windows) or \"COMMAND\" (Mac), and then click each item you want to select. To cancel the selection of individual items, hold down \"CTRL\" or \"COMMAND\", and then click the items that you don't want to include."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:264
msgid "Select how many columns you would like to display for your portfolio."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:272
msgid "Select the layout you would like to display for your portfolio. The \"Use Global Content Layout\" option allows you to keep your sidebar if you have already selected \"Content Left, Sidebar Right\" or \"Sidebar Left, Content Right\" for your \"Content Layout\" in the Customizer."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:276
msgid "Use Global Content Layout"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:280
msgid "Select how many posts you would like to display per page for your portfolio."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:286
msgid "Disable Filtering"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:287
msgid "Turning off the portfolio filters will remove the ability to sort portfolio items by category."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:301
msgid "Post Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:302
msgid "Here you will find various options you can use to create different page styles."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:315
msgid "Fullwidth Post Layout"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:316
msgid "If your global content layout includes a sidebar, selecting this option will remove the sidebar for this post."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:359
msgid "Quote Post Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:360, framework/legacy/functions/admin/meta-entries.php:367
msgid "Input your quote."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:366
msgid "The Quote"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:373
msgid "Citation"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:374
msgid "Specify who originally said the quote."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:389
msgid "Link Post Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:390
msgid "Input your link."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:396
msgid "The Link"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:397
msgid "Insert your link URL, e.g. http://www.themeforest.net."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:412
msgid "Video Post Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:413
msgid "These settings enable you to embed videos into your posts."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:419, framework/legacy/functions/plugins/visual-composer.php:1731, framework/legacy/functions/plugins/visual-composer.php:1861
msgid "Aspect Ratio"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:420
msgid "Select the aspect ratio for your video."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:428
msgid "The URL to the .m4v video file."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:435
msgid "The URL to the .ogv video file."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:457
msgid "Audio Post Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:458
msgid "These settings enable you to embed audio into your posts."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:464
msgid "MP3 File URL"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:465
msgid "The URL to the .mp3 audio file."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:471
msgid "OGA File URL"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:472
msgid "The URL to the .oga or .ogg audio file."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:478
msgid "Embedded Audio Code"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:479
msgid "If you are using something other than self hosted audio such as Soundcloud paste the embed code here. This field will override the above."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:496
msgid "Ethos Post Settings"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:497
msgid "Here you will find some options specific to Ethos that you can use to create different post layouts."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:503
msgid "Index Featured Post Layout"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:504
msgid "Make the featured image of this post fullwidth on the blog index page."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:510
msgid "Index Featured Post Size"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:511
msgid "If the \"Index Featured Post Layout\" option above is selected, select a size for the output."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:518
msgid "Post Carousel Display"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:519
msgid "Display this post in the Post Carousel if you have \"Featured\" selected in the Customizer."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:525
msgid "Post Slider Display &ndash; Blog"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:526
msgid "Display this post in the Blog Post Slider if you have \"Featured\" selected in the Customizer."
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:532
msgid "Post Slider Display &ndash; Archives"
msgstr ""

#: framework/legacy/functions/admin/meta-entries.php:533
msgid "Display this post in the Archives Post Slider if you have \"Featured\" selected in the Customizer."
msgstr ""

#: framework/legacy/functions/admin/setup.php:146
msgid "Thumbnail"
msgstr ""

#: framework/legacy/functions/admin/taxonomies.php:32, framework/legacy/functions/admin/taxonomies.php:72
msgid "Archive Title"
msgstr ""

#: framework/legacy/functions/admin/taxonomies.php:34, framework/legacy/functions/admin/taxonomies.php:76
msgid "Enter in a value to overwrite the default title of the archive page."
msgstr ""

#: framework/legacy/functions/admin/taxonomies.php:37, framework/legacy/functions/admin/taxonomies.php:81
msgid "Archive Subtitle"
msgstr ""

#: framework/legacy/functions/admin/taxonomies.php:39, framework/legacy/functions/admin/taxonomies.php:85
msgid "Enter in a value to overwrite the default subtitle of the archive page."
msgstr ""

#: framework/legacy/functions/admin/taxonomies.php:42, framework/legacy/functions/admin/taxonomies.php:90, framework/legacy/functions/plugins/visual-composer.php:3762
msgid "Accent"
msgstr ""

#: framework/legacy/functions/admin/taxonomies.php:44, framework/legacy/functions/admin/taxonomies.php:94
msgid "Choose an accent color to be used for certain design elements."
msgstr ""

#: framework/legacy/functions/frontend/breadcrumbs.php:41
msgid "Home"
msgstr ""

#: framework/legacy/functions/frontend/content.php:111
msgid "Pages:"
msgstr ""

#: framework/legacy/functions/frontend/content.php:147
msgid "Previous Post"
msgstr ""

#: framework/legacy/functions/frontend/content.php:153
msgid "Next Post"
msgstr ""

#: framework/legacy/functions/frontend/content.php:235
msgid "Toggle the Widgetbar"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:34
msgid "by %s"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:82, framework/legacy/functions/frontend/ethos.php:60, framework/legacy/functions/frontend/integrity.php:76, framework/legacy/functions/frontend/integrity.php:55, framework/legacy/functions/frontend/renew.php:76, framework/legacy/functions/frontend/renew.php:55
msgid "View all posts in: &ldquo;%s&rdquo;"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:106, framework/legacy/functions/frontend/integrity.php:99, framework/legacy/functions/frontend/renew.php:100
msgid "Leave a Comment"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:106, framework/legacy/functions/frontend/integrity.php:99, framework/legacy/functions/frontend/renew.php:100
msgid "%s Comment"
msgid_plural "%s Comments"
msgstr[0] ""
msgstr[1] ""

#: framework/legacy/functions/frontend/ethos.php:110, framework/legacy/functions/frontend/icon.php:156, framework/legacy/functions/frontend/integrity.php:103, framework/legacy/functions/frontend/renew.php:105
msgid "Leave a comment on: &ldquo;%s&rdquo;"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:208
msgid "See All Posts"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:359, framework/legacy/functions/frontend/icon.php:87
msgid "%1$s"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:414, framework/legacy/functions/frontend/icon.php:200
msgid "Name"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:415, framework/legacy/functions/frontend/icon.php:202
msgid "Your Name"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:420, framework/legacy/functions/frontend/icon.php:208
msgid "Your Email"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:424, framework/legacy/functions/frontend/icon.php:212
msgid "Website"
msgstr ""

#: framework/legacy/functions/frontend/ethos.php:425, framework/legacy/functions/frontend/icon.php:214
msgid "Your Website"
msgstr ""

#: framework/legacy/functions/frontend/icon.php:97, framework/legacy/functions/frontend/integrity.php:184
msgid "Post<br>Author"
msgstr ""

#: framework/legacy/functions/frontend/icon.php:120
msgid "%s Edit"
msgstr ""

#: framework/legacy/functions/frontend/pagination.php:27
msgid "First Page"
msgstr ""

#: framework/legacy/functions/frontend/pagination.php:28
msgid "Last Page"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:83
msgid "To Forums List"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:112
msgid "Topic Tags"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:226
msgid "To Parent Forum"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:424
msgid "Forums Search"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:427
msgid "Favorites"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:428
msgid "Subscriptions"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:436, framework/legacy/functions/plugins/buddypress.php:471
msgid "Profile"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:434, framework/legacy/functions/plugins/buddypress.php:340, framework/legacy/functions/plugins/buddypress.php:394, framework/legacy/functions/plugins/buddypress.php:469
msgid "Log in"
msgstr ""

#: framework/legacy/functions/plugins/bbpress.php:443
msgid "Forums"
msgstr ""

#: framework/legacy/functions/plugins/buddypress.php:311
msgid "No Posts Yet"
msgstr ""

#: framework/legacy/functions/plugins/buddypress.php:309
msgid "Latest Post"
msgstr ""

#: framework/legacy/functions/plugins/buddypress.php:365
msgid "Edit Settings"
msgstr ""

#: framework/legacy/functions/plugins/buddypress.php:415
msgid "Your Activity"
msgstr ""

#: framework/legacy/functions/plugins/buddypress.php:472
msgid "Log out"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:64
msgid "Legacy X Integration"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:76
msgid "Activate legacy X integration. Keep this enabled if you built your site with Visual Composer and X shortcodes. This allows the theme to overwrite the column and row shortcodes. Turning this off will allow the plugin to operate natively without any changes from the theme."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:324
msgid "Line"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:328, framework/legacy/functions/plugins/visual-composer.php:371, framework/legacy/functions/plugins/visual-composer.php:423, framework/legacy/functions/plugins/visual-composer.php:3810, framework/legacy/functions/plugins/visual-composer.php:4245, framework/legacy/functions/plugins/visual-composer.php:4422
msgid "Structure"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:329
msgid "Place a horizontal rule in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:334, framework/legacy/functions/plugins/visual-composer.php:386, framework/legacy/functions/plugins/visual-composer.php:429, framework/legacy/functions/plugins/visual-composer.php:502, framework/legacy/functions/plugins/visual-composer.php:574, framework/legacy/functions/plugins/visual-composer.php:659, framework/legacy/functions/plugins/visual-composer.php:721, framework/legacy/functions/plugins/visual-composer.php:947, framework/legacy/functions/plugins/visual-composer.php:999, framework/legacy/functions/plugins/visual-composer.php:1222, framework/legacy/functions/plugins/visual-composer.php:1281, framework/legacy/functions/plugins/visual-composer.php:1335, framework/legacy/functions/plugins/visual-composer.php:1529, framework/legacy/functions/plugins/visual-composer.php:1575, framework/legacy/functions/plugins/visual-composer.php:1637, framework/legacy/functions/plugins/visual-composer.php:1689, framework/legacy/functions/plugins/visual-composer.php:1810, framework/legacy/functions/plugins/visual-composer.php:1887, framework/legacy/functions/plugins/visual-composer.php:1933, framework/legacy/functions/plugins/visual-composer.php:2013, framework/legacy/functions/plugins/visual-composer.php:2086, framework/legacy/functions/plugins/visual-composer.php:2149, framework/legacy/functions/plugins/visual-composer.php:2195, framework/legacy/functions/plugins/visual-composer.php:2313, framework/legacy/functions/plugins/visual-composer.php:2439, framework/legacy/functions/plugins/visual-composer.php:2493, framework/legacy/functions/plugins/visual-composer.php:2545, framework/legacy/functions/plugins/visual-composer.php:2662, framework/legacy/functions/plugins/visual-composer.php:2731, framework/legacy/functions/plugins/visual-composer.php:2783, framework/legacy/functions/plugins/visual-composer.php:2844, framework/legacy/functions/plugins/visual-composer.php:2949, framework/legacy/functions/plugins/visual-composer.php:3065, framework/legacy/functions/plugins/visual-composer.php:3132, framework/legacy/functions/plugins/visual-composer.php:3280, framework/legacy/functions/plugins/visual-composer.php:3412, framework/legacy/functions/plugins/visual-composer.php:3524, framework/legacy/functions/plugins/visual-composer.php:3605, framework/legacy/functions/plugins/visual-composer.php:3666, framework/legacy/functions/plugins/visual-composer.php:3773, framework/legacy/functions/plugins/visual-composer.php:3819, framework/legacy/functions/plugins/visual-composer.php:3924, framework/legacy/functions/plugins/visual-composer.php:4019, framework/legacy/functions/plugins/visual-composer.php:4135, framework/legacy/functions/plugins/visual-composer.php:4627, framework/legacy/functions/plugins/visual-composer.php:4702
msgid "(Optional) Enter a unique ID."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:341, framework/legacy/functions/plugins/visual-composer.php:393, framework/legacy/functions/plugins/visual-composer.php:436, framework/legacy/functions/plugins/visual-composer.php:509, framework/legacy/functions/plugins/visual-composer.php:581, framework/legacy/functions/plugins/visual-composer.php:666, framework/legacy/functions/plugins/visual-composer.php:728, framework/legacy/functions/plugins/visual-composer.php:835, framework/legacy/functions/plugins/visual-composer.php:954, framework/legacy/functions/plugins/visual-composer.php:1006, framework/legacy/functions/plugins/visual-composer.php:1229, framework/legacy/functions/plugins/visual-composer.php:1288, framework/legacy/functions/plugins/visual-composer.php:1342, framework/legacy/functions/plugins/visual-composer.php:1536, framework/legacy/functions/plugins/visual-composer.php:1582, framework/legacy/functions/plugins/visual-composer.php:1644, framework/legacy/functions/plugins/visual-composer.php:1696, framework/legacy/functions/plugins/visual-composer.php:1817, framework/legacy/functions/plugins/visual-composer.php:1894, framework/legacy/functions/plugins/visual-composer.php:1940, framework/legacy/functions/plugins/visual-composer.php:2020, framework/legacy/functions/plugins/visual-composer.php:2093, framework/legacy/functions/plugins/visual-composer.php:2156, framework/legacy/functions/plugins/visual-composer.php:2202, framework/legacy/functions/plugins/visual-composer.php:2258, framework/legacy/functions/plugins/visual-composer.php:2320, framework/legacy/functions/plugins/visual-composer.php:2446, framework/legacy/functions/plugins/visual-composer.php:2500, framework/legacy/functions/plugins/visual-composer.php:2552, framework/legacy/functions/plugins/visual-composer.php:2669, framework/legacy/functions/plugins/visual-composer.php:2738, framework/legacy/functions/plugins/visual-composer.php:2790, framework/legacy/functions/plugins/visual-composer.php:2851, framework/legacy/functions/plugins/visual-composer.php:2956, framework/legacy/functions/plugins/visual-composer.php:3072, framework/legacy/functions/plugins/visual-composer.php:3139, framework/legacy/functions/plugins/visual-composer.php:3287, framework/legacy/functions/plugins/visual-composer.php:3419, framework/legacy/functions/plugins/visual-composer.php:3531, framework/legacy/functions/plugins/visual-composer.php:3612, framework/legacy/functions/plugins/visual-composer.php:3673, framework/legacy/functions/plugins/visual-composer.php:3780, framework/legacy/functions/plugins/visual-composer.php:3826, framework/legacy/functions/plugins/visual-composer.php:3931, framework/legacy/functions/plugins/visual-composer.php:4026, framework/legacy/functions/plugins/visual-composer.php:4142, framework/legacy/functions/plugins/visual-composer.php:4396, framework/legacy/functions/plugins/visual-composer.php:4560, framework/legacy/functions/plugins/visual-composer.php:4635, framework/legacy/functions/plugins/visual-composer.php:4710
msgid "Class"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:342, framework/legacy/functions/plugins/visual-composer.php:394, framework/legacy/functions/plugins/visual-composer.php:437, framework/legacy/functions/plugins/visual-composer.php:510, framework/legacy/functions/plugins/visual-composer.php:582, framework/legacy/functions/plugins/visual-composer.php:667, framework/legacy/functions/plugins/visual-composer.php:729, framework/legacy/functions/plugins/visual-composer.php:836, framework/legacy/functions/plugins/visual-composer.php:955, framework/legacy/functions/plugins/visual-composer.php:1007, framework/legacy/functions/plugins/visual-composer.php:1230, framework/legacy/functions/plugins/visual-composer.php:1289, framework/legacy/functions/plugins/visual-composer.php:1343, framework/legacy/functions/plugins/visual-composer.php:1537, framework/legacy/functions/plugins/visual-composer.php:1583, framework/legacy/functions/plugins/visual-composer.php:1645, framework/legacy/functions/plugins/visual-composer.php:1697, framework/legacy/functions/plugins/visual-composer.php:1818, framework/legacy/functions/plugins/visual-composer.php:1895, framework/legacy/functions/plugins/visual-composer.php:1941, framework/legacy/functions/plugins/visual-composer.php:2021, framework/legacy/functions/plugins/visual-composer.php:2094, framework/legacy/functions/plugins/visual-composer.php:2157, framework/legacy/functions/plugins/visual-composer.php:2203, framework/legacy/functions/plugins/visual-composer.php:2259, framework/legacy/functions/plugins/visual-composer.php:2321, framework/legacy/functions/plugins/visual-composer.php:2447, framework/legacy/functions/plugins/visual-composer.php:2501, framework/legacy/functions/plugins/visual-composer.php:2553, framework/legacy/functions/plugins/visual-composer.php:2670, framework/legacy/functions/plugins/visual-composer.php:2739, framework/legacy/functions/plugins/visual-composer.php:2791, framework/legacy/functions/plugins/visual-composer.php:2852, framework/legacy/functions/plugins/visual-composer.php:2957, framework/legacy/functions/plugins/visual-composer.php:3073, framework/legacy/functions/plugins/visual-composer.php:3140, framework/legacy/functions/plugins/visual-composer.php:3288, framework/legacy/functions/plugins/visual-composer.php:3420, framework/legacy/functions/plugins/visual-composer.php:3532, framework/legacy/functions/plugins/visual-composer.php:3613, framework/legacy/functions/plugins/visual-composer.php:3674, framework/legacy/functions/plugins/visual-composer.php:3781, framework/legacy/functions/plugins/visual-composer.php:3827, framework/legacy/functions/plugins/visual-composer.php:3932, framework/legacy/functions/plugins/visual-composer.php:4027, framework/legacy/functions/plugins/visual-composer.php:4143, framework/legacy/functions/plugins/visual-composer.php:4397, framework/legacy/functions/plugins/visual-composer.php:4561, framework/legacy/functions/plugins/visual-composer.php:4636, framework/legacy/functions/plugins/visual-composer.php:4711
msgid "(Optional) Enter a unique class name."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:350, framework/legacy/functions/plugins/visual-composer.php:402, framework/legacy/functions/plugins/visual-composer.php:445, framework/legacy/functions/plugins/visual-composer.php:518, framework/legacy/functions/plugins/visual-composer.php:590, framework/legacy/functions/plugins/visual-composer.php:675, framework/legacy/functions/plugins/visual-composer.php:737, framework/legacy/functions/plugins/visual-composer.php:844, framework/legacy/functions/plugins/visual-composer.php:963, framework/legacy/functions/plugins/visual-composer.php:1015, framework/legacy/functions/plugins/visual-composer.php:1238, framework/legacy/functions/plugins/visual-composer.php:1297, framework/legacy/functions/plugins/visual-composer.php:1351, framework/legacy/functions/plugins/visual-composer.php:1545, framework/legacy/functions/plugins/visual-composer.php:1591, framework/legacy/functions/plugins/visual-composer.php:1653, framework/legacy/functions/plugins/visual-composer.php:1705, framework/legacy/functions/plugins/visual-composer.php:1826, framework/legacy/functions/plugins/visual-composer.php:1903, framework/legacy/functions/plugins/visual-composer.php:1949, framework/legacy/functions/plugins/visual-composer.php:2029, framework/legacy/functions/plugins/visual-composer.php:2102, framework/legacy/functions/plugins/visual-composer.php:2165, framework/legacy/functions/plugins/visual-composer.php:2211, framework/legacy/functions/plugins/visual-composer.php:2267, framework/legacy/functions/plugins/visual-composer.php:2329, framework/legacy/functions/plugins/visual-composer.php:2455, framework/legacy/functions/plugins/visual-composer.php:2509, framework/legacy/functions/plugins/visual-composer.php:2561, framework/legacy/functions/plugins/visual-composer.php:2678, framework/legacy/functions/plugins/visual-composer.php:2747, framework/legacy/functions/plugins/visual-composer.php:2799, framework/legacy/functions/plugins/visual-composer.php:2860, framework/legacy/functions/plugins/visual-composer.php:2965, framework/legacy/functions/plugins/visual-composer.php:3081, framework/legacy/functions/plugins/visual-composer.php:3148, framework/legacy/functions/plugins/visual-composer.php:3296, framework/legacy/functions/plugins/visual-composer.php:3428, framework/legacy/functions/plugins/visual-composer.php:3540, framework/legacy/functions/plugins/visual-composer.php:3621, framework/legacy/functions/plugins/visual-composer.php:3682, framework/legacy/functions/plugins/visual-composer.php:3789, framework/legacy/functions/plugins/visual-composer.php:3835, framework/legacy/functions/plugins/visual-composer.php:3940, framework/legacy/functions/plugins/visual-composer.php:4035, framework/legacy/functions/plugins/visual-composer.php:4151, framework/legacy/functions/plugins/visual-composer.php:4406, framework/legacy/functions/plugins/visual-composer.php:4570, framework/legacy/functions/plugins/visual-composer.php:4645, framework/legacy/functions/plugins/visual-composer.php:4720
msgid "(Optional) Enter inline CSS."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:367
msgid "Gap"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:372
msgid "Insert a vertical gap in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:377
msgid "Enter in the size of your gap. Pixels, ems, and percentages are all valid units of measurement."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:419
msgid "Clear"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:424
msgid "Clear floated elements in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:462
msgid "Blockquote"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:467
msgid "Include a blockquote in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:472, framework/legacy/functions/plugins/visual-composer.php:545, framework/legacy/functions/plugins/visual-composer.php:617, framework/legacy/functions/plugins/visual-composer.php:990, framework/legacy/functions/plugins/visual-composer.php:1042, framework/legacy/functions/plugins/visual-composer.php:1326, framework/legacy/functions/plugins/visual-composer.php:1620, framework/legacy/functions/plugins/visual-composer.php:1680, framework/legacy/functions/plugins/visual-composer.php:1978, framework/legacy/functions/plugins/visual-composer.php:2240, framework/legacy/functions/plugins/visual-composer.php:2484, framework/legacy/functions/plugins/visual-composer.php:2536, framework/legacy/functions/plugins/visual-composer.php:2889, framework/legacy/functions/plugins/visual-composer.php:3108, framework/legacy/functions/plugins/visual-composer.php:3709, framework/legacy/functions/plugins/visual-composer.php:3862, framework/legacy/functions/plugins/visual-composer.php:4178
msgid "Enter your text."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:480, framework/legacy/functions/plugins/visual-composer.php:553
msgid "Cite"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:481, framework/legacy/functions/plugins/visual-composer.php:554
msgid "Cite the person you are quoting."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:489
msgid "Select the alignment of the blockquote."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:535
msgid "Pullquote"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:540
msgid "Include a pullquote in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:562
msgid "Select the alignment of the pullquote."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:607
msgid "Alert"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:612
msgid "Provide information to users with alerts"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:625, framework/legacy/functions/plugins/visual-composer.php:922
msgid "Heading"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:626
msgid "Enter the heading of your alert."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:633, framework/legacy/functions/plugins/visual-composer.php:1267, framework/legacy/functions/plugins/visual-composer.php:1628
msgid "Type"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:634
msgid "Select the alert style."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:648, framework/legacy/templates/buddypress/members/register.php:89, framework/legacy/templates/buddypress/members/single/profile/edit.php:43
msgid "Close"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:649
msgid "Select to display the close button."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:692
msgid "Map"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:696, framework/legacy/functions/plugins/visual-composer.php:758, framework/legacy/functions/plugins/visual-composer.php:865, framework/legacy/functions/plugins/visual-composer.php:1372, framework/legacy/functions/plugins/visual-composer.php:1726, framework/legacy/functions/plugins/visual-composer.php:1847, framework/legacy/functions/plugins/visual-composer.php:2350, framework/legacy/functions/plugins/visual-composer.php:2476, framework/legacy/functions/plugins/visual-composer.php:2699, framework/legacy/functions/plugins/visual-composer.php:2768, framework/legacy/functions/plugins/visual-composer.php:3169
msgid "Media"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:697
msgid "Embed a map from a third-party provider"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:701, framework/legacy/functions/plugins/visual-composer.php:1852, framework/legacy/functions/plugins/visual-composer.php:2773
msgid "Code (See Notes Below)"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:702, framework/legacy/functions/plugins/visual-composer.php:1853, framework/legacy/functions/plugins/visual-composer.php:2774
msgid "Switch to the \"text\" editor and do not place anything else here other than your &lsaquo;iframe&rsaquo; or &lsaquo;embed&rsaquo; code."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:710, framework/legacy/functions/plugins/visual-composer.php:825, framework/legacy/functions/plugins/visual-composer.php:1799, framework/legacy/functions/plugins/visual-composer.php:1876, framework/legacy/functions/plugins/visual-composer.php:2428
msgid "No Container"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:711, framework/legacy/functions/plugins/visual-composer.php:826
msgid "Select to remove the container around the map."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:754
msgid "Google Map"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:759
msgid "Embed a customizable Google map"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:766, framework/legacy/functions/plugins/visual-composer.php:872
msgid "Latitude"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:767
msgid "Enter in the center latitude of your map."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:774, framework/legacy/functions/plugins/visual-composer.php:880
msgid "Longitude"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:775
msgid "Enter in the center longitude of your map."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:782
msgid "Draggable"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:783
msgid "Select to allow your users to drag the map view."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:792
msgid "Zoom Level"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:793
msgid "Choose the initial zoom level of your map. This value should be between 1 and 18. 1 is fully zoomed out and 18 is right at street level."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:800
msgid "Zoom Control"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:801
msgid "Select to activate the zoom control for the map."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:811
msgid "Choose an optional height for your map. If no height is selected, a responsive, proportional unit will be used. Any type of unit is acceptable (e.g. 450px, 30em, 40%, et cetera)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:818
msgid "Custom Color"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:819
msgid "Choose an optional custom color for your map."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:861
msgid "Google Map Marker"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:866
msgid "Place a location marker on your Google map"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:873
msgid "Enter in the latitude of your marker."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:881
msgid "Enter in the longitude of your marker."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:888
msgid "Additional Information"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:889
msgid "Optional description text to appear in a popup when your marker is clicked on."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:896
msgid "Custom Marker Image"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:897
msgid "Utilize a custom marker image instead of the default provided by Google."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:913
msgid "Skill Bar"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:918
msgid "Include an informational skill bar"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:923
msgid "Enter the heading of your skill bar."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:930
msgid "Percent"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:931
msgid "Enter the percentage of your skill and be sure to include the percentage sign (i.e. 90%)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:938
msgid "Bar Text"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:939
msgid "Enter in some alternate text in place of the percentage inside the skill bar."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:980
msgid "Code"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:985
msgid "Add a block of example code to your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1032
msgid "Button"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1036, framework/legacy/functions/plugins/visual-composer.php:2820, framework/legacy/functions/plugins/visual-composer.php:2881, framework/legacy/functions/plugins/visual-composer.php:2986, framework/legacy/functions/plugins/visual-composer.php:3102, framework/legacy/functions/plugins/visual-composer.php:3334
msgid "Marketing"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1037
msgid "Add a clickable button to your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1051
msgid "Select the button shape."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1064
msgid "Select the button size."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1079, framework/legacy/functions/plugins/visual-composer.php:1391
msgid "Float"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1080
msgid "Optionally float the button."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1092
msgid "Block"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1093
msgid "Select to make your button go fullwidth."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1102, framework/legacy/functions/plugins/visual-composer.php:3036, framework/legacy/functions/plugins/visual-composer.php:3383
msgid "Marketing Circle"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1103, framework/legacy/functions/plugins/visual-composer.php:3037, framework/legacy/functions/plugins/visual-composer.php:3384
msgid "Select to include a marketing circle around your button."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1112
msgid "Icon Only"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1113
msgid "Select if you are only using an icon in your button."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1122, framework/legacy/functions/plugins/visual-composer.php:1429, framework/legacy/functions/plugins/visual-composer.php:3046, framework/legacy/functions/plugins/visual-composer.php:3393
msgid "Href"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1123
msgid "Enter in the URL you want your button to link to."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1131
msgid "Enter in the title attribute you want for your button (will also double as title for popover or tooltip if you have chosen to display one)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1138, framework/legacy/functions/plugins/visual-composer.php:1445, framework/legacy/functions/plugins/visual-composer.php:3054, framework/legacy/functions/plugins/visual-composer.php:3401
msgid "Target"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1139
msgid "Select to open your button link in a new window."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1148, framework/legacy/functions/plugins/visual-composer.php:1455
msgid "Info"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1149
msgid "Select whether or not you want to add a popover or tooltip to your button."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1161, framework/legacy/functions/plugins/visual-composer.php:1468
msgid "Info Placement"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1162, framework/legacy/functions/plugins/visual-composer.php:1469
msgid "Select where you want your popover or tooltip to appear."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1175, framework/legacy/functions/plugins/visual-composer.php:1482
msgid "Info Trigger"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1176, framework/legacy/functions/plugins/visual-composer.php:1483
msgid "Select what actions you want to trigger the popover or tooltip."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1188, framework/legacy/functions/plugins/visual-composer.php:1495
msgid "Info Content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1189, framework/legacy/functions/plugins/visual-composer.php:1496
msgid "Extra content for the popover."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1196, framework/legacy/functions/plugins/visual-composer.php:1503
msgid "Lightbox Thumbnail"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1197
msgid "Use this option to select a thumbnail for your lightbox thumbnail navigation or to set an image if you are linking out to a video."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1203, framework/legacy/functions/plugins/visual-composer.php:1510
msgid "Lightbox Video"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1204
msgid "Select if you are linking to a video from this button in the lightbox."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1213, framework/legacy/functions/plugins/visual-composer.php:1520
msgid "Lightbox Caption"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1214, framework/legacy/functions/plugins/visual-composer.php:1521
msgid "Lightbox caption text."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1255
msgid "Block Grid"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1260
msgid "Include a block grid container in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1268
msgid "Select how many block grid items you want per row."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1314
msgid "Block Grid Item"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1319
msgid "Include a block grid item in your block grid"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1373
msgid "Include an image in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1378
msgid "Select the image style."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1392
msgid "Optionally float the image."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1404
msgid "Src"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1405
msgid "Enter your image."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1411, framework/legacy/functions/plugins/visual-composer.php:3123
msgid "Alt"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1412
msgid "Enter in the alt text for your image."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1419
msgid "Link"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1420
msgid "Select to wrap your image in an anchor tag."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1430
msgid "Enter in the URL you want your image to link to. If using this image for a lightbox, enter the URL of your media here (e.g. YouTube embed URL, et cetera). Leave this field blank if you want to link to the image uploaded to the \"Src\" for your lightbox."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1438
msgid "Enter in the title attribute you want for your image (will also double as title for popover or tooltip if you have chosen to display one)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1446
msgid "Select to open your image link in a new window."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1456
msgid "Select whether or not you want to add a popover or tooltip to your image."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1504
msgid "Use this option to select a different thumbnail for your lightbox thumbnail navigation or to set an image if you are linking out to a video. Will default to the \"Src\" image if nothing is set."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1511
msgid "Select if you are linking to a video from this image in the lightbox."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1562
msgid "Icon List"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1567
msgid "Include an icon list in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1608
msgid "Icon List Item"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1613
msgid "Include an icon list item in your icon list"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1629
msgid "Select your icon."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1670
msgid "Columnize"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1675
msgid "Split your text into multiple columns"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1722
msgid "Video (Self Hosted)"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1727, framework/legacy/functions/plugins/visual-composer.php:1848
msgid "Include responsive video into your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1732, framework/legacy/functions/plugins/visual-composer.php:1862
msgid "Select your aspect ratio."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1746
msgid "M4V"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1747
msgid "Include and .m4v version of your video."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1754
msgid "OGV"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1755
msgid "Include and .ogv version of your video for additional native browser support."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1762
msgid "Poster Image"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1763
msgid "Include a poster image for your self-hosted video."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1769, framework/legacy/functions/plugins/visual-composer.php:2720
msgid "Advanced Controls"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1770
msgid "Select to enable advanced controls on your self-hosted video."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1779
msgid "Hide Controls"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1780
msgid "Select to hide the controls on your self-hosted video."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1789
msgid "Autoplay"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1790
msgid "Select to automatically play your self-hosted video."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1800, framework/legacy/functions/plugins/visual-composer.php:1877
msgid "Select to remove the container around the video."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1843
msgid "Video (Embedded)"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1920
msgid "Accordion"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1925
msgid "Include an accordion into your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1966
msgid "Accordion Item"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1971
msgid "Include an accordion item in your accordion"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1987
msgid "Optionally include an ID given to the parent accordion to only allow one toggle to be open at a time."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:1995
msgid "Include a title for your accordion item."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2002
msgid "Open"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2003
msgid "Select for your accordion item to be open by default."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2046
msgid "Tab Nav"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2051
msgid "Include a tab nav into your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2058
msgid "Tab Nav Items Per Row"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2059
msgid "If your tab nav is on top, select how many tab nav items you want per row."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2072
msgid "Tab Nav Position"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2073
msgid "Select the position of your tab nav."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2119
msgid "Tab Nav Item"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2124
msgid "Include a tab nav item into your tab nav"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2131
msgid "Include a title for your tab nav item."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2138, framework/legacy/functions/plugins/visual-composer.php:2248
msgid "Active"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2139
msgid "Select to make this tab nav item active."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2182
msgid "Tabs"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2187
msgid "Include a tabs container after your tab nav"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2228
msgid "Tab"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2233
msgid "Include a tab into your tabs container"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2249
msgid "Select to make this tab active."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2284, framework/legacy/templates/buddypress/members/single/settings/profile.php:15
msgid "Visibility"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2289
msgid "Alter content based on screen size"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2296
msgid "Visibility Type"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2297
msgid "Select how you want to hide or show your content."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2351
msgid "Include a slider in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2358
msgid "Animation"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2359
msgid "Select your slider animation."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2370
msgid "Slide Time"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2371
msgid "The amount of time a slide will stay visible in milliseconds."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2379
msgid "Slide Speed"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2380
msgid "The amount of time to transition between slides in milliseconds."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2388
msgid "Slideshow"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2389
msgid "Select for your slides to advance automatically."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2399
msgid "Select to randomly display your slides each time the page loads."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2408
msgid "Control Navigation"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2409
msgid "Select to display the control navigation."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2418
msgid "Previous/Next Navigation"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2419
msgid "Select to display the previous/next navigation."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2429
msgid "Select to remove the container from your slider."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2472
msgid "Slide"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2477
msgid "Include a slide into your slider"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2526
msgid "Protect"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2531
msgid "Protect content from non logged in users"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2578
msgid "Recent Posts"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2583
msgid "Display your most recent posts"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2588
msgid "Choose between standard posts or portfolio posts."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2599
msgid "Post Count"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2600
msgid "Select how many posts to display."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2613
msgid "Offset"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2614
msgid "Enter a number to offset initial starting post of your recent posts."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2621
msgid "Category"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2622
msgid "To filter your posts by category, enter in the slug of your desired category. To filter by multiple categories, enter in your slugs separated by a comma."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2629, framework/legacy/functions/plugins/visual-composer.php:3238
msgid "Orientation"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2630
msgid "Select the orientation or your recent posts."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2641
msgid "Remove Featured Image"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2642
msgid "Select to remove the featured image."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2651, framework/legacy/functions/plugins/visual-composer.php:4589, framework/legacy/functions/plugins/visual-composer.php:4664
msgid "Fade Effect"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2652, framework/legacy/functions/plugins/visual-composer.php:4590, framework/legacy/functions/plugins/visual-composer.php:4665
msgid "Select to activate the fade effect."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2695
msgid "Audio (Self Hosted)"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2700, framework/legacy/functions/plugins/visual-composer.php:2769
msgid "Place audio files into your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2704
msgid "MP3"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2705
msgid "Include and .mp3 version of your audio."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2712
msgid "OGA"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2713
msgid "Include and .oga version of your audio for additional native browser support."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2721
msgid "Select to enable advanced controls on your self-hosted audio."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2764
msgid "Audio (Embedded)"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2816
msgid "Pricing Table"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2821
msgid "Include a pricing table in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2829
msgid "Select how many columns you want for your pricing table."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2877
msgid "Pricing Table Column"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2882
msgid "Include a pricing table column"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2898
msgid "Include a title for your pricing column."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2906
msgid "Select to make this your featured offer."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2915
msgid "Featured Sub Heading"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2916
msgid "Include a sub heading for your featured column."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2923
msgid "Currency Symbol"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2924
msgid "Enter in the currency symbol you want to use."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2931
msgid "Price"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2932
msgid "Enter in the price for this pricing column."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2939
msgid "Interval"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2940
msgid "Enter in the time period that this pricing column is for."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2982
msgid "Callout"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2987
msgid "Include a marketing callout into your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:2992
msgid "Select the alignment for your callout."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3005
msgid "Enter the title for your callout."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3012, framework/legacy/functions/plugins/visual-composer.php:3359, framework/legacy/templates/buddypress/members/single/messages/compose.php:20, framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:11
msgid "Message"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3013
msgid "Enter the message for your callout."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3020, framework/legacy/functions/plugins/visual-composer.php:3367
msgid "Button Text"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3021
msgid "Enter the text for your callout button."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3028, framework/legacy/functions/plugins/visual-composer.php:3375
msgid "Button Icon"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3029, framework/legacy/functions/plugins/visual-composer.php:3376
msgid "Optionally enter the button icon."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3047
msgid "Enter in the URL you want your callout button to link to."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3055
msgid "Select to open your callout link button in a new window."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3098
msgid "Promo"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3103
msgid "Include a marketing promo in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3116
msgid "Promo Image"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3117
msgid "Include an image for your promo element."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3124
msgid "Enter in the alt text for your promo image."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3165
msgid "Responsive Lightbox"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3170
msgid "Display images in a responsive lightbox"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3174, framework/legacy/functions/plugins/visual-composer.php:3966
msgid "Selector"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3175
msgid "Enter in the selector for your images (e.g. if your class is \"lightbox-img\" enter \".lightbox-img\"). Set to \".x-img-link\" to automatically setup a lightbox for all linked [image] shortcodes on your page."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3183
msgid "Deeplink"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3184
msgid "Select to activate deeplinking (creates unique link for each image)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3193
msgid "Backdrop Opacity"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3194
msgid "Enter in the opacity for the backdrop (valid inputs are numbers 0 to 1)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3202
msgid "Previous Item Scale"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3203
msgid "Enter in the scale for the previous item (valid inputs are numbers 0 to 1)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3211
msgid "Previous Item Opacity"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3212
msgid "Enter in the opacity for the previous item (valid inputs are numbers 0 to 1)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3220
msgid "Next Item Scale"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3221
msgid "Enter in the scale for the next item (valid inputs are numbers 0 to 1)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3229
msgid "Next Item Opacity"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3230
msgid "Enter in the opacity for the next item (valid inputs are numbers 0 to 1)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3239
msgid "Select the orientation of your lightbox."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3250
msgid "Thumbnails"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3251
msgid "Select to activate thumbnail navigation."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3275
msgid "Include post author information"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3304
msgid "Enter in a title for your author information."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3312
msgid "Author ID"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3313
msgid "By default the author of the post or page will be output by leaving this input blank. If you would like to output the information of another author, enter in their user ID here."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3330
msgid "Prompt"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3335
msgid "Include a marketing prompt into your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3340
msgid "Select the alignment of your prompt."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3352
msgid "Enter the title for your prompt."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3360
msgid "Enter the message for your prompt."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3368
msgid "Enter the text for your prompt button."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3394
msgid "Enter in the URL you want your prompt button to link to."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3402
msgid "Select to open your prompt button link in a new window."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3445
msgid "Social Sharing"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3450
msgid "Include social sharing into your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3455
msgid "Enter in a title for your social links."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3464
msgid "Select to activate the Facebook sharing link."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3474
msgid "Select to activate the Twitter sharing link."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3484
msgid "Select to activate the LinkedIn sharing link."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3494
msgid "Select to activate the Pinterest sharing link."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3504
msgid "Select to activate the Reddit sharing link."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3514
msgid "Select to activate the email sharing link."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3557
msgid "Table of Contents"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3562
msgid "Include a table of contents in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3570
msgid "Set the title of the table of contents."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3579
msgid "Select the alignment of your table of contents."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3592
msgid "Select a column count for your links if you have chosen \"Fullwidth\" as your alignment."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3638
msgid "Table of Contents Item"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3643
msgid "Include a table of contents item"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3650
msgid "Set the title of the table of contents item."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3657
msgid "Page"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3658
msgid "Set the page of the table of contents item."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3699
msgid "Custom Headline"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3704
msgid "Include a custom headline in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3718
msgid "Select which way to align the custom headline."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3730, framework/legacy/functions/plugins/visual-composer.php:3883
msgid "Heading Level"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3731, framework/legacy/functions/plugins/visual-composer.php:3884
msgid "Select which level to use for your heading (e.g. h2)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3746, framework/legacy/functions/plugins/visual-composer.php:3899
msgid "Looks Like"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3747, framework/legacy/functions/plugins/visual-composer.php:3900
msgid "Select which level your heading should look like (e.g. h3)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3763
msgid "Select to activate the heading accent."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3806
msgid "Container"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3811
msgid "Include a container in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3852
msgid "Feature Headline"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3857
msgid "Include a feature headline in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3871
msgid "Select which way to align the feature headline."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3916
msgid "Select the icon to use with your feature headline."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3962
msgid "Include responsive text in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3967
msgid "Enter in the selector for your responsive text (e.g. if your class is \"h-responsive\" enter \".h-responsive\")."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3974
msgid "Compression"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3975
msgid "Enter the compression for your responsive text (adjust up and down to desired level in small increments)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3983
msgid "Minimum Size"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3984
msgid "Enter the minimum size of your responsive text."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3991
msgid "Maximum Size"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:3992
msgid "Enter the maximum size of your responsive text."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4014
msgid "Include a search field in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4052
msgid "Counter"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4057
msgid "Include an animated number counter in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4061
msgid "Starting Number"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4062
msgid "Enter in the number that you would like your counter to start from."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4070
msgid "Ending Number"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4071
msgid "Enter int he number that you would like your counter to end at. This must be higher than your starting number."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4079
msgid "Counter Speed"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4080
msgid "The amount of time to transition between numbers in milliseconds."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4088
msgid "Number Prefix"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4089
msgid "Prefix your number with a symbol or text."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4096
msgid "Number Suffix"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4097
msgid "Suffix your number with a symbol or text."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4104
msgid "Number Color"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4105
msgid "Select the color of your number."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4111
msgid "Text Above"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4112
msgid "Optionally include text above your number."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4119
msgid "Text Below"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4120
msgid "Optionally include text below your number."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4127
msgid "Text Color"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4128
msgid "Select the color of your text above and below the number if you have include any."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4173
msgid "Include a block of text in your content"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4241, framework/legacy/functions/plugins/visual-composer.php:4418
msgid "Content Band"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4246, framework/legacy/functions/plugins/visual-composer.php:4423
msgid "Place and structure your shortcodes inside of a row"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4273, framework/legacy/functions/plugins/visual-composer.php:4437
msgid "Select to insert a container inside of the content band. Use this instead of the [container] shortcode for greater flexibility and to contain multiple columns."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4283, framework/legacy/functions/plugins/visual-composer.php:4447
msgid "Remove Margin"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4284, framework/legacy/functions/plugins/visual-composer.php:4448
msgid "Select to remove the margin from the content band and stack them on top of each other."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4294, framework/legacy/functions/plugins/visual-composer.php:4458
msgid "Padding Top"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4295, framework/legacy/functions/plugins/visual-composer.php:4459
msgid "Set the top padding of the content band (leave blank to keep default)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4304, framework/legacy/functions/plugins/visual-composer.php:4468
msgid "Padding Bottom"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4305, framework/legacy/functions/plugins/visual-composer.php:4469
msgid "Set the bottom padding of the content band (leave blank to keep default)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4315, framework/legacy/functions/plugins/visual-composer.php:4479
msgid "Select whether or not to display a border on your content band."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4334, framework/legacy/functions/plugins/visual-composer.php:4498
msgid "Select the background color of your content band (leave blank for \"transparent\")."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4342, framework/legacy/functions/plugins/visual-composer.php:4506
msgid "Upload a background pattern to your content band."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4350, framework/legacy/functions/plugins/visual-composer.php:4514
msgid "Upload a background image to your content band (this will overwrite your Background Pattern)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4357, framework/legacy/functions/plugins/visual-composer.php:4521
msgid "Parallax"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4358, framework/legacy/functions/plugins/visual-composer.php:4522
msgid "Select to activate the parallax effect with background patterns and images."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4368, framework/legacy/functions/plugins/visual-composer.php:4532
msgid "Background Video"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4369, framework/legacy/functions/plugins/visual-composer.php:4533
msgid "Include the path to your background video (this will overwrite both your Background Pattern and Background Image)."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4377, framework/legacy/functions/plugins/visual-composer.php:4541
msgid "Background Video Poster"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4378, framework/legacy/functions/plugins/visual-composer.php:4542
msgid "Include a poster image for your background video on mobile devices."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4386, framework/legacy/functions/plugins/visual-composer.php:4550
msgid "Select to remove the spacing between columns."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4600, framework/legacy/functions/plugins/visual-composer.php:4675
msgid "Fade Animation"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4601, framework/legacy/functions/plugins/visual-composer.php:4676
msgid "Select the type of fade animation you want to use."
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4616, framework/legacy/functions/plugins/visual-composer.php:4691
msgid "Fade Animation Offset"
msgstr ""

#: framework/legacy/functions/plugins/visual-composer.php:4617, framework/legacy/functions/plugins/visual-composer.php:4692
msgid "Set how large you want the offset for your fade animation to be."
msgstr ""

#: framework/legacy/functions/plugins/woocommerce.php:492, framework/legacy/functions/plugins/woocommerce.php:529
msgid "%d Item"
msgid_plural "%d Items"
msgstr[0] ""
msgstr[1] ""

#: framework/legacy/functions/updates/class-plugin-updater.php:229
msgid "There is a new version of %1$s available. <a href=\"%2$s\" class=\"thickbox\" title=\"%3$s\">View version %4$s details</a> or <a href=\"%5$s\">update now</a>."
msgstr ""

#: framework/legacy/functions/updates/class-plugin-updater.php:227
msgid "There is a new version of %1$s available. <a href=\"%2$s\" class=\"thickbox\" title=\"%3$s\">View version %4$s details</a>. %5$s"
msgstr ""

#: framework/legacy/functions/updates/class-plugin-updater.php:225
msgid "There is a new version of %1$s available. <a href=\"%2$s\" class=\"thickbox\" title=\"%3$s\">View version %4$s details</a>."
msgstr ""

#: framework/legacy/functions/updates/class-x-tgmpa-integration.php:196
msgid "Bulk Extension Manager"
msgstr ""

#: framework/legacy/functions/updates/class-x-tgmpa-integration.php:204
msgid "<p>This screen will allow you to manage all Extensions and Approved plugins.</p>"
msgstr ""

#: framework/legacy/functions/updates/class-x-tgmpa-integration.php:210
msgid "Installing Plugin: %s"
msgstr ""

#: framework/legacy/functions/updates/class-x-tgmpa-integration.php:211
msgid "Updating Plugin: %s"
msgstr ""

#: framework/legacy/functions/updates/class-x-tgmpa-integration.php:212
msgid "Something went wrong with the plugin API."
msgstr ""

#: framework/legacy/functions/updates/class-x-tgmpa-integration.php:213
msgid "Return to Bulk Extension Manager"
msgstr ""

#: framework/legacy/templates/bbpress/content-search.php:37
msgid "Enter in your query in the search form above."
msgstr ""

#: framework/legacy/templates/bbpress/form-reply-move.php:29
msgid "<strong>WARNING:</strong> This process cannot be undone. You can either make this reply a new topic with a new title, or merge it into an existing topic. If you choose an existing topic, replies will be ordered by the time and date they were created."
msgstr ""

#: framework/legacy/templates/bbpress/form-topic-merge.php:29
msgid "<strong>WARNING:</strong> This process cannot be undone. Select the topic to merge this one into. The destination topic will remain the lead topic, and this one will change into a reply. To keep this topic as the lead, go to the other topic and use the merge tool from there instead. All replies within both topics will be merged chronologically. The order of the merged replies is based on the time and date they were posted. If the destination topic was created after this one, it's post date will be updated to second earlier than this one."
msgstr ""

#: framework/legacy/templates/bbpress/form-topic-split.php:29
msgid "<strong>WARNING:</strong> This process cannot be undone. When you split a topic, you are slicing it in half starting with the reply you just selected. Choose to use that reply as a new topic with a new title, or merge those replies into an existing topic. If you use the existing topic option, replies within both topics will be merged chronologically. The order of the merged replies is based on the time and date they were posted."
msgstr ""

#: framework/legacy/templates/bbpress/form-topic-tag.php:21
msgid "Leave the slug empty to have one automatically generated. Changing the slug affects its permalink. Any links to the old slug will stop working."
msgstr ""

#: framework/legacy/templates/bbpress/form-topic-tag.php:83
msgid "Deleting a tag cannot be undone. This does not delete your topics. Only the tag itself is deleted. Any links to this tag will no longer function."
msgstr ""

#: framework/legacy/templates/bbpress/form-user-edit.php:156
msgid "Update"
msgstr ""

#: framework/legacy/templates/bbpress/form-user-lost-pass.php:18
msgid "Enter in your username or email address below then select the &ldquo;Reset&rdquo; button."
msgstr ""

#: framework/legacy/templates/bbpress/form-user-lost-pass.php:30, framework/legacy/functions/admin/markup/page-home-box-theme-options-manager.php:51
msgid "Reset"
msgstr ""

#: framework/legacy/templates/bbpress/form-user-register.php:18
msgid "Your username must be unique, and cannot be changed later. We use your email address to email you a secure password and verify your account."
msgstr ""

#: framework/functions/pro/stacks/blank/blank.php:36
msgid "Blank"
msgstr ""

#: framework/functions/pro/stacks/starter/starter.php:23
msgid "Starter"
msgstr ""

#: framework/legacy/cranium/headers/functions/navbar.php:92, framework/legacy/cranium/footers/views/global/_nav-footer.php:21
msgid "Assign a Menu"
msgstr ""

#: framework/legacy/cranium/headers/functions/navbar.php:147
msgid "Type and Press &ldquo;enter&rdquo; to Search"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-approved-plugins.php:27
msgid "Approved Plugins"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-approved-plugins.php:31
msgid "Install and activate any <strong>Approved Plugin</strong> using the buttons below. You can also use the <a href=\"%s\">Bulk Extension Manager</a>."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-approved-plugins.php:58, framework/legacy/functions/admin/markup/page-home-box-extensions.php:61
msgid "by"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:25
msgid "Automatic Updates"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:63
msgid "Admin Notifications"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:64
msgid "Get updates in WordPress."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:70
msgid "Stay Up to Date"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:71
msgid "Use the latest features right away."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:77
msgid "Manual No More"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:78
msgid "Say goodbye to your FTP client."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:35
msgid "Installed Version"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:36, framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:43
msgid "Changelog"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:42
msgid "Latest Version Available"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:49
msgid "Checked Every 12 Hours"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-automatic-updates.php:50
msgid "Check Now"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:25
msgid "Design Cloud"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:34
msgid "Complete designs. Done for you."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:41
msgid "Individual assets to use anywhere."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:47
msgid "Automatic"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:48
msgid "Install with one click."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:55
msgid "Get Design Cloud"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-design-cloud.php:53
msgid "Launch Design Cloud"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-extensions.php:27, framework/legacy/functions/admin/markup/page-home-box-extensions.php:80
msgid "Extensions"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-extensions.php:77
msgid "Over $1,000 in Value!"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-extensions.php:78
msgid "20+ Extensions (plugins) created by Themeco and 3rd parties to work seamlessly with your site. Instantly download with each verified purchase. <strong>Oh, and they are all free!</strong>"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-extensions.php:33
msgid "Install and activate any <strong>Extension</strong> using the buttons below. You can also use the <a href=\"%s\">Bulk Extension Manager</a>."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:25, framework/legacy/functions/admin/markup/page-home-box-support.php:54
msgid "Support"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:33
msgid "Real People"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:34
msgid "A professional and courteous staff."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:40
msgid "Around the Clock"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:41
msgid "Get help at any time, day or night."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:47, framework/legacy/functions/admin/markup/page-home-box-support.php:54
msgid "Docs"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:48
msgid "Dozens of articles and videos."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-support.php:57
msgid "Get World-Class Support"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-theme-options-manager.php:25
msgid "Theme Options Manager"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-theme-options-manager.php:35
msgid "<strong>Choose an XCS file</strong> or drag it here to import."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-theme-options-manager.php:45
msgid "Export"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home-box-validation.php:49
msgid "Input Code and Hit Enter"
msgstr ""

#: framework/legacy/functions/admin/markup/page-home.php:95
msgid "Your site is validated. %s."
msgstr ""

#: framework/legacy/functions/admin/markup/page-home.php:95
msgid "Revoke validation"
msgstr ""

#. translators: 1: user profile link, 2: user name, 3: activity permalink, 4: activity timestamp
#: framework/legacy/templates/buddypress/activity/comment.php:30
msgid "<a href=\"%1$s\">%2$s</a> replied <a href=\"%3$s\" class=\"activity-time-since\"><span class=\"time-since\">%4$s</span></a>"
msgstr ""

#: framework/legacy/templates/buddypress/activity/comment.php:40, framework/legacy/templates/buddypress/members/single/messages/single.php:86
msgid "Reply"
msgstr ""

#: framework/legacy/templates/buddypress/activity/comment.php:46, framework/legacy/templates/buddypress/groups/single/admin.php:344, framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:45, framework/legacy/templates/buddypress/members/single/messages/single.php:24, framework/legacy/templates/buddypress/members/single/settings/delete-account.php:28
msgid "Delete"
msgstr ""

#: framework/legacy/templates/buddypress/activity/entry.php:40
msgid "View Conversation"
msgstr ""

#: framework/legacy/templates/buddypress/activity/entry.php:48
msgid "Comment <span class=\"x-bp-count\">%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/activity/entry.php:60, framework/legacy/templates/buddypress/activity/entry.php:60
msgid "Remove Favorite"
msgstr ""

#: framework/legacy/templates/buddypress/activity/entry.php:56
msgid "Mark as Favorite"
msgstr ""

#: framework/legacy/templates/buddypress/activity/entry.php:56
msgid "Favorite"
msgstr ""

#: framework/legacy/templates/buddypress/activity/entry.php:95, framework/legacy/templates/buddypress/activity/post-form.php:30
msgid "Post"
msgstr ""

#: framework/legacy/templates/buddypress/activity/entry.php:95
msgid "Cancel"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:19
msgid "The public activity for everyone on this site."
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:19, framework/legacy/templates/buddypress/members/index.php:19
msgid "All Members <span>%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:29
msgid "The activity of my friends only."
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:29, framework/legacy/templates/buddypress/members/index.php:22
msgid "My Friends <span>%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:41
msgid "The activity of groups I am a member of."
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:41, framework/legacy/templates/buddypress/groups/index.php:22
msgid "My Groups <span>%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:51
msgid "The activity I've marked as a favorite."
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:51
msgid "My Favorites <span>%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:59
msgid "Activity that I have been mentioned in."
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:59
msgid "Mentions"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:59
msgctxt "Number of new activity mentions"
msgid "%s new"
msgid_plural "%s new"
msgstr[0] ""
msgstr[1] ""

#: framework/legacy/templates/buddypress/activity/index.php:71, framework/legacy/templates/buddypress/groups/single/activity.php:3
msgid "RSS"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:76, framework/legacy/templates/buddypress/groups/single/activity.php:8, framework/legacy/templates/buddypress/members/single/activity.php:18
msgid "Show:"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:78, framework/legacy/templates/buddypress/groups/single/activity.php:10, framework/legacy/templates/buddypress/members/single/activity.php:20
msgid "Everything"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:79, framework/legacy/templates/buddypress/groups/single/activity.php:11, framework/legacy/templates/buddypress/members/single/activity.php:21
msgid "Updates"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:83, framework/legacy/templates/buddypress/members/single/activity.php:27
msgid "Posts"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:90, framework/legacy/templates/buddypress/groups/single/activity.php:14, framework/legacy/templates/buddypress/members/single/activity.php:43
msgid "Forum Topics"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:91, framework/legacy/templates/buddypress/groups/single/activity.php:15, framework/legacy/templates/buddypress/members/single/activity.php:44
msgid "Forum Replies"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:97, framework/legacy/templates/buddypress/members/single/activity.php:50
msgid "New Groups"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:98, framework/legacy/templates/buddypress/groups/single/activity.php:18, framework/legacy/templates/buddypress/members/single/activity.php:51
msgid "Group Memberships"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:104, framework/legacy/templates/buddypress/members/single/activity.php:35
msgid "Friendships"
msgstr ""

#: framework/legacy/templates/buddypress/activity/index.php:108
msgid "New Members"
msgstr ""

#: framework/legacy/templates/buddypress/activity/post-form.php:19
msgid "What's new, %s?"
msgstr ""

#: framework/legacy/templates/buddypress/activity/post-form.php:17
msgid "What's new in %s, %s?"
msgstr ""

#: framework/legacy/templates/buddypress/activity/post-form.php:30
msgid "Post In"
msgstr ""

#: framework/legacy/templates/buddypress/blogs/blogs-loop.php:66
msgid "Sorry, there were no sites found."
msgstr ""

#: framework/legacy/templates/buddypress/blogs/index.php:16
msgid "All Sites <span>%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/blogs/index.php:20
msgid "My Sites <span>%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/blogs/index.php:36, framework/legacy/templates/buddypress/groups/index.php:36, framework/legacy/templates/buddypress/members/index.php:35, framework/legacy/templates/buddypress/members/single/blogs.php:19, framework/legacy/templates/buddypress/members/single/forums.php:18, framework/legacy/templates/buddypress/members/single/friends.php:20, framework/legacy/templates/buddypress/members/single/groups.php:20
msgid "Order By:"
msgstr ""

#: framework/legacy/templates/buddypress/blogs/index.php:38, framework/legacy/templates/buddypress/groups/index.php:39, framework/legacy/templates/buddypress/members/index.php:37, framework/legacy/templates/buddypress/members/single/blogs.php:21, framework/legacy/templates/buddypress/members/single/forums.php:20, framework/legacy/templates/buddypress/members/single/friends.php:22, framework/legacy/templates/buddypress/members/single/groups.php:22
msgid "Last Active"
msgstr ""

#: framework/legacy/templates/buddypress/blogs/index.php:39, framework/legacy/templates/buddypress/members/single/blogs.php:22
msgid "Newest"
msgstr ""

#: framework/legacy/templates/buddypress/blogs/index.php:40, framework/legacy/templates/buddypress/groups/index.php:42, framework/legacy/templates/buddypress/members/index.php:41, framework/legacy/templates/buddypress/members/single/blogs.php:23, framework/legacy/templates/buddypress/members/single/friends.php:24, framework/legacy/templates/buddypress/members/single/groups.php:25
msgid "Alphabetical"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:39, framework/legacy/templates/buddypress/groups/single/admin.php:30
msgid "Group Name (required)"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:44, framework/legacy/templates/buddypress/groups/single/admin.php:33
msgid "Group Description (required)"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:61, framework/legacy/templates/buddypress/groups/single/admin.php:70
msgid "Privacy Options"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:65, framework/legacy/templates/buddypress/groups/single/admin.php:75
msgid "This is a public group"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:67, framework/legacy/templates/buddypress/groups/single/admin.php:77
msgid "Any site member can join this group."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:68, framework/legacy/templates/buddypress/groups/create.php:77, framework/legacy/templates/buddypress/groups/single/admin.php:78, framework/legacy/templates/buddypress/groups/single/admin.php:88
msgid "This group will be listed in the groups directory and in search results."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:69, framework/legacy/templates/buddypress/groups/single/admin.php:79
msgid "Group content and activity will be visible to any site member."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:74, framework/legacy/templates/buddypress/groups/single/admin.php:85
msgid "This is a private group"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:76, framework/legacy/templates/buddypress/groups/single/admin.php:87
msgid "Only users who request membership and are accepted can join the group."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:78, framework/legacy/templates/buddypress/groups/create.php:87, framework/legacy/templates/buddypress/groups/single/admin.php:89, framework/legacy/templates/buddypress/groups/single/admin.php:99
msgid "Group content and activity will only be visible to members of the group."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:83, framework/legacy/templates/buddypress/groups/single/admin.php:95
msgid "This is a hidden group"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:85, framework/legacy/templates/buddypress/groups/single/admin.php:97
msgid "Only users who are invited can join the group."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:86, framework/legacy/templates/buddypress/groups/single/admin.php:98
msgid "This group will not be listed in the groups directory or search results."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:92, framework/legacy/templates/buddypress/groups/single/admin.php:104
msgid "Group Invitations"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:94, framework/legacy/templates/buddypress/groups/single/admin.php:106
msgid "Which members of this group are allowed to invite others?"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:99, framework/legacy/templates/buddypress/groups/single/admin.php:111
msgid "All group members"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:104, framework/legacy/templates/buddypress/groups/single/admin.php:116
msgid "Group admins and mods only"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:109, framework/legacy/templates/buddypress/groups/single/admin.php:121
msgid "Group admins only"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:115
msgid "Group Forums"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:126
msgid "<strong>Attention Site Admin:</strong> Group forums require the <a href=\"%s\">correct setup and configuration</a> of a bbPress installation."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:119
msgid "Should this group have a forum?"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:122, framework/legacy/templates/buddypress/groups/single/admin.php:61
msgid "Enable discussion forum"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:152, framework/legacy/templates/buddypress/groups/single/admin.php:137
msgid "Upload an image to use as an avatar for this group. The image will be shown on the main group page, and in search results."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:156, framework/legacy/templates/buddypress/groups/single/admin.php:141
msgid "Upload"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:160
msgid "To skip the avatar upload process, hit the \"Next\" button."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:167
msgid "Crop Group Avatar"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:169, framework/legacy/templates/buddypress/groups/single/admin.php:162
msgid "Avatar to crop"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:172, framework/legacy/templates/buddypress/groups/single/admin.php:165
msgid "Avatar preview"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:175, framework/legacy/templates/buddypress/groups/single/admin.php:168
msgid "Crop"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:249
msgid "Once you have built up friend connections you will be able to invite others to your group."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:214, framework/legacy/templates/buddypress/groups/single/invites-loop.php:61
msgid "Select people to invite from your friends list."
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:232, framework/legacy/templates/buddypress/groups/single/invites-loop.php:37
msgid "Remove Invite"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:278
msgid "Next"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:285
msgid "Create"
msgstr ""

#: framework/legacy/templates/buddypress/groups/create.php:292
msgid "Finish"
msgstr ""

#: framework/legacy/templates/buddypress/groups/groups-loop.php:71
msgid "There were no groups found."
msgstr ""

#: framework/legacy/templates/buddypress/groups/index.php:19
msgid "All Groups <span>%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/groups/index.php:40, framework/legacy/templates/buddypress/members/single/groups.php:23
msgid "Most Members"
msgstr ""

#: framework/legacy/templates/buddypress/groups/index.php:41, framework/legacy/templates/buddypress/members/single/groups.php:24
msgid "Newly Created"
msgstr ""

#: framework/legacy/templates/buddypress/members/activate.php:23
msgid "Please provide a valid activation key."
msgstr ""

#: framework/legacy/templates/buddypress/members/activate.php:16
msgid "Your account was activated successfully! You can now <a href=\"%s\">log in</a> with the username and password you provided when you signed up."
msgstr ""

#: framework/legacy/templates/buddypress/members/activate.php:14
msgid "Your account was activated successfully! Your account details have been sent to you in a separate email."
msgstr ""

#: framework/legacy/templates/buddypress/members/index.php:38, framework/legacy/templates/buddypress/members/single/friends.php:23
msgid "Newest Registered"
msgstr ""

#: framework/legacy/templates/buddypress/members/members-loop.php:76
msgid "Sorry, no members were found."
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:13
msgid "User registration is currently not allowed."
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:30
msgid "Account Details"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:32
msgid "Username"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:32, framework/legacy/templates/buddypress/members/register.php:36, framework/legacy/templates/buddypress/members/register.php:40, framework/legacy/templates/buddypress/members/register.php:44, framework/legacy/templates/buddypress/members/register.php:132, framework/legacy/templates/buddypress/members/register.php:141
msgid "(required)"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:36
msgid "Email Address"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:40
msgid "Choose a Password"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:44
msgid "Confirm Password"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:62
msgid "Profile Details"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:94, framework/legacy/templates/buddypress/members/register.php:79, framework/legacy/templates/buddypress/members/single/profile/edit.php:47, framework/legacy/templates/buddypress/members/single/profile/edit.php:33
msgid "This field can be seen by: <span class=\"current-visibility-level\">%s</span>"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:79
msgctxt "Change profile field visibility level"
msgid "Change"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:84, framework/legacy/templates/buddypress/members/single/profile/edit.php:38
msgid "Who can see this field?"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:126
msgid "Blog Details"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:128
msgid "Yes, I'd like to create a new site"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:132
msgid "Blog URL"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:145
msgid "I would like my site to appear in search engines, and in public listings around this network."
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:164
msgid "Sign Up"
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:181
msgid "You have successfully created your account! Please log in using the username and password you have just created."
msgstr ""

#: framework/legacy/templates/buddypress/members/register.php:179
msgid "You have successfully created your account! To begin using this site you will need to activate your account via the email we have just sent to your address."
msgstr ""

#: framework/legacy/cranium/headers/views/ethos/_landmark-header.php:103, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:113, framework/legacy/cranium/headers/views/renew/_landmark-header.php:93
msgid "Post Archive by Day"
msgstr ""

#: framework/legacy/cranium/headers/views/ethos/_landmark-header.php:97, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:106, framework/legacy/cranium/headers/views/renew/_landmark-header.php:89
msgid "Post Archive by Month"
msgstr ""

#: framework/legacy/cranium/headers/views/ethos/_landmark-header.php:91, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:99, framework/legacy/cranium/headers/views/renew/_landmark-header.php:85
msgid "Post Archive by Year"
msgstr ""

#: framework/legacy/cranium/headers/views/ethos/_landmark-header.php:85, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:92, framework/legacy/cranium/headers/views/renew/_landmark-header.php:81
msgid "Oops!"
msgstr ""

#: framework/legacy/cranium/headers/views/ethos/_landmark-header.php:74, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:79, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:64, framework/legacy/cranium/headers/views/renew/_landmark-header.php:73
msgid "Tag Archive"
msgstr ""

#: framework/legacy/cranium/headers/views/ethos/_landmark-header.php:61, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:49, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:34, framework/legacy/cranium/headers/views/renew/_landmark-header.php:62
msgid "Category Archive"
msgstr ""

#: framework/legacy/cranium/headers/views/ethos/_landmark-header.php:53, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:25, framework/legacy/cranium/headers/views/renew/_landmark-header.php:55
msgid "Search Results"
msgstr ""

#: framework/legacy/cranium/headers/views/global/_nav-primary.php:15
msgid "Navigation"
msgstr ""

#: framework/legacy/cranium/headers/views/integrity/_landmark-header.php:114, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:107, framework/legacy/cranium/headers/views/integrity/_landmark-header.php:100
msgid "Below you'll find a list of all posts from "
msgstr ""

#: framework/legacy/cranium/headers/views/integrity/_landmark-header.php:93
msgid "You blew up the Internet. "
msgstr ""

#: framework/legacy/cranium/headers/views/integrity/_landmark-header.php:80
msgid "Below you'll find a list of all items that have been tagged as "
msgstr ""

#: framework/legacy/cranium/headers/views/integrity/_landmark-header.php:65
msgid "Below you'll find a list of all posts that have been tagged as "
msgstr ""

#: framework/legacy/cranium/headers/views/integrity/_landmark-header.php:50
msgid "Below you'll find a list of all items that have been categorized as "
msgstr ""

#: framework/legacy/cranium/headers/views/integrity/_landmark-header.php:35
msgid "Below you'll find a list of all posts that have been categorized as "
msgstr ""

#: framework/legacy/cranium/headers/views/integrity/_landmark-header.php:26
msgid "Below you'll see everything we could locate for your search of "
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:40
msgid "Notify group members of these changes via email"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:46, framework/legacy/templates/buddypress/groups/single/admin.php:127, framework/legacy/templates/buddypress/members/single/profile/edit.php:61, framework/legacy/templates/buddypress/members/single/settings/capabilities.php:13, framework/legacy/templates/buddypress/members/single/settings/general.php:24, framework/legacy/templates/buddypress/members/single/settings/notifications.php:9, framework/legacy/templates/buddypress/members/single/settings/profile.php:41
msgid "Save"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:148
msgid "If you'd like to remove the existing avatar but not upload a new one, please use the following link:"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:149, framework/legacy/templates/buddypress/groups/single/admin.php:149
msgid "Delete Avatar"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:160
msgid "Crop Avatar"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:188
msgid "Administrators"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:201
msgid "View Activity"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:204, framework/legacy/templates/buddypress/groups/single/admin.php:233
msgid "Demote to Member"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:220
msgid "Moderators"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:232, framework/legacy/templates/buddypress/groups/single/admin.php:232, framework/legacy/templates/buddypress/groups/single/admin.php:285, framework/legacy/templates/buddypress/groups/single/admin.php:285
msgid "Promote to Admin"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:303, framework/legacy/templates/buddypress/groups/single/members.php:54
msgid "This group has no members."
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:283
msgid "Kick and ban this member"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:283
msgid "Kick &amp; Ban"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:284, framework/legacy/templates/buddypress/groups/single/admin.php:284
msgid "Promote to Mod"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:279
msgid "Unban this member"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:279
msgid "Remove Ban"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:289
msgid "Remove this member"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:289
msgid "Remove from group"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:337
msgid "WARNING: Deleting this group will completely remove ALL content associated with it. There is no way back, please be careful with this option."
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/admin.php:340
msgid "I understand the consequences of deleting this group."
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/group-header.php:12
msgid "active %s"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/group-header.php:36
msgid "Admins"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/group-header.php:45
msgid "Mods"
msgstr ""

#: framework/legacy/templates/buddypress/groups/single/invites-loop.php:71, framework/legacy/templates/buddypress/members/single/messages/compose.php:28
msgid "Send"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/forums.php:21
msgid "Most Posts"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/forums.php:22
msgid "Unreplied"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/friends/requests.php:46
msgid "You have no pending friendship requests."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/friends/requests.php:20, framework/legacy/templates/buddypress/members/single/groups/invites.php:22
msgid "Accept"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/friends/requests.php:21, framework/legacy/templates/buddypress/members/single/groups/invites.php:23
msgid "Reject"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/groups/invites.php:42
msgid "You have no outstanding group invites."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/compose.php:5
msgid "Send To (Username or Friend's Name)"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/compose.php:14
msgid "This is a notice to all users."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:73
msgid "Sorry, no messages were found."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:10
msgid "From"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:10
msgid "To"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:12
msgid "Action"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:37
msgid "View Message"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/messages-loop.php:45, framework/legacy/templates/buddypress/members/single/messages/notices-loop.php:43, framework/legacy/templates/buddypress/members/single/messages/single.php:24
msgid "Delete Message"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/notices-loop.php:54
msgid "Sorry, no notices were found."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/notices-loop.php:36
msgid "Sent:"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/single.php:18
msgid "Conversation between %s and you."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/messages/single.php:14
msgid "You are alone in this conversation."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/notifications/notifications-loop.php:4
msgid "Notification"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/notifications/notifications-loop.php:5
msgid "Date Received"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/notifications/notifications-loop.php:6
msgid "Actions"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:69
msgid "Your profile photo will be used on your profile and throughout the site. To change your profile photo, please create an account with <a href=\"http://gravatar.com\">Gravatar</a> using the same email address as you used to register with this site."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:12
msgid "Change Profile Photo"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:13
msgid "Your profile photo will be used on your profile and throughout the site. If there is a <a href=\"http://gravatar.com\">Gravatar</a> associated with your account email we will use that, or you can upload an image from your computer."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:20
msgid "Click below to select a JPG, GIF or PNG format photo from your computer and then click 'Upload Image' to proceed."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:24
msgid "Upload Image"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:29
msgid "If you'd like to delete your current profile photo but not upload a new one, please use the delete profile photo button."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:30
msgid "Delete Profile Photo"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:30
msgid "Delete My Profile Photo"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:37
msgid "Crop Your New Profile Photo"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:39
msgid "Profile Photo to crop"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:42
msgid "Profile Photo preview"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/change-avatar.php:45
msgid "Crop Image"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/edit.php:10
msgid "Editing '%s' Profile Group"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/profile/edit.php:33
msgid "Change"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/capabilities.php:9
msgid "This user is a spammer."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/delete-account.php:12
msgid "Deleting this account will delete all of the content it has created. It will be completely irrecoverable."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/delete-account.php:8
msgid "Deleting your account will delete all of the content you have created. It will be completely irrecoverable."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/delete-account.php:23
msgid "I understand the consequences."
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/general.php:9
msgid "Lost your password?"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/general.php:13
msgid "Account Email"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/general.php:16
msgid "Change Password <span>(leave blank for no change)</span>"
msgstr ""

#: framework/legacy/templates/buddypress/members/single/settings/general.php:19
msgid "Repeat New Password <span>(confirm above entry)</span>"
msgstr ""

#: plugins/tco-content-dock/tco-content-dock.php:56, plugins/tco-content-dock/tco-content-dock.php:56
msgid "Content Dock"
msgstr ""

#: plugins/tco-sample-stack/tco-sample-stack.php:20, plugins/tco-sample-stack/tco-sample-stack.php:26
msgid "Sample Stack"
msgstr ""

#: plugins/tco-sample-stack/tco-sample-stack.php:32
msgid "Sample Value"
msgstr ""

#: plugins/tco-test-custom-element/tco-test-custom-element.php:83
msgid "On"
msgstr ""

#: plugins/tco-test-custom-element/tco-test-custom-element.php:106
msgid "Base"
msgstr ""

#: plugins/tco-test-custom-element/tco-test-custom-element.php:107
msgid "Interaction"
msgstr ""
