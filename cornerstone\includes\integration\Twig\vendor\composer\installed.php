<?php return array(
    'root' => array(
        'name' => 'themeco/cornerstone-twig-renderer',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'de387a8bf1ecd7959067d388ea516d3d92dfc40d',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.7',
            'version' => '6.4.7.0',
            'reference' => 'decadcf3865918ecfcbfa90968553994ce935a5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'ef4d7e442ca910c4764bce785146269b30cb5fc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '32a9da87d7b3245e09ac426c83d334ae9f06f80f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'a287ed7475f85bf6f61890146edbc932c0fff919',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'bc45c394692b948b4d383a08d7753968bed9a83d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '861391a8da9a04cbad2d232ddd9e4893220d6e25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '87b68208d5c1188808dd7839ee1e6c8ec3b02f1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.4.7',
            'version' => '6.4.7.0',
            'reference' => 'ffeb9591c61f65a68d47f77d12b83fa530227a69',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'b9d2189887bb6b2e0367a9fc7136c5239ab9b05a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'themeco/cornerstone-twig-renderer' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'de387a8bf1ecd7959067d388ea516d3d92dfc40d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/html-extra' => array(
            'pretty_version' => 'v3.9.0',
            'version' => '3.9.0.0',
            'reference' => '8d8bf63a958bec84dbbf12d6c9958319bbaa2eea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/html-extra',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/string-extra' => array(
            'pretty_version' => 'v3.9.0',
            'version' => '3.9.0.0',
            'reference' => '5ff1c41366aa003d45f6e2707c5d698c1b37ff99',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/string-extra',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.9.3',
            'version' => '3.9.3.0',
            'reference' => 'a842d75fed59cdbcbd3a3ad7fb9eb768fc350d58',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
