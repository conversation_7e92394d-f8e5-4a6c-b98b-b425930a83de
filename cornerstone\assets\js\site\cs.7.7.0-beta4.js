(()=>{var wi=Object.create;var le=Object.defineProperty;var vi=Object.getOwnPropertyDescriptor;var bi=Object.getOwnPropertyNames;var Si=Object.getPrototypeOf,Ti=Object.prototype.hasOwnProperty;var Ai=t=>le(t,"__esModule",{value:!0});var Ei=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Ue=(t,e)=>{for(var n in e)le(t,n,{get:e[n],enumerable:!0})},Ci=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of bi(e))!Ti.call(t,o)&&(n||o!=="default")&&le(t,o,{get:()=>e[o],enumerable:!(r=vi(e,o))||r.enumerable});return t},Li=(t,e)=>Ci(Ai(le(t!=null?wi(Si(t)):{},"default",!e&&t&&t.__esModule?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t);var fr=Ei(()=>{Array.prototype.flat||Object.defineProperty(Array.prototype,"flat",{configurable:!0,value:function t(){var e=isNaN(arguments[0])?1:Number(arguments[0]);return e?Array.prototype.reduce.call(this,function(n,r){return Array.isArray(r)?n.push.apply(n,t.call(r,e-1)):n.push(r),n},[]):Array.prototype.slice.call(this)},writable:!0}),Array.prototype.flatMap||Object.defineProperty(Array.prototype,"flatMap",{configurable:!0,value:function(t){return Array.prototype.map.apply(this,arguments).flat()},writable:!0})});var Tn={};Ue(Tn,{attach:()=>S,debug:()=>Jt,defineRivetArchetype:()=>it,drive:()=>Zt,registerAction:()=>yt,registerBehavior:()=>xt,registerEvent:()=>U,registerInnate:()=>Kt,registerMacro:()=>Sn,registerObserver:()=>tt,store:()=>J,util:()=>b});var b={};Ue(b,{NON_PASSIVE_ARGS:()=>Dt,PASSIVE_ARGS:()=>W,SCROLLBAR_ACTIVE_VAR_NAME:()=>Yt,SCROLLBAR_ACTIVE_VAR_STRING:()=>dn,SCROLLBAR_VAR_NAME:()=>fn,SCROLLBAR_VAR_STRING:()=>Fs,absVector:()=>Bi,addClass:()=>os,addVectors:()=>Fi,animateToElement:()=>Bs,animateTopOffset:()=>qs,appendHtmlString:()=>cs,balanceFromDifference:()=>Vi,ceil:()=>Ii,clamp:()=>Bt,clampVector:()=>zi,debounce:()=>St,defer:()=>Gi,dispatch:()=>bs,distanceInRange:()=>je,divideVectors:()=>Ni,doOnce:()=>_t,elementAttributes:()=>At,elementIndex:()=>as,elementIsVisibleInViewport:()=>wr,elementMeta:()=>rn,ensureNumber:()=>Z,ensureString:()=>qt,evaluateCondition:()=>Qs,expandElementValue:()=>gt,farthest:()=>us,floor:()=>Hi,fontCompress:()=>Os,forceOpaque:()=>Ns,getCachedAttribute:()=>Et,getCachedJsonAttribute:()=>Ts,getDurations:()=>en,getEasing:()=>pt,getJsonAttrContent:()=>Ze,getOuterHeight:()=>ds,getPrecisionLength:()=>Pi,getPrecisionLengthWithCommas:()=>Mi,getStateValue:()=>Tr,getTransitionDuration:()=>gs,getTransitionTimingMS:()=>ps,hasClass:()=>rs,intersect:()=>zs,isMobile:()=>cn,isScalar:()=>ki,lerp:()=>Wi,listener:()=>X,listenerPassive:()=>q,lockMotion:()=>br,makeAlternatingSynchronizer:()=>ms,makeDirectionalEasing:()=>ns,makeElementWeakMap:()=>Wt,makeFindClosest:()=>tn,makeGetComputedFloatValues:()=>ks,makeGetComputedStyle:()=>Ls,makeGetComputedStyles:()=>an,makeMotionLockedUpdate:()=>Sr,makeRafLoop:()=>me,makeSortByKey:()=>Oi,makeStateSynchronizer:()=>cr,makeTreeWalker:()=>de,memoize:()=>Je,multiplyVectors:()=>Di,normalizeCondition:()=>gn,normalizeTeardown:()=>Nt,offsetFromTop:()=>pe,onLoad:()=>V,onPageVisibilityChange:()=>nn,onPercentScrolled:()=>Es,onResize:()=>Vt,onResizeOrScan:()=>ct,onScan:()=>ge,onScanLazy:()=>Cs,onScroll:()=>Gt,onScrollOrResize:()=>mr,onScrollRaw:()=>on,onViewportChange:()=>gr,once:()=>pr,oncePassive:()=>Tt,parseHTML:()=>Xe,parseTime:()=>mt,removeClass:()=>is,rivetDispatch:()=>vs,rivetListener:()=>ws,round:()=>nr,roundVector:()=>$i,runAnimation:()=>Ws,scrollOffset:()=>fs,scrollingDisable:()=>$s,scrollingEnable:()=>_s,setRootVar:()=>jt,siblings:()=>ar,subtractVectors:()=>_i,teardown:()=>j,throttle:()=>Qe,toggleClass:()=>ss,transitionEnd:()=>Ss,triggerScan:()=>As,tween:()=>xs,unwrapHtmlTemplate:()=>ls,updateStateKey:()=>Ys,vectorsEq:()=>qi,watchElementIsVisible:()=>Ds,waypoint:()=>Hs,wrapNumber:()=>Ye,wrapVector:()=>Ri});function Z(t){if(typeof t=="function")return Z(t());if(typeof t=="number")return t;let e=Number.parseFloat(t);return Number.isNaN(e)?0:e}var qt=t=>typeof t=="function"?qt(t()):typeof t=="string"?t:"";function ki(t){var e=typeof t;return e==="string"||e==="number"||e==="boolean"||e==="symbol"||t==null||t instanceof Symbol||t instanceof String||t instanceof Number||t instanceof Boolean}var Oi=t=>(e,n)=>e[t]-n[t];var Ii=(t,e=100)=>Math.ceil(t*e)/e,Hi=(t,e=100)=>Math.floor(t*e)/e,nr=(t,e=100)=>Math.round((t+Number.EPSILON)*e)/e,Pi=t=>{var e,n;return(n=(e=t.toString().split(/\./)[1])==null?void 0:e.length)!=null?n:0},Mi=t=>{var e,n;return(n=(e=t.toString().split(/,/)[1])==null?void 0:e.length)!=null?n:0},Ye=(t,e)=>(e+t)%e,Ri=([t,e],[n,r])=>[Ye(t,n),Ye([e,r])],Bt=(t,e,n)=>Math.min(Math.max(t,e),n),zi=([t,e],n,r)=>[Bt(t,n,r),Bt(e,n,r)],Fi=([t,e],[n,r])=>[t+n,e+r],qi=([t,e],[n,r])=>t===n&&e===r,Bi=t=>t.map(Math.abs),$i=(t,e)=>t.map(n=>nr(n,e)),_i=([t,e],[n,r])=>[t-n,e-r],Di=([t,e],[n,r])=>[t*n,e*r],Ni=([t,e],[n,r])=>[t/n,e/r],je=(t,e,n)=>(t-e+n)%n,Wi=(t,e,n)=>t+n*(e-t),Vi=(t,e,n)=>{let r=je(t,e,n),o=je(e,t,n);return r===o?0:r>o?-1:1};function Gi(t){return setTimeout(t,0)}function Qe(t,e,n={}){var r=!0,o=!0;return r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o,St(t,e,{leading:r,maxWait:e,trailing:o})}function St(t,e=0,n={}){var r,o,i,s,a,c,u=0,p=!1,f=!1,d=!0;p=!!n.leading,f="maxWait"in n,i=f?Math.max(n.maxWait||0,e):i,d="trailing"in n?!!n.trailing:d;function l(v){var E=r,H=o;return r=o=void 0,u=v,s=t.apply(H,E),s}function m(v){return u=v,a=setTimeout(x,e),p?l(v):s}function g(v){var E=v-c,H=v-u,F=e-E;return f?Math.min(F,i-H):F}function h(v){var E=v-c,H=v-u;return c===void 0||E>=e||E<0||f&&H>=i}function x(){var v=window.Date.now();if(h(v))return y(v);a=setTimeout(x,g(v))}function y(v){return a=void 0,d&&r?l(v):(r=o=void 0,s)}function w(){a!==void 0&&clearTimeout(a),u=0,r=c=o=a=void 0}function T(){return a===void 0?s:y(window.Date.now())}function O(){var v=window.Date.now(),E=h(v);if(r=arguments,o=this,c=v,E){if(a===void 0)return m(c),()=>void w();if(f)return clearTimeout(a),a=setTimeout(x,e),l(c),()=>void w()}return a===void 0&&(a=setTimeout(x,e)),()=>void w()}return O.cancel=w,O.flush=T,O}function Je(t,e){let n=new Map;return function(...r){let o=e?e.apply(this,r):r[0];if(n.has(o))return n.get(o);let i=t.apply(this,r);return n.set(o,i),i}}var Ui=4,Yi=.001,ji=1e-7,Qi=10,$t=11,ue=1/($t-1),Ji=typeof Float32Array=="function";function rr(t,e){return 1-3*e+3*t}function or(t,e){return 3*e-6*t}function ir(t){return 3*t}function fe(t,e,n){return((rr(e,n)*t+or(e,n))*t+ir(e))*t}function sr(t,e,n){return 3*rr(e,n)*t*t+2*or(e,n)*t+ir(e)}function Ki(t,e,n,r,o){var i,s,a=0;do s=e+(n-e)/2,i=fe(s,r,o)-t,i>0?n=s:e=s;while(Math.abs(i)>ji&&++a<Qi);return s}function Zi(t,e,n,r){for(var o=0;o<Ui;++o){var i=sr(e,n,r);if(i===0)return e;var s=fe(e,n,r)-t;e-=s/i}return e}function Xi(t){return t}function Ke(t,e,n,r){if(!(0<=t&&t<=1&&0<=n&&n<=1))throw new Error("bezier x values must be in [0, 1] range");if(t===e&&n===r)return Xi;for(var o=Ji?new Float32Array($t):new Array($t),i=0;i<$t;++i)o[i]=fe(i*ue,t,n);function s(a){for(var c=0,u=1,p=$t-1;u!==p&&o[u]<=a;++u)c+=ue;--u;var f=(a-o[u])/(o[u+1]-o[u]),d=c+f*ue,l=sr(d,t,n);return l>=Yi?Zi(a,d,t,n):l===0?d:Ki(a,c,c+ue,t,n)}return function(c){return c===0||c===1?c:fe(s(c),e,r)}}var ts=t=>{switch(t){case"linear":return"cubic-bezier(0.0, 0.0, 1.0, 1.0)";case"ease-in":return"cubic-bezier(0.42, 0, 1.0, 1.0)";case"ease-out":return"cubic-bezier(0, 0, 0.58, 1.0)";case"ease-in-out":return"cubic-bezier(0.42, 0, 0.58, 1.0)";case"ease":default:return"cubic-bezier(0.25, 0.1, 0.25, 1.0)"}},k=Je(t=>{let e=ts(t);try{let[,n]=e.match(/cubic-bezier\((.*)\)/);return Ke(...n.split(",").map(r=>Number(r.trim())))}catch{console.warn("unable to parse easing function",e)}return k("ease")}),es={easeInQuad:k("cubic-bezier(0.550, 0.085, 0.680, 0.530)"),easeInCubic:k("cubic-bezier(0.550, 0.055, 0.675, 0.190)"),easeInQuart:k("cubic-bezier(0.895, 0.030, 0.685, 0.220)"),easeInQuint:k("cubic-bezier(0.755, 0.050, 0.855, 0.060)"),easeInSine:k("cubic-bezier(0.470, 0.000, 0.745, 0.715)"),easeInExpo:k("cubic-bezier(0.950, 0.050, 0.795, 0.035)"),easeInCirc:k("cubic-bezier(0.600, 0.040, 0.980, 0.335)"),easeInBack:k("cubic-bezier(0.600, -0.280, 0.735, 0.045)"),easeOutQuad:k("cubic-bezier(0.250, 0.460, 0.450, 0.940)"),easeOutCubic:k("cubic-bezier(0.215, 0.610, 0.355, 1.000)"),easeOutQuart:k("cubic-bezier(0.165, 0.840, 0.440, 1.000)"),easeOutQuint:k("cubic-bezier(0.230, 1.000, 0.320, 1.000)"),easeOutSine:k("cubic-bezier(0.390, 0.575, 0.565, 1.000)"),easeOutExpo:k("cubic-bezier(0.190, 1.000, 0.220, 1.000)"),easeOutCirc:k("cubic-bezier(0.075, 0.820, 0.165, 1.000)"),easeOutBack:k("cubic-bezier(0.175, 0.885, 0.320, 1.275)"),easeInOutQuad:k("cubic-bezier(0.455, 0.030, 0.515, 0.955)"),easeInOutCubic:k("cubic-bezier(0.645, 0.045, 0.355, 1.000)"),easeInOutQuart:k("cubic-bezier(0.770, 0.000, 0.175, 1.000)"),easeInOutQuint:k("cubic-bezier(0.860, 0.000, 0.070, 1.000)"),easeInOutSine:k("cubic-bezier(0.445, 0.050, 0.550, 0.950)"),easeInOutExpo:k("cubic-bezier(1.000, 0.000, 0.000, 1.000)"),easeInOutCirc:k("cubic-bezier(0.785, 0.135, 0.150, 0.860)"),easeInOutBack:k("cubic-bezier(0.680, -0.550, 0.265, 1.550)"),materialStand:k("cubic-bezier(0.400, 0.000, 0.200, 1.000)"),materialDecel:k("cubic-bezier(0.000, 0.000, 0.200, 1.000)"),materialAccel:k("cubic-bezier(0.400, 0.000, 1.000, 1.000)"),materialSharp:k("cubic-bezier(0.400, 0.000, 0.600, 1.000)")};function pt(t){return es[t]||k(t)}var ns=t=>{let e=pt(t);return n=>{let r=(-1*n+1)/2,o=Math.min(1,Math.max(0,r));return(e(o)-.5)*2}};var rs=(t,e)=>{var n;return(n=t==null?void 0:t.classList)==null?void 0:n.contains(e)},os=(t,e)=>{var n;return(n=t==null?void 0:t.classList)==null?void 0:n.add(e)},is=(t,e)=>{var n;return(n=t==null?void 0:t.classList)==null?void 0:n.remove(e)},ss=(t,e,n)=>{var r;return(r=t==null?void 0:t.classList)==null?void 0:r.toggle(e,n)};function as(t){if(!t)return-1;for(var e=0;t=t.previousElementSibling;)e++;return e}function Ze(t,e){let n=t.getAttribute(e);if(n===null)return{};if(typeof n=="string")try{return JSON.parse(n)}catch{try{return JSON.parse(n.replace(/&quot;/g,'"'))}catch{}}return n}var Xe=t=>{let e=document.implementation.createHTMLDocument("");return e.body.innerHTML=t,e.body.children},cs=(t,e)=>{Array.from(Xe(e)).forEach(n=>{t.append(n)})},ls=t=>{Array.from(Xe(t.innerHTML)).forEach(e=>{t.insertAdjacentElement("afterend",e)}),t.remove()};function us(t,e){let n=t,r;for(;n&&n.parentElement;)n=n.parentElement.closest(e),n&&(r=n);return r}var ar=t=>t&&t.parentElement?Array.from(t.parentElement.children).filter(e=>e!==t):[],de=(t,e)=>n=>{let r=new Set,o=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:()=>NodeFilter.FILTER_ACCEPT});for(;o.nextNode();)if(t(o.currentNode)){if(e)return o.currentNode;r.add(o.currentNode)}return e?null:Array.from(r)},pe=t=>t?t.offsetParent?t.offsetTop+pe(t.offsetParent):t.offsetTop:0,fs=t=>{let e=t.getBoundingClientRect(),{top:n,left:r,height:o}=e;return{top:n+window.scrollY,bottom:n+o+window.scrollY,left:r+window.scrollX}},ds=t=>{var o;if(!t)return 0;let e=Math.max(t.scrollHeight,t.offsetHeight),n=(o=t.getAttribute("style"))!=null?o:"";t.style.display="block",t.style.position="absolute",t.style.visibility="hidden";let r=Math.max(0,e,t.scrollHeight,t.offsetHeight);return t.setAttribute("style",n),r},tn=t=>{let e=de(t,!0),n=o=>{let i=o;for(;i;){if(t(i))return i;i=i.parentElement}},r=o=>{let i=o;for(;i;){let s;if(ar(i).find(a=>(s=t(a)?a:e(a),s)),s)return s;i=i.parentElement}};return o=>n(o)||r(o)||null};function ps(t){if(!t)return 0;let n=window.getComputedStyle(t)["transition-duration"]||"";return parseFloat(n.replace("s",""))*1e3}var cr=(t,{pending:e=()=>{},delay:n=10,initialState:r=null}={})=>{let o=r,i=r,s=[],a=!1,c=()=>{o!==i&&(a=!0,e(!0),o=i,t(o,(...p)=>{a=!1,e(!1),s=p,c()},...s))},u=St(p=>{i=p,a||c()},n);return u.reset=()=>{o=!1,i=!1,s=[]},u},ms=(t,e,n,r=!1)=>cr((o,i,s)=>{o?t(()=>void i(e)):e(i),s&&s(i)},{delay:n,initialState:r}),_t=t=>{let e=!1;return(...n)=>{if(!e)return e=!0,t(...n)}},mt=(t,e=0)=>{if(typeof t=="number")return t;let n=typeof t=="string"?t:"",[,r,o=""]=n.match(/(\d*.?\d+)(\w*)/)||[],i=parseFloat(r);return Number.isNaN(i)?e:o.toLowerCase()==="s"?i*1e3:i};function gs(t,e){return mt(t&&window.getComputedStyle(t).getPropertyValue("transition-duration"),e)}function en(t){let e=window.getComputedStyle(t);e.getPropertyValue("transition-duration");let n=mt(e.getPropertyValue("transition-duration"),0),r=mt(e.getPropertyValue("transition-delay"),0),o=mt(e.getPropertyValue("animation-duration"),0),i=mt(e.getPropertyValue("animation-delay"),0);return{transitionDuration:n,transitionDelay:r,animationDuration:o,animationDelay:i,transitionTime:n+r,animationTime:o+i}}var me=t=>{let e,n,r=o=>{typeof e=="undefined"&&(e=o);let i=o-e;t(i,o)!==!1&&(n=requestAnimationFrame(r))};return n=requestAnimationFrame(r),()=>void cancelAnimationFrame(n)},hs=({setup:t=()=>{},update:e=()=>{},complete:n=()=>{},cancel:r=()=>{},duration:o,easing:i})=>{let s=mt(o,500),a=pt(i);t();let c=me(u=>{if(u<s)e(a(u/s));else return e(1),n(),!1});return()=>{r(),c()}},ys=(t,e,n)=>e===n?n:n>e?e+(n-e)*t:e+(e-n)*(t*-1),lr=t=>Object.keys(t).reduce((e,n)=>(e[n]=parseFloat(t[n]),e),{}),ur=(t,{update:e,interpolate:n=ys,...r})=>{let o=lr(t);return(i={})=>{let s=lr(i);return hs({update:a=>{e(Object.keys(s).reduce((c,u)=>(c[u]=n(a,o[u],s[u]),c),{}))},...r})}},xs=(t,e)=>{let n=typeof t=="object"?ur(t,e):ur({from:t},{...e,update:({from:r})=>e.update(r)});return r=>n(typeof r=="object"?r:{from:r})};Promise.resolve().then(()=>Li(fr()));var dr="rvt",W={passive:!0},Dt={passive:!1};function ws(t,e,n){return X(t,`${dr}-${e}`,n)}function vs(t,e){t.dispatchEvent(new CustomEvent(`${dr}-${e}`))}function bs(t,e,n={},r=!0){t.dispatchEvent(new CustomEvent(e),{bubbles:r,detail:n})}function X(t,e,n,r={}){return t?(typeof r.passive=="undefined"&&(r.passive=!1),t.addEventListener(e,n,r),()=>void t.removeEventListener(e,n,r)):()=>{}}function q(t,e,n){return X(t,e,n,W)}function V(t){let e=()=>void t();return document.readyState==="complete"?(e(),()=>{}):q(document,"readystatechange",function(){document.readyState==="complete"&&setTimeout(e,0)})}function pr(t,e,n,r=Dt){let o=function(i){t.removeEventListener(e,o),n(i)};return t.addEventListener(e,o,r),()=>void t.removeEventListener(e,o)}function Tt(t,e,n){return pr(t,e,n,W)}var Nt=t=>(Array.isArray(t)?t.map(Nt):[t]).flat().filter(e=>typeof e=="function"),j=t=>{let e=Nt(t);return()=>e.forEach(n=>n())};function Ss(t,e){let r=window.getComputedStyle(t)["transition-duration"];if(r=r?parseFloat(r.replace("s","")):0,r===0){e();return}let o=_t(e),i=setTimeout(function(){o()},r*1e3+500),s=Tt(t,"transitionend",o);return function(){clearTimeout(i),s()}}var nn=(t,e)=>(e&&t(document.visibilityState==="visible"),j([X(window,"pagehide",()=>{t(!1)}),X(window.document,"visibilitychange",()=>{t(document.visibilityState==="visible")})]));function D(t,e){let n,r=null;return function(o){if(n){r=o;return}n=setTimeout(function(){t(r),n=null},e)}}var Wt=(t={})=>{let e,n=()=>{e=new WeakMap},r=c=>e.has(c),o=c=>e.delete(c),i=c=>e.has(c)?e.get(c):t,s=(c,u)=>void e.set(c,u),a=(c,u)=>void s(c,u(i(c)));return n(),{get:i,del:o,set:s,has:r,update:a,reset:n,cache:()=>e}},rn=Wt(),At=Wt();function Et(t,e){return At.has(t)||At.set(t,new Map),At.get(t).has(e)||At.get(t).set(e,Ze(t,e)),At.get(t).get(e)}function Ts(t,e){if(!t)return{};let n=Et(t,e);return typeof n=="object"?n:{}}var As=()=>window.dispatchEvent(new CustomEvent("rvt-scan")),ge=t=>X(window,"rvt-scan",()=>t()),Vt=(t,e=!1)=>{e&&t();let n=D(t,100);return j([q(window,"resize",n,W),X(screen.orientation,"change",n)])},Gt=(t,e=!1)=>{e&&t();let n=D(t,40);return q(window,"scroll",n)},on=(t,e=!1)=>(e&&t(),q(window,"scroll",t)),mr=(t,e=!1)=>j([Gt(t,e),ct(t,e)]),ct=(t,e)=>j([ge(t),Vt(t,e)]),gr=(t,e)=>j([ge(t),V(t),nn(t,!1),Vt(t,e)]),Es=(t,e,n=!1)=>{let r,o,i=mr(()=>{let s=document.body.offsetHeight,c=1-(s-(window.scrollY+window.innerHeight))/s>=t;c!==o&&(e(c),c&&n&&(r=!0,i()),o=c)},!0);return()=>{r||i()}},Cs=(t,{throttle:e=50}={})=>{let n,o=Qe(()=>{n=requestAnimationFrame(()=>void t())},e,{trailing:!0}),i=St(o,450);return[V(i),Vt(i),ge(o),()=>cancelAnimationFrame(n)]},sn,hr;gr(()=>{sn=new WeakMap,hr=new WeakMap},!0);Gt(()=>{hr=new WeakMap},!0);var yr=t=>{let e=sn.get(t);return e||(e=new WeakMap,sn.set(t,e)),e};function an(t,e){let n=function(r){let o=yr(n).get(r);if(!o){let i=getComputedStyle(r);o=t.reduce((s,a)=>(s[a]=typeof e=="function"?e(i[a],a):i[a],s),{}),yr(n).set(r,o)}return o};return n}function Ls(t){let e=an([t]);return n=>e(n)[t]}function ks(t){return an(t,e=>parseFloat(e))}function Os(t,{c:e=1,min:n=Number.NEGATIVE_INFINITY,max:r=Number.POSITIVE_INFINITY}){let o=Z(n),i=Z(r);return Vt(()=>{let s=Bt(parseFloat(getComputedStyle(t,null).width)/(e*10),o,i);t.style.setProperty("font-size",`${s}px`)},!0)}var Is="ontouchstart"in document.documentElement;function cn(){return window.innerWidth<=978.98&&Is}var xr=0,Ct={};function A(t){if(!t)throw new Error("No options passed to Waypoint constructor");if(!t.element)throw new Error("No element option passed to Waypoint constructor");if(!t.handler)throw new Error("No handler option passed to Waypoint constructor");this.key="waypoint-"+xr,this.options=A.Adapter.extend({},A.defaults,t),this.element=this.options.element,this.adapter=new A.Adapter(this.element),this.callback=t.handler,this.axis=this.options.horizontal?"horizontal":"vertical",this.enabled=this.options.enabled,this.triggerPoint=null,this.group=A.Group.findOrCreate({name:this.options.group,axis:this.axis}),this.context=A.Context.findOrCreateByElement(this.options.context),A.offsetAliases[this.options.offset]&&(this.options.offset=A.offsetAliases[this.options.offset]),this.group.add(this),this.context.add(this),Ct[this.key]=this,xr+=1}A.prototype.queueTrigger=function(t){this.group.queueTrigger(this,t)};A.prototype.trigger=function(t){!this.enabled||this.callback&&this.callback.apply(this,t)};A.prototype.destroy=function(){this.context.remove(this),this.group.remove(this),delete Ct[this.key]};A.prototype.disable=function(){return this.enabled=!1,this};A.prototype.enable=function(){return this.context.refresh(),this.enabled=!0,this};A.prototype.next=function(){return this.group.next(this)};A.prototype.previous=function(){return this.group.previous(this)};A.invokeAll=function(t){var e=[];for(var n in Ct)e.push(Ct[n]);for(var r=0,o=e.length;r<o;r++)e[r][t]()};A.destroyAll=function(){A.invokeAll("destroy")};A.disableAll=function(){A.invokeAll("disable")};A.enableAll=function(){A.Context.refreshAll();for(var t in Ct)Ct[t].enabled=!0;return this};A.refreshAll=function(){A.Context.refreshAll()};A.viewportHeight=function(){return window.innerHeight||document.documentElement.clientHeight};A.viewportWidth=function(){return document.documentElement.clientWidth};A.adapters=[];A.defaults={context:window,continuous:!0,enabled:!0,group:"default",horizontal:!1,offset:0};A.offsetAliases={"bottom-in-view":function(){return this.context.innerHeight()-this.adapter.outerHeight()},"right-in-view":function(){return this.context.innerWidth()-this.adapter.outerWidth()}};(function(){"use strict";var t=0,e={},n=window.onload;function r(o){this.element=o,this.Adapter=A.Adapter,this.adapter=new this.Adapter(o),this.key="waypoint-context-"+t,this.didScroll=!1,this.didResize=!1,this.oldScroll={x:this.adapter.scrollLeft(),y:this.adapter.scrollTop()},this.waypoints={vertical:{},horizontal:{}},o.waypointContextKey=this.key,e[o.waypointContextKey]=this,t+=1,A.windowContext||(A.windowContext=!0,A.windowContext=new r(window)),this.createThrottledScrollHandler(),this.createThrottledResizeHandler()}r.prototype.add=function(o){var i=o.options.horizontal?"horizontal":"vertical";this.waypoints[i][o.key]=o,this.refresh()},r.prototype.checkEmpty=function(){var o=this.Adapter.isEmptyObject(this.waypoints.horizontal),i=this.Adapter.isEmptyObject(this.waypoints.vertical),s=this.element==this.element.window;o&&i&&!s&&(this.adapter.off(".waypoints"),delete e[this.key])},r.prototype.createThrottledResizeHandler=function(){var o=this;function i(){o.handleResize(),o.didResize=!1}this.adapter.on("resize.waypoints",function(){o.didResize||(o.didResize=!0,requestAnimationFrame(i))})},r.prototype.createThrottledScrollHandler=function(){var o=this;function i(){o.handleScroll(),o.didScroll=!1}this.adapter.on("scroll.waypoints",function(){(!o.didScroll||A.isTouch)&&(o.didScroll=!0,requestAnimationFrame(i))})},r.prototype.handleResize=function(){A.Context.refreshAll()},r.prototype.handleScroll=function(){var o={},i={horizontal:{newScroll:this.adapter.scrollLeft(),oldScroll:this.oldScroll.x,forward:"right",backward:"left"},vertical:{newScroll:this.adapter.scrollTop(),oldScroll:this.oldScroll.y,forward:"down",backward:"up"}};for(var s in i){var a=i[s],c=a.newScroll>a.oldScroll,u=c?a.forward:a.backward;for(var p in this.waypoints[s]){var f=this.waypoints[s][p];if(f.triggerPoint!==null){var d=a.oldScroll<f.triggerPoint,l=a.newScroll>=f.triggerPoint,m=d&&l,g=!d&&!l;(m||g)&&(f.queueTrigger(u),o[f.group.id]=f.group)}}}for(var h in o)o[h].flushTriggers();this.oldScroll={x:i.horizontal.newScroll,y:i.vertical.newScroll}},r.prototype.innerHeight=function(){return this.element==this.element.window?A.viewportHeight():this.adapter.innerHeight()},r.prototype.remove=function(o){delete this.waypoints[o.axis][o.key],this.checkEmpty()},r.prototype.innerWidth=function(){return this.element==this.element.window?A.viewportWidth():this.adapter.innerWidth()},r.prototype.destroy=function(){var o=[];for(var i in this.waypoints)for(var s in this.waypoints[i])o.push(this.waypoints[i][s]);for(var a=0,c=o.length;a<c;a++)o[a].destroy()},r.prototype.refresh=function(){var o=this.element==this.element.window,i=o?void 0:this.adapter.offset(),s={},a;this.handleScroll(),a={horizontal:{contextOffset:o?0:i.left,contextScroll:o?0:this.oldScroll.x,contextDimension:this.innerWidth(),oldScroll:this.oldScroll.x,forward:"right",backward:"left",offsetProp:"left"},vertical:{contextOffset:o?0:i.top,contextScroll:o?0:this.oldScroll.y,contextDimension:this.innerHeight(),oldScroll:this.oldScroll.y,forward:"down",backward:"up",offsetProp:"top"}};for(var c in a){var u=a[c];for(var p in this.waypoints[c]){var f=this.waypoints[c][p],d=f.options.offset,l=f.triggerPoint,m=0,g=l==null,h,x,y,w,T;f.element!==f.element.window&&(m=f.adapter.offset()[u.offsetProp]),typeof d=="function"?d=d.apply(f):typeof d=="string"&&(d=parseFloat(d),f.options.offset.indexOf("%")>-1&&(d=Math.ceil(u.contextDimension*d/100))),h=u.contextScroll-u.contextOffset,f.triggerPoint=Math.floor(m+h-d),x=l<u.oldScroll,y=f.triggerPoint>=u.oldScroll,w=x&&y,T=!x&&!y,!g&&w?(f.queueTrigger(u.backward),s[f.group.id]=f.group):(!g&&T||g&&u.oldScroll>=f.triggerPoint)&&(f.queueTrigger(u.forward),s[f.group.id]=f.group)}}return requestAnimationFrame(function(){for(var O in s)s[O].flushTriggers()}),this},r.findOrCreateByElement=function(o){return r.findByElement(o)||new r(o)},r.refreshAll=function(){for(var o in e)e[o].refresh()},r.findByElement=function(o){return e[o.waypointContextKey]},window.onload=function(){n&&n(),r.refreshAll()},A.Context=r})();(function(){"use strict";function t(o,i){return o.triggerPoint-i.triggerPoint}function e(o,i){return i.triggerPoint-o.triggerPoint}var n={vertical:{},horizontal:{}};function r(o){this.name=o.name,this.axis=o.axis,this.id=this.name+"-"+this.axis,this.waypoints=[],this.clearTriggerQueues(),n[this.axis][this.name]=this}r.prototype.add=function(o){this.waypoints.push(o)},r.prototype.clearTriggerQueues=function(){this.triggerQueues={up:[],down:[],left:[],right:[]}},r.prototype.flushTriggers=function(){for(var o in this.triggerQueues){var i=this.triggerQueues[o],s=o==="up"||o==="left";i.sort(s?e:t);for(var a=0,c=i.length;a<c;a+=1){var u=i[a];(u.options.continuous||a===i.length-1)&&u.trigger([o])}}this.clearTriggerQueues()},r.prototype.next=function(o){this.waypoints.sort(t);var i=A.Adapter.inArray(o,this.waypoints),s=i===this.waypoints.length-1;return s?null:this.waypoints[i+1]},r.prototype.previous=function(o){this.waypoints.sort(t);var i=A.Adapter.inArray(o,this.waypoints);return i?this.waypoints[i-1]:null},r.prototype.queueTrigger=function(o,i){this.triggerQueues[i].push(o)},r.prototype.remove=function(o){var i=A.Adapter.inArray(o,this.waypoints);i>-1&&this.waypoints.splice(i,1)},r.prototype.first=function(){return this.waypoints[0]},r.prototype.last=function(){return this.waypoints[this.waypoints.length-1]},r.findOrCreate=function(o){return n[o.axis][o.name]||new r(o)},A.Group=r})();(function(){"use strict";function t(r){return r===r.window}function e(r){return t(r)?r:r.defaultView}function n(r){this.element=r,this.handlers={}}n.prototype.innerHeight=function(){var r=t(this.element);return r?this.element.innerHeight:this.element.clientHeight},n.prototype.innerWidth=function(){var r=t(this.element);return r?this.element.innerWidth:this.element.clientWidth},n.prototype.off=function(r,o){function i(d,l,m){for(var g=0,h=l.length-1;g<h;g++){var x=l[g];(!m||m===x)&&d.removeEventListener(x)}}var s=r.split("."),a=s[0],c=s[1],u=this.element;if(c&&this.handlers[c]&&a)i(u,this.handlers[c][a],o),this.handlers[c][a]=[];else if(a)for(var p in this.handlers)i(u,this.handlers[p][a]||[],o),this.handlers[p][a]=[];else if(c&&this.handlers[c]){for(var f in this.handlers[c])i(u,this.handlers[c][f],o);this.handlers[c]={}}},n.prototype.offset=function(){if(!this.element.ownerDocument)return null;var r=this.element.ownerDocument.documentElement,o=e(this.element.ownerDocument),i={top:0,left:0};return this.element.getBoundingClientRect&&(i=this.element.getBoundingClientRect()),{top:i.top+o.pageYOffset-r.clientTop,left:i.left+o.pageXOffset-r.clientLeft}},n.prototype.on=function(r,o){var i=r.split("."),s=i[0],a=i[1]||"__default",c=this.handlers[a]=this.handlers[a]||{},u=c[s]=c[s]||[];u.push(o),this.element.addEventListener(s,o)},n.prototype.outerHeight=function(r){var o=this.innerHeight(),i;return r&&!t(this.element)&&(i=window.getComputedStyle(this.element),o+=parseInt(i.marginTop,10),o+=parseInt(i.marginBottom,10)),o},n.prototype.outerWidth=function(r){var o=this.innerWidth(),i;return r&&!t(this.element)&&(i=window.getComputedStyle(this.element),o+=parseInt(i.marginLeft,10),o+=parseInt(i.marginRight,10)),o},n.prototype.scrollLeft=function(){var r=e(this.element);return r?r.pageXOffset:this.element.scrollLeft},n.prototype.scrollTop=function(){var r=e(this.element);return r?r.pageYOffset:this.element.scrollTop},n.extend=function(){var r=Array.prototype.slice.call(arguments);function o(a,c){if(typeof a=="object"&&typeof c=="object")for(var u in c)c.hasOwnProperty(u)&&(a[u]=c[u]);return a}for(var i=1,s=r.length;i<s;i++)o(r[0],r[i]);return r[0]},n.inArray=function(r,o,i){return o==null?-1:o.indexOf(r,i)},n.isEmptyObject=function(r){for(var o in r)return!1;return!0},A.adapters.push({name:"noframework",Adapter:n}),A.Adapter=n})();var ln=A;q(window,"rvt-scan",ln.Context.refreshAll);function Hs(t,e,n,r=!0){let o=new ln({element:t,handler:(...s)=>{e(...s),r&&i()},offset:n}),i=()=>o&&void o.destroy();return i}var Ut=new Map,Ps=({threshold:t=.5,top:e="0px",bottom:n="0px"}={})=>{let r=Number.parseFloat(t);return{key:`${e}:${n}:${r}`,options:{root:null,rootMargin:`${e} 0px ${n} 0px`,_threshold:r}}},Ms=(t,e)=>new IntersectionObserver((n,r)=>{let{subscribers:o}=Ut.get(t);n.forEach(i=>{let s=o.get(i.target);if(s)for(let a of s.values())a(i)})},e),Rs=t=>{let{key:e,options:n}=Ps(t);return Ut.has(e)||Ut.set(e,{observer:Ms(e,n),subscribers:new Map,key:e}),Ut.get(e)},zs=(t,e,n)=>{if(typeof window.IntersectionObserver=="undefined")return function(){};let{observer:r,subscribers:o,key:i}=Rs(n);return o.has(t)||(o.set(t,new Set),r.observe(t)),o.get(t).add(e),()=>{o.get(t).delete(e),o.get(t).size<=0&&(o.delete(t),r.unobserve(t)),o.size<=0&&(r.disconnect(),Ut.delete(i))}};var un=0,fn="--x-body-scroll-bar-size",Fs=`var(${fn}, 0)`,Yt="--x-body-scroll-active-bar-size",dn=`var(${Yt}, 0)`,qs=(t=0,e=850,n=null,r=()=>{},o=window)=>{let i=Z(typeof t=="function"?t(0):t),s=pt(n),a=Z(e),c=o.scrollY||document.documentElement.scrollTop;return vr(o,c,s,a,r,i)},Bs=(t,e=0,n=850,r=null,o=()=>{},i=window)=>{let s=pt(r),a=Z(n),c=i.scrollY||document.documentElement.scrollTop;return vr(i,c,s,a,o,function(){return pe(t)+Z(typeof e=="function"?e(0):e)})},he="auto",ye=!1,xe=t=>{t.target&&t.target.closest&&(t.target.closest("[data-x-scrollbar]")||t.target.closest(".x-off-canvas")||t.target.closest(".x-modal"))||(t.preventDefault(),t.stopPropagation())},$s=()=>{if(ye)return;ye=!0;let{adminBarOffset:t}=window.csGlobal;he=document.body.style.touchAction==="none"?he:document.body.style.touchAction,document.body.style.touchAction="none";let e=window.scrollY-t();document.body.style.top=-e+"px",document.body.classList.add("x-body-scroll-disabled"),window.addEventListener("wheel",xe,Dt),window.addEventListener("scroll",xe,Dt),jt(Yt,un+"px")},_s=()=>{if(!ye)return;ye=!1;let{adminBarOffset:t}=window.csGlobal;document.body.style.touchAction=he==="none"?"auto":he,document.body.classList.remove("x-body-scroll-disabled");let e=-(parseFloat(document.body.style.top)-t());document.body.style.top="",window.scrollTo({top:e}),setTimeout(function(){window.dispatchEvent(new CustomEvent("resize"))},250),window.removeEventListener("wheel",xe),window.removeEventListener("scroll",xe),jt(Yt,"0px")},Ds=(t,e=0,n=0,r)=>{let o=D(function(){r(wr(t,e,n))},25);return j([ct(o),Gt(o)])},wr=(t,e=0,n=0)=>{e===0&&(e=.01),n===0&&(n=.01);let{top:r,left:o,bottom:i,right:s}=t.getBoundingClientRect(),{innerHeight:a,innerWidth:c}=window,u=e?a*(1-parseFloat(e)/100):0,p=n?a*(parseFloat(n)/100):a;return r<=u&&o>=0&&i>=p&&s<=c};V(function(){un=window.innerWidth-document.body.offsetWidth,jt(fn,un+"px"),jt(Yt,"0px")});function jt(t,e){document.querySelector(":root").style.setProperty(t,e)}function vr(t,e,n,r,o,i=null){let s=t===window;return me(a=>{let c=typeof i=="function"?i():i;if(a<r){let u=e+(c-e)*n(a/r);t.scrollTo(0,u),s&&document.body.scrollTo(0,u)}else return t.scrollTo(0,c),s&&document.body.scrollTo(0,c),o(),!1})}function br(t,e=null){return e?t.style.setProperty("transition-property",e,"important"):t.style.setProperty("transition","none","important"),t.style.setProperty("animation","none","important"),()=>{t.offsetHeight,t.style.removeProperty(e?"transition-property":"transition"),t.style.removeProperty("animation")}}var Sr=(t,e)=>(n,{after:r}={})=>{t(n);let o=br(n);return()=>{e(n),o(),typeof r=="function"&&r()}},Ns=Sr(t=>t.style.setProperty("opacity",1,"important"),t=>t.style.removeProperty("opacity")),Ws=(t,{animation:e,className:n,timeout:r,remove:o},i=()=>{})=>{if(!e)return;n&&!t.classList.contains(n)&&t.classList.add(n),t.style.removeProperty("animation-duration"),t.style.setProperty("animation-name",e);let s=r?en(t).animationTime:0;t.csAnimationEndingTimeout&&clearTimeout(t.csAnimationEndingTimeout);let a=_t(()=>{o&&(t.csAnimationEndingTimeout=setTimeout(function(){t.style.animationName===e&&t.style.setProperty("animation-name","")},250)),i()});Tt(t,"animationend",a),r&&setTimeout(a,s)};function Vs(t,e,n){e=typeof e=="number"?Lt(e.toString()):typeof e=="string"?Lt(e):e;let r=(o,i,s,a)=>{let c,u=i[a];if(i.length>a){if(Array.isArray(o))try{u=pn(u,o),c=o.slice()}catch(p){if(o.length===0)c={};else throw new Error(p)}else c=Object.assign({},o);return c[u]=r(o[u]!==void 0?o[u]:{},i,s,a+1),c}return typeof s=="function"?s(o):s};return r(t,e,n,0)}function Gs(t,e,n){e=typeof e=="number"?Lt(e.toString()):typeof e=="string"?Lt(e):e;for(var r=0;r<e.length;r++){if(t===null||typeof t!="object")return n;let o=e[r];Array.isArray(t)&&o==="$end"&&(o=t.length-1),t=t[o]}return typeof t=="undefined"?n:t}function Us(t,e){e=typeof e=="number"?Lt(e.toString()):typeof e=="string"?Lt(e):e;let n=(r,o,i)=>{let s,a=o[i];return r===null||typeof r!="object"||!Array.isArray(r)&&r[a]===void 0?r:o.length-1>i?(Array.isArray(r)?(a=pn(a,r),s=r.slice()):s=Object.assign({},r),s[a]=n(r[a],o,i+1),s):(Array.isArray(r)?(a=pn(a,r),s=[].concat(r.slice(0,a),r.slice(a+1))):(s=Object.assign({},r),delete s[a]),s)};return n(t,e,0)}function pn(t,e){if(t==="$end"&&(t=Math.max(e.length-1,0)),!/^\+?\d+$/.test(t))throw new Error(`Array index '${t}' has to be an integer`);return parseInt(t)}function Lt(t){return t.split(".").reduce((e,n,r,o)=>{let i=r>0&&o[r-1];if(i&&/(?:^|[^\\])\\$/.test(i)){let s=e.pop();e.push(s.slice(0,-1)+"."+n)}else e.push(n);return e},[])}var mn={get:Gs,set:Vs,deleteProperty:Us};var Tr=(t,e)=>e&&typeof t=="object"?mn.get(t,qt(e)):t,Ys=(t,e,n)=>e&&typeof t=="object"?{...t,[qt(e)]:n}:n;function gt(t,e){if(Array.isArray(e))return e.map(i=>gt(t,i));if(typeof e=="function")return gt(t,e(t));if(typeof e=="object")return Object.keys(e).reduce((i,s)=>(i[s]=gt(t,e[s]),i),{});if(typeof e!="string")return e;let n,r=()=>(n||(n=window.getComputedStyle(t)),n),o=rn.get(t);return e.replace(/var\(([\w-]+)(?:\s*,\s*(.+?))?\)/g,(i,s,a="")=>r().getPropertyValue(s)||a).replace(/attr\(([\w-]+)(?:\s*,\s*(.+?))?\)/g,(i,s,a="")=>t.getAttribute(s)||a).replace(/meta\(([.\w-]+)(?:\s*,\s*(.+?))?\)/g,(i,s,a="")=>{let c=mn.get(o,s);return typeof c=="undefined"?a:c})}var js=(t,e,n)=>{let r=t,o=e;return n==="IN"?r.includes(o):n==="NOT IN"?!r.includes(o):((typeof r=="boolean"||typeof o=="boolean")&&(r=!!r,o=!!o),n===">"||n==="<"?r>o:n===">="||n==="<="?r>=o:n==="="||n==="=="?r==o:n==="!="?r!=o:n=="==="?r===o:n=="!=="?r!==o:!0)},gn=(t,e)=>{if(typeof t=="undefined")return null;if(!Array.isArray(t))return gn([t,"==",e]);let[n,r,o]=t;return t.length<=0?null:[n,r,o||e]},Qs=(t,e,n,r)=>{try{let o=gn(e,r);if(!o)return!0;let[i,s,a]=o;return js(Tr(n,gt(t,a)),gt(t,i),gt(t,s))}catch(o){console.warn("Failed to check condition. Make sure your state contains the key you are checking",o,{el:t,condition:e,state:n})}return!1};var Qt=new Map,hn=new Map,we=(...t)=>void console.warn(...t),it=(t,e)=>{if(Qt.has(t))return we("Rivet archetypes can not be redefined");hn.set(t,typeof e=="function"?e:(n,...r)=>n(...r))},ht=(t,e,n,r={})=>{if(Qt.has(e))return we(`Rivet ${e} already registered`);if(!hn.has(t))return we("Rivet archetype unknown",t);Qt.set(e,{archetype:t,handler:n,config:r})};function ve({el:t,handle:e,handles:n,defaultOption:r,options:o}){var l;let i=n||[e],s=i.find(({type:m})=>m&&Qt.has(m));if(!s)return we(`Unknown rivet type: ${(l=i[0])==null?void 0:l.type}`,t);let{handler:a,config:{defaultOption:c,priority:u=0,...p}={},archetype:f}=Qt.get(s.type),d=c&&r?{[c]:r}:{};return[u,()=>hn.get(f)(a,t,{...d,...o||{}},s.name,p)]}function be(t){return t.sort(([e],[n])=>e-n)}var yn="data-rvt";function Ar(t=window.document.body){return de(xn)(t)}function xn(t){if(t.hasAttributes()){let e=t.attributes;for(let n=e.length-1;n>=0;n--)if(e[n].name.indexOf(yn)===0)return!0}return!1}function Js(t,e){let n=Et(t,e);return typeof n!="object"&&typeof n!="undefined"&&n!==""?{defaultOption:n,options:{}}:{options:n}}function Ks(t){let e=new Set;for(let n=t.attributes.length-1;n>=0;n--){let r=t.attributes[n].name;if(r.indexOf(yn)!==0)continue;let o=r.substr(yn.length+1),i=o.split("-");e.add({el:t,handles:[{type:o,name:"default"},{name:i.pop(),type:i.join("-")}],...Js(t,r)})}return e}function wn(t){return j(be(Array.from(Ks(t)).map(e=>ve(e)).filter(e=>!!e)).map(([,e])=>e()))}function Er(t,e){try{return t&&t.matches&&t.matches(e)}catch{}return!1}function Se(t){try{return window.document.querySelectorAll(t)}catch(e){console.warn(e)}return[]}var Cr=!1,Lr=null,kr,vn=new Set,bn=new Set,Te=new WeakMap,kt=new WeakMap;function S(t,e,n=0){if(typeof t!="string"){console.warn("Rivet selector must be a string",t),console.trace();return}if(typeof e!="function"){console.warn("Rivet handler must be a function",e),console.trace();return}let r={handler:e,selector:t,priority:n};vn.add(r),Cr&&Zs(r)}function Zs(t){clearTimeout(kr),bn.add(t),kr=setTimeout(()=>{let e=Array.from(bn.values());bn.clear(),Or(e)},0)}function Or(t){t.sort(({priority:e},{priority:n})=>e-n),t.forEach(({selector:e,handler:n})=>{Array.from(Se(e)).forEach(r=>{Ae(r,n,Ir(e,r))})})}function Ir(t,e){let n=t.match(/(data-[\w-]+)/g)||[];return n&&n.length?n=n.pop():n=null,n?Et(e,n):null}function Ae(t,e,n){try{if(Xs(t,e)||!document.body.contains(t))return;ta(t,e);let r=Nt(e.call(window,t,n));Array.isArray(r)&&r.map(o=>{Hr(t,o)}),typeof r=="function"&&Hr(t,r)}catch(r){console.warn("Failed to attach handler to element",t,e,n,r)}}function Hr(t,e){typeof e=="function"&&(Te.get(t)||Te.set(t,new Set),Te.get(t).add(e))}function Xs(t,e){return kt.get(t)&&kt.get(t).get(e)}function ta(t,e){kt.get(t)||kt.set(t,new WeakMap),kt.get(t).set(e,!0)}window.document.addEventListener("DOMContentLoaded",()=>{Ar().forEach(t=>{Ae(t,e=>wn(e))}),Or(Array.from(vn.values())),Lr=new MutationObserver(function(t){t.reduce((e,n)=>{for(let r=0;r<n.addedNodes.length;r++)n.addedNodes[r].nodeType===1&&e.push(n.addedNodes[r]);return e},[]).forEach(function e(n){if(!!n){if(n.children&&n.children.length>0)for(let r=0;r<n.children.length;r++){if(!n)return;e(n.children[r])}xn(n)&&Ae(n,r=>wn(r)),vn.forEach(({selector:r,handler:o})=>{n&&Er(n,r)&&Ae(n,o,Ir(r,n))})}}),t.reduce((e,n)=>{for(let r=0;r<n.removedNodes.length;r++){let o=n.removedNodes[r];o.nodeType===1&&!document.contains(o)&&e.push(o)}return e},[]).forEach(function e(n){if(n.children&&n.children.length>0)for(let o=0;o<n.children.length;o++)e(n.children[o]);let r=Te.get(n);if(r)for(let o of r.values())o.call(window,n),r.delete(o),kt.delete(n)})}),Lr.observe(window.document.body,{childList:!0,subtree:!0}),Cr=!0});var J={};Ue(J,{container:()=>C,initState:()=>ua,makeDetectStateChange:()=>Mr,makeDispatch:()=>ia,makeInspect:()=>oa,subscribe:()=>sa});var C={providers:new Map,subscribers:new Map,relationships:new Map,providerIndex:new WeakMap,subscriberIndex:new WeakMap},Pr=(()=>{let t=0;return()=>t++})();function Mr(t){let e={};return n=>{let r=t.filter(o=>e[o]!==n[o]);return t.forEach(o=>{e[o]=n[o]}),r}}var Rr=(t,e)=>{var n,r;return(r=(n=C.subscriberIndex.get(t))==null?void 0:n.get(e))==null?void 0:r.id},zr=t=>C.providers.get(C.relationships.get(t)),Fr=(t,e)=>zr(Rr(t,e)),ea=(t,e)=>C.providerIndex.has(t)&&C.providerIndex.get(t).has(e),qr=(t,e)=>{let n=tn(r=>ea(r,e))(t);return n?C.providerIndex.get(n).get(e):null},Ot=new WeakMap;window.addEventListener("rvt-store-provider",()=>{Ot=new WeakMap});var Br=(t,e)=>(Ot.get(t)||Ot.set(t,{}),Ot.get(t).name||(Ot.get(t).name=C.providers.get(qr(t,e))),Ot.get(t).name),$r=(t,e=!1)=>{let n=zr(t);if(!n)return;let r=C.subscribers.get(t);if(!!r)for(let o of r.values()){let[i,s]=o;i(n.state,s(n.state),e)}},na=(t,e,n)=>{let r,o=()=>{let s=C.relationships.get(t),a=qr(e,n);s!==a&&(C.relationships.set(t,a),clearTimeout(r),r=setTimeout(()=>$r(t,!0),10))},i=X(window,"rvt-store-provider",o);return o(),()=>{clearTimeout(r),i()}},ra=(t,e)=>[typeof t=="function"?t:()=>{},Mr(Array.isArray(e)?e:[])],oa=(t,e)=>()=>{var n;return(n=Br(t,e))==null?void 0:n.state},ia=(t,e)=>n=>{var r;return(r=Br(t,e))==null?void 0:r.dispatch(n)},sa=(t,e,n=()=>{},r=[])=>{let o=ra(n,r);if(C.subscriberIndex.has(t)||C.subscriberIndex.set(t,new Map),!C.subscriberIndex.get(t).has(e)){let s=Pr();C.subscribers.set(s,new Set),C.subscriberIndex.get(t).set(e,{id:s,teardown:na(s,t,e)})}return C.subscribers.get(Rr(t,e)).add(o),{unsubscribe:()=>{let{id:s,teardown:a}=C.subscriberIndex.get(t).get(e),c=C.subscribers.get(s);c.delete(o),c.size===0&&(C.subscribers.delete(s),C.relationships.delete(s),C.subscriberIndex.get(t).delete(e),a())},getState:()=>{var s,a;return(a=(s=Fr(t,e))==null?void 0:s.state)!=null?a:{}},dispatch:s=>{var a;return(a=Fr(t,e))==null?void 0:a.dispatch(s)}}},aa=t=>typeof t!="function"?e=>e:(...e)=>t(...e),ca=t=>{let e;return n=>{let{state:r,...o}=C.providers.get(t);C.providers.set(t,{...o,state:o.reducer(n(r))}),cancelAnimationFrame(e),e=requestAnimationFrame(()=>{for(let[i,s]of C.relationships)s===t&&$r(i)})}},la=(t,e,{_reducer:n,...r})=>{if(C.providerIndex.get(t)||C.providerIndex.set(t,new Map),C.providerIndex.get(t).has(e))return;let o=aa(n),i=Pr();return C.providers.set(i,{reducer:o,state:o(r),dispatch:ca(i)}),C.providerIndex.get(t).set(e,i),window.dispatchEvent(new CustomEvent("rvt-store-provider")),()=>{C.providers.delete(i),C.providerIndex.get(t).delete(e)}},ua=(t,e={},n=window.document.documentElement)=>{if(!t){console.warn("States must set an ID",t,e,n);return}return la(n,t,e)};var _r=Wt(!1);function Jt(t){return _r.has(t)}Jt.enable=function(t){_r.set(t,!0)};var yt=(...t)=>ht("action",...t),tt=(...t)=>ht("observer",...t),xt=(...t)=>ht("behavior",...t),Kt=(...t)=>ht("innate",...t),Sn=(...t)=>ht("macro",...t),U=(...t)=>ht("event",...t);function fa(t,e,n={},r="default"){let o={el:t,handle:{type:e,name:r}};return typeof n=="string"?(o.defaultOption=n,o.options={}):o.options=n,ve(o)}function Zt(t){return j(be(t.filter(e=>!!e).map(e=>fa(...e)).filter(e=>!!e)).map(([,e])=>e()))}var{rivetListener:da,rivetDispatch:pa,expandElementValue:Dr,onScanLazy:ma}=b;it("behavior");it("innate");it("macro",(t,e,n,r)=>t(e,Zt,n,r));it("action",(t,e,n,r)=>{let o=()=>void t(e,Dr(e,n));return da(e,r,i=>{n.defer?setTimeout(o,0):o()})});it("event",(t,e,n,r)=>t(()=>pa(e,r),Dr(e,n),e));it("observer",(t,e,n,r="",{scan:o})=>{let[i,s=[]]=(typeof t=="function"?[t]:t)||[],a=i(e,n),[c,u]=Array.isArray(a)?a:[a,()=>{}],{unsubscribe:p,getState:f}=J.subscribe(e,r,c,s),d=o?ma(()=>c(f(),[],!1,!0)):()=>{};return[p,u,d]});var{expandElementValue:ga,getStateValue:ha}=b;tt("outlet",(t,{key:e})=>{let n=t.innerHTML;return r=>{try{let o=ha(r,ga(t,e));t.innerHTML=typeof o=="undefined"?n:o}catch(o){console.warn("Unable to update Rivet outlet",o,{key:e,state:r,el:t})}}},{defaultOption:"key"});var{listener:Nr}=b,ya=["click","focus","focusin","focusout","blur"];ya.forEach(t=>{U(`on${t}`,(e,{preventDefault:n=!0,stopPropagation:r=!1,once:o=!1},i)=>{let s;return Nr(i,t,a=>{o&&s||(s=!0,n&&a.preventDefault(),r&&a.stopPropagation(),e())})})});var xa=["keydown","keyup"];xa.forEach(t=>{U(`on${t}`,(e,{key:n,preventDefault:r=!0,stopPropagation:o=!1,once:i=!1},s)=>{let a;return Nr(document,t,c=>{i&&a||(a=!0,c.key===n&&(r&&c.preventDefault(),o&&c.stopPropagation(),e()))})},{defaultOption:"key"})});var{throttle:wa,debounce:va,onLoad:ba,onScanLazy:Sa,triggerScan:Ta,listener:Ee}=b;U("onready",t=>{setTimeout(()=>void t(),0)});U("onload",t=>ba(t));U("onexit",(t,{delay:e=1e3,repeat:n=!1})=>{let r,o=!1;return Ee(document,"mouseout",i=>{clearTimeout(r),!i.toElement&&!i.relatedTarget&&!o&&(r=setTimeout(()=>void t(),e),n||(o=!0))})},{defaultOption:"delay"});U("onresize",(t,{throttle:e=50})=>Ee(window,"resize",wa(t,e,{trailing:!0}),W),{defaultOption:"throttle"});U("onresized",(t,{debounce:e=500})=>Ee(window,"resize",va(t,e,{trailing:!0}),W),{defaultOption:"debounce"});U("onscan",(t,e)=>Sa(t,e),{defaultOption:"throttle"});S("img",t=>Ee(t,"load",()=>void Ta()));var{animateTopOffset:Aa,makeDirectionalEasing:Ea,intersect:Ca}=b,Wr,Vr,Ce=!1,Le=new Map;function Gr(){Wr=window.innerHeight}window.addEventListener("resize",Gr);Gr();function La(t,{prop:e,easingFn:n}){let{top:r,height:o}=t.getBoundingClientRect(),i=r+o/2,s=Wr/2;t.style.setProperty(e,n((i-s)/s))}function Ur(){if(!!Ce){for(let[t,e]of Le)La(t,e);Vr=requestAnimationFrame(Ur)}}var ka=(t,e)=>{Le.set(t,e),!Ce&&(Ce=!0,Vr=requestAnimationFrame(Ur))},Yr=t=>{Le.delete(t),Le.size<=0&&(Ce=!1)};xt("intersect",(t,{easing:e="linear",prop:n="--rvt-intersect"})=>[Ca(t,({isIntersecting:o})=>{o?ka(t,{easingFn:Ea(e),prop:n}):Yr(t)},{threshold:0,top:"0px",bottom:"0px"}),()=>void Yr(t)],{defaultOption:"prop"});yt("scroll-to-top",(t,{offset:e,speed:n,easing:r="easeInOutExpo"})=>{Aa(e,n,r)},{defaultOption:"offset"});Sn("scroll-top",(t,e)=>e([[t,"onclick"],[t,"scroll-to-top"]]));var{ensureNumber:jr,updateStateKey:Qr,getStateValue:Oa,expandElementValue:ke}=b;Kt("define",(t,e={},n)=>{if(!n.match(/^\w+$/)){console.warn("Rivet state keys must be alphanumeric");return}let{_reducer:r,...o}=e.__value||e||{};J.initState(n,{_reducer:r,...ke(t,o)},t)},{defaultOption:"__value",priority:-1});yt("set",(t,{state:e,key:n,value:r})=>{J.makeDispatch(t,e)(o=>Qr(o,ke(t,n),r))});var Ia=(t,e,n)=>{let r=t.includes(e);return n&&r?t.filter(o=>o!==e):r?t:[...t,e]};yt("list",(t,{state:e,key:n,value:r,toggle:o=!0})=>{J.makeDispatch(t,e)(s=>{let a=ke(t,n),c=Oa(s,a);return Array.isArray(c)?Qr(s,a,Ia(c,r,o)):s})});yt("inc",(t,{state:e,key:n,amount:r=1,min:o=null,max:i=null,wrap:s=!1})=>{let a=J.makeDispatch(t,e),c=u=>jr(u)+jr(r);a(u=>{let p=ke(t,n);return p?{...u||{},[p]:c(u[p])}:c(u)})},{defaultOption:"state"});Kt("debug",t=>{Jt.enable(t),t.removeAttribute("data-rvt-debug")},{defaultOption:"message"});var{isScalar:Jr,getTransitionDuration:Ha,getStateValue:An,expandElementValue:Oe,evaluateCondition:Ie,listener:Pa,fontCompress:Ma,addClass:Kr,removeClass:Ra}=b,za=(t,e)=>{let n=t||"$v";return Jr(n)?Jr(e)?`${n}`.replace("$v",e):n==="$v"?"":n:""};tt("classname",(t,{key:e,classname:n,condition:r})=>{let o="";return i=>{let s=An(i,Oe(t,e)),c=Ie(t,r,i,e)?za(n,s):"";c!==o&&(o&&t.classList.contains(o)&&t.classList.remove(o),c&&!t.classList.contains(c)&&t.classList.add(c)),o=c}},{defaultOption:"classname"});tt("prop",(t,{key:e,prop:n,value:r,condition:o})=>{let i=null;return s=>{let a=An(s,Oe(t,e));Ie(t,o,s,e)?a!==i&&t.style.setProperty(n,typeof r=="undefined"?a:r):a!==i&&t.style.removeProperty(n),i=a}},{defaultOption:"key"});tt("attr",(t,{key:e,attr:n,value:r,condition:o})=>{let i=null;return s=>{let a=An(s,Oe(t,e));Ie(t,o,s,e)?a!==i&&t.setAttribute(n,typeof r=="undefined"?a:r):a!==i&&t.removeAttribute(n),i=a}},{defaultOption:"key"});tt("height",(t,{key:e,condition:n,selector:r})=>{let o,i;return(s,a,c,u)=>{if(e&&!u){let f=Oe(t,e);if(s[f]===o)return;o=s[f]}let p=Ie(t,n,s,e);setTimeout(()=>{if(p){let[f,...d]=Array.from(t.querySelectorAll(r)).map(l=>l.offsetHeight).sort((l,m)=>m-l);f&&f!==i&&(t.style.setProperty("height",`${f}px`,"important"),i=f)}else t.style.removeProperty("height"),i=null})}},{defaultOption:"selector",scan:!0});window.offscreenTemplates||(window.offscreenTemplates=new WeakMap);xt("offscreen-reset",(t,{mode:e="default"})=>{let n=t.closest("[data-x-toggleable]");if(window.offscreenTemplates.get(t))return;try{let c=document.createElement("textarea");c.innerHTML=t.querySelector('script[type="text/rvt-template"]').textContent;let u=document.createElement("div");u.innerHTML=c.innerText,window.offscreenTemplates.set(t,[c.innerText,u])}catch(c){return console.warn("Unable to locate content template",c),()=>{}}let r,o=()=>{try{let[c,u]=window.offscreenTemplates.get(t);Array.from(u.querySelectorAll("[data-x-toggleable]")).map(f=>f.getAttribute("data-x-toggleable")).forEach(f=>{window.xToggleDelete(f)}),t.innerHTML=c}catch(c){console.warn("Unable to reset offscreen content",c)}},i=()=>{t.innerHTML=""},s=()=>{r=setTimeout(()=>{i(),e==="close"&&o()},Ha(n,300)+100)},a=c=>{clearTimeout(r),c?(e==="open"&&i(),o()):e!=="open"&&s()};return e==="close"&&o(),Pa(n,"tco-toggle",({detail:{state:c}={}})=>void a(c))},{defaultOption:"mode"});xt("font-compress",(t,e)=>Ma(t,e));var Fa=(t,e)=>{try{if(e)return Array.from(t.querySelectorAll(e))}catch{}return t};xt("inner-wrap",(t,{selector:e="",tag:n="span",class:r=""})=>Fa(t,e).map(o=>{let i=document.createElement(n);Kr(i,"has-been-tagged"),r&&Kr(i,r),Array.from(o.childNodes).forEach(s=>{i.appendChild(s)}),o.append(i),i.offsetHeight,Ra(i,"has-been-tagged")}),{defaultOption:"selector"});var En={...Tn};var{toggleClass:Zr,listener:Bf,PASSIVE_ARGS:qa}=b;S("[data-x-bar]",(t,{scrollButtons:e})=>{if(!e)return;let n=t.querySelector(".x-bar-scroll-inner"),r=t.querySelector(".x-bar-content"),o=t.querySelector('[data-x-bar-scroll-button="bck"]'),i=t.querySelector('[data-x-bar-scroll-button="fwd"]'),s=0,a=0,c=0,u=0,p=()=>{let g=parseInt(window.getComputedStyle(n).width),h=parseInt(window.getComputedStyle(n,":before").width),x=parseInt(window.getComputedStyle(r).width);s=g,a=g-h*2,c=x,u=n.scrollLeft;let y=u<=0,w=c-u-a<=0;Zr(o,"is-active",!y),Zr(i,"is-active",!w)},f=g=>n.scrollTo({top:0,left:g,behavior:"smooth"}),d=()=>{f(Math.max(u-s,0))},l=()=>{f(Math.min(u+s,c-a))};p();let m=D(p,50);window.addEventListener("resize",m),n.addEventListener("scroll",m,qa),o.addEventListener("mouseup",function(){d()}),i.addEventListener("mouseup",function(){l()})});var st={},It={};function Ba(t,e){st[t]||(st[t]=[]),st[t].push(typeof e=="function"?e:()=>e)}function $a(t,e){if(!st[t])return;let n=st[t].indexOf(e);st[t].splice(n,1)}function _a(t,e){It[t]||(It[t]=[]),It[t].push(e)}function Da(){let t=[...arguments],e=t.shift(),n=t.shift(),r=st[e]?st[e]:[];return(It[e]?It[e]:[]).forEach(i=>i.call(this,n,...t)),r.reduce((i,s)=>s.call(this,i,...t),n)}var _={filter:Ba,action:_a,apply:Da,filters:st,actions:It,remove_filter:$a};var{animateTopOffset:Na,scrollOffset:Wa}=b,Ht,Xr=!1,Cn;function to(){if(!Xr){Ht=0;let t=D(to,50);window.addEventListener("resize",t,W),Cn=document.querySelector("#wpadminbar"),Xr=!0}if(Cn){let{position:t,height:e}=window.getComputedStyle(Cn);Ht=t==="fixed"?parseInt(e):0}return Ht}var Y=()=>Ht!=null?Ht:to(),Pt=()=>_.apply("fixed_top_offset",0),Va=(t,e,n)=>{if(t instanceof Element){let{bottom:r,top:o}=Wa(t);return e?r-n:o+n}return(typeof t=="number"?t:parseFloat(t))+n},eo=(t,{offsetTop:e=!0,duration:n,easing:r,bottom:o=!1}={})=>{Na(()=>Va(t,o,e?Pt():0),n,r)};window.csGlobal=window.csGlobal||{};window.csJsData=window.csJsData||{};window.csGlobal.rivet=En;window.csGlobal._=window.csGlobal.rivet.util;window.csGlobal.everinit=En.attach;window.csGlobal.adminBarOffset=Y;window.csGlobal.fixedTopOffset=Pt;window.csGlobal.scrollTo=eo;window.csGlobal.csHooks=_;window.document.documentElement.classList.remove("no-js");window.document.documentElement.classList.add("js");var{onScrollRaw:Ga,onResize:Ua,oncePassive:no,elementIndex:ro,addClass:Ln,removeClass:kn,toggleClass:rd,hasClass:wt}=b,oo=t=>getComputedStyle(t).display!=="none",io=0,so=!1,On=!1;function ao(t){var d,l,m;let e=document.querySelector(".x-site");if(!e){t.style.width="100%";return}var n=window.getComputedStyle(t);if(n.position!=="fixed"){t.style.left="",t.style.width="",t.style.maxWidth="";return}var r=[];co(n["margin-left"])||r.push(n["margin-left"]),co(n["margin-right"])||r.push(n["margin-right"]);var o="";r.length>0&&(o=r.length===1?r[0]:"("+r.join(" + ")+")");let i=(m=(l=(d=document.querySelector("body.x-stack-icon .x-sidebar .max.width:not(.x-container)"))==null?void 0:d.parentElement)==null?void 0:l.offsetWidth)!=null?m:0,s=Array.from(document.querySelectorAll(".x-bar-space-v")).reduce((g,h)=>g+h.offsetWidth,i),a=Array.from(document.querySelectorAll(".x-bar-left")).reduce((g,h)=>g+h.offsetWidth,i),c="";a&&(c="calc(0px + "+a+"px)");var u="";s>0&&(u+=" - "+s+"px"),o&&(u+=" - "+o),u+=" - "+dn,t.style.width="calc(100%"+u+")",t.style.left=c;var p=window.getComputedStyle(e),f=p["max-width"];f&&f!=="none"&&(t.style.maxWidth=o?"calc("+f+" - "+o+")":f)}function co(t){return t.trim().split(" ").filter(e=>!e.match(/^0[a-zA-Z%]+|0$|none$/)).length===0}S("[data-x-bar]",function(t,e){if(e.region==="top"||e.region==="bottom")return b.onViewportChange(()=>{ao(t)},!0)});var He=/__cs_debug/.test(location.href),lo=!1,Mt=[],et=[],Xt=[],In,te=!1;function Pe(t){let e=Mt.map(o=>o.id),n=!1,r=Mt.filter(({el:o,id:i})=>{let s=e.indexOf(i);return s!==e.lastIndexOf(i)?(n=!0,e.splice(s,1),!1):window.document.body.contains(o)?!0:(n=!0,!1)});return(n||t)&&(r=r.sort((o,i)=>ro(o.el)-ro(i.el))),n?(Mt=r,et=Mt,setTimeout(Hn,0),!0):!1}function Hn(){if(Pe()||!Y)return;et=Mt.filter(({el:o})=>oo(o));let t=Y(),e=0,n=0,r=0;Ja(),et.forEach(o=>{var{height:i}=o.el.getBoundingClientRect();o.height=i=Math.round(i),o.hasShrink=po(o.props.shrink),o.goal&&o.space&&(o.space.style.height=i+"px"),o.hasShrink&&!o.goal&&(o.shrinkHeight=fo(i,o.props.shrink)),o.hasShrink||(o.shrinkHeight=o.height),o.triggerOffset=parseInt(o.props.triggerOffset),isNaN(o.triggerOffset)&&(o.triggerOffset=0)}),Xt=et.map((o,i)=>{let{el:s,props:a,height:c,shrinkHeight:u,triggerOffset:p,goal:f}=o,d=r++===0,l=a.zStack&&!d,{marginTop:m}=getComputedStyle(s);var g=parseFloat(m),h=a.keepMargin?g:0,{top:x,bottom:y}=s.getBoundingClientRect();let w=0,O=s.parentNode.getBoundingClientRect().top-Y();O+=window.scrollY;let v=s.parentNode.childNodes;for(let G=0;G<v.length;++G){let rt=v[G];if(!!rt.getBoundingClientRect){if(rt===s)break;wt(rt,"x-bar-space")||wt(rt,"x-bar-is-sticky")||(w+=rt.getBoundingClientRect().height)}}x=O+p+(w-e)+Y();let E=p+w+O+Y(),H=document.body.scrollTop,F=H+x;if(a.triggerSelector)try{let G=document.querySelector(a.triggerSelector);if(G){let{top:rt}=G.getBoundingClientRect();E=rt+H+p-u}}catch(G){He&&console.warn(G)}else a.hideInitially?(E+=c,E+=p):p>0&&(E+=p+c);l?E+=u:E-=h;let R=Y()+e;Pn(E,"red");let L=t+n;a.keepMargin||(L+=g,R-=g),E=Math.floor(E),Pn(E,"green"),t=E,l?(R-=u+h,R=Math.max(R,Y())):e+=u+h;let P=a.hideInitially||F<E||F+c<R;n+=c-u;let B=u+e;Pn(B,"orange"),a.keepMargin&&(B+=g),B=Math.ceil(B);let ce=h?`calc( -100% - ${h}px)`:"-100%";return f&&R!==Xt[i].top&&(s.style.top=`${R}px`),{offset:E,bottom:y,top:R,slide:P,elOffset:F,topOffset:B,translateY:ce,startsFixed:o.startsFixed,firstBar:d,only_show_on_scroll_up:a.only_show_on_scroll_up}}),He&&et.forEach(function(o,i){console.log(`Bar: ${i}`,o)}),Me()}function Me(){if(te||Pe()||On||document.body.classList.contains("x-body-scroll-disabled"))return;On=!0;let t=window.scrollY+Y(),e=t-io,n=e===0?so:e<0&&window.scrollY!==0;io=t,so=n;let r=Xt.reduce((i,{offset:s,only_show_on_scroll_up:a,startsFixed:c},u)=>(c?t>=s:t>s)&&(!a||n)?u:i,-1),o=!1;et.forEach((i,s)=>{let a=i.goal;i.goal=r>=s,a!==i.goal&&(o=!0)}),o&&requestAnimationFrame(uo),On=!1}function uo(){if(te)return;let t=Ya();t?(te=!0,t(()=>{te=!1,uo()})):(te=!1,window.dispatchEvent(new CustomEvent("cs-sticky-bar-post-transition")),setTimeout(Me,0))}function Ya(){let t=-1,e=et.map(n=>{let{goal:r,el:o}=n,i=n.fixedOnce;return{goal:r,fixed:wt(o,"x-bar-fixed")&&i}});if(e.forEach(({goal:n,fixed:r},o)=>{let i=n===r;!i&&t===-1&&(t=o),!i&&o>0&&e[o-1].fixed&&Xt[o].slide&&(t=o)}),t!==-1){let n=et[t],r=n.goal;if(!r&&!wt(n.el,"x-bar-fixed"))return!1;let o=r?ja:Qa;return i=>o(et[t],Xt[t],{st:window.scrollY,done:i})}return!1}function ja(t,{top:e,slide:n,elOffset:r,translateY:o,firstBar:i,only_show_on_scroll_up:s},{st:a,done:c}){let{el:u,space:p,content:f,shrinkHeight:d,height:l}=t;t.fixedOnce=!0;let m={top:`${e}px`},g=n||!i&&r+d<a+e||s;if(t.slideEnabled||(g=!1),d&&l!==d){m.height=d;let{paddingTop:w,paddingBottom:T}=getComputedStyle(u);f.style.height=`calc(${d}px - (${w} + ${T}))`,u.style.height=`calc(${d}px - (${w} + ${T}))`}g&&(m.transform=`translate3d( 0, ${o}, 0)`);let h=()=>c();if(Object.entries(m).forEach(([w,T])=>{u.style.setProperty(w,T)}),p){p.style.display="";let w=l+"px";w!==p.style.height&&(p.style.height=w)}In.forEach(w=>void Ln(u,w)),kn(u,"x-bar-is-initially-hidden"),ao(u);let y=window.getComputedStyle(u)["transition-duration"];y=y?parseFloat(y.replace("s","")):0,g?(Ln(u,"x-bar-is-visible"),u.style.transform="",y!==0?no(u,"transitionend",h):c()):c()}function Qa({el:t,space:e,props:n,content:r,shrinkHeight:o,slideEnabled:i},{top:s,slide:a,elOffset:c,translateY:u,firstBar:p,only_show_on_scroll_up:f},{st:d,done:l}){let m=parseFloat(n.shrink),g=!1,h=a||!p&&c+o<d+s||f&&c+o<d+s;i||(h=!1);function x(){let T=window.getComputedStyle(t)["transition-duration"];if(T=T?parseFloat(T.replace("s","")):0,T===0){y();return}no(t,"transitionend",y),setTimeout(y,T*1e3+100)}n.hideInitially&&Ln(t,"x-bar-is-initially-hidden");function y(){g||(g=!0,r.style.height="",t.style.top="",t.style.transform="",t.style.height="",t.style.left="",t.style.width="",In.forEach(w=>kn(t,w)),kn(t,"x-bar-is-visible"),e&&(e.style.display="none"),l())}h?(t.style.transform=`translate3d( 0, ${u}, 0)`,x()):!isNaN(m)&&m<1?(r.style.height="",t.style.height="",x()):y()}function Ja(){!He||Se(".cs-sticky-bar-waypoint-debug").forEach(function(t){t.remove()})}function Pn(t,e="red",n="white"){if(!He)return;let r=`<div class="cs-sticky-bar-waypoint-debug" style="position:absolute;height:1px;width:100%;top:${t}px;border-top:1px solid ${e};z-index:999999"><span style="color: ${n};background-color: ${e};left: 0;position: absolute;top: 0; padding: .5em 1em; transform: translate3d(0,-50%,0);";>${t}</span></div>`,o=document.createElement("div");o.innerHTML=r,document.body.appendChild(o.childNodes[0])}function Ka(){let t=0;return et.forEach(function(e){var n;t+=e.goal&&!((n=e.props)==null?void 0:n.zStack)&&e.props.scrollOffset?e.height:0}),t}_.filter("fixed_top_offset",t=>Math.max(t,Ka()+Y()));var Mn=()=>{Pe(!0)||Hn()},Rn=()=>{Pe(!0),Hn()};function Za(){if(typeof window.ResizeObserver=="undefined")return;let t=window.document.body.clientHeight;new ResizeObserver(D(function(n){!n||document.body.classList.contains("x-body-scroll-disabled")||t!==window.document.body.clientHeight&&(t=window.document.body.clientHeight,Mn())},100)).observe(document.body)}S("[data-x-bar]",(t,e)=>{lo||(In=(wt(document.body,"x-boxed-layout-active")?"x-bar-fixed x-container max width":"x-bar-fixed").split(" "),V(Mn),Ua(Mn),Ga(Me),Za(),lo=!0);let{id:n,region:r}=e;if(!wt(t,"x-bar-is-sticky")||r!=="top")return;var{top:o,height:i}=t.getBoundingClientRect();i=Math.round(i);let s=document.querySelector(`.${n}.x-bar-space`);s&&(s.style.height=i+"px");let a={id:n,el:t,props:e,height:i,topOffset:i,startsFixed:wt(t,"x-bar-fixed"),slideEnabled:!!e.slideEnabled,fixedOnced:!1,shrinkHeight:fo(i,e.shrink),space:s,scrollOffset:e.scrollOffset,content:t.querySelector(".x-bar-content"),visible:oo(t),startingRectTop:o};return Mt.push(a),Rn(),setTimeout(function(){Rn()},1e3),function(){Rn(),Me()}});function fo(t,e){return po(e)?e*t:t}function po(t){return!isNaN(t)&&t>0&&parseFloat(t)!==1}var{listener:Xa,oncePassive:tc}=b;S('.x-alert [data-dismiss="alert"]',t=>Xa(t,"click",e=>{e.preventDefault();let n=t.parentElement;n.classList.remove("in"),n.classList.remove("x-effect-enter"),tc(n,"transitionend",()=>{if(window.csGlobal&&window.csGlobal.isPreview){n.style.display="none";return}n.remove()})}));var{util:ec}=window.csGlobal.rivet,{addClass:ee,removeClass:ne,hasClass:Re}=ec;function Rt(t,e){if(e){if(!t||Re(t,"x-anchor-layered-back"))return;ee(t,"x-active")}else ne(t,"x-active")}function zn(t,e){if(e){if(!t||Re(t,"x-active")||Re(t,"x-currently-active"))return;ee(t,"x-currently-active")}else ne(t,"x-currently-active")}function mo(t){setTimeout(()=>{let e=t.closest(".x-menu");e.addEventListener("transitionend",function(n){let r=e.getBoundingClientRect().top;if(r<0&&n.propertyName==="height"){let o=0,i=t.closest(".x-bar-v .x-bar-scroll-inner, .x-off-canvas-content, .x-modal")||window;if(i===window){let s=window.pageYOffset,a=parseInt(window.getComputedStyle(document.documentElement).marginTop),c=parseInt(window.getComputedStyle(e).fontSize);o=Math.max(0,r+s-a-c)}i.scrollTo({top:o,left:0,behavior:"smooth"})}})},0)}function nc(t){var e=t.offsetHeight,n=getComputedStyle(t);return e+=parseInt(n.marginTop)+parseInt(n.marginBottom),e}var go=t=>Math.max(t.offsetHeight,Array.from(t.children).reduce((e,n)=>e+nc(n)||0,0)),ho=(t,e)=>{let n=t.closest("[data-x-toggle-layered-root]");n&&(n.style.height=`${e}px`)};function rc(t){ho(t,go(t));let e=t.closest(`ul:not([data-x-toggleable="${t.getAttribute("data-x-toggleable")}"])`);ne(e,"x-current-layer"),ee(e,"x-prev-layer"),ee(t,"x-current-layer"),mo(t)}function oc(t){let e=t.closest(`ul:not([data-x-toggleable="${t.getAttribute("data-x-toggleable")}"])`);!Re(e,"x-prev-layer")||(ho(t,go(e)),ne(t,"x-current-layer"),ee(e,"x-current-layer"),ne(e,"x-prev-layer"),mo(t))}function yo(t,e){return e?rc(t):oc(t)}function xo(t,e,n){var r,o,i,s,a=!1,c=!1,u={},p=0,f=0,d={sensitivity:7,interval:100,timeout:0,handleFocus:!1};function l(v,E){return f&&(f=clearTimeout(f)),p=0,c?void 0:n.call(v,E)}function m(v){r=v.clientX,o=v.clientY}function g(v,E){if(f&&(f=clearTimeout(f)),Math.abs(i-r)+Math.abs(s-o)<d.sensitivity)return p=1,c?void 0:e.call(v,E);i=r,s=o,f=setTimeout(function(){g(v,E)},d.interval)}u.options=function(v){var E=v.handleFocus!==d.handleFocus;return d=Object.assign({},d,v),E&&(d.handleFocus?T():O()),u};function h(v){return a=!0,f&&(f=clearTimeout(f)),t.removeEventListener("mousemove",m,!1),p!==1&&(i=v.clientX,s=v.clientY,t.addEventListener("mousemove",m,!1),f=setTimeout(function(){g(t,v)},d.interval)),this}function x(v){return a=!1,f&&(f=clearTimeout(f)),t.removeEventListener("mousemove",m,!1),p===1&&(f=setTimeout(function(){l(t,v)},d.timeout)),this}function y(v){a||(c=!0,e.call(t,v))}function w(v){!a&&c&&(c=!1,n.call(t,v))}function T(){t.addEventListener("focus",y,!1),t.addEventListener("blur",w,!1)}function O(){t.removeEventListener("focus",y,!1),t.removeEventListener("blur",w,!1)}return u.remove=function(){!t||(t.removeEventListener("mouseover",h,!1),t.removeEventListener("mouseout",x,!1),O())},t&&(t.addEventListener("mouseover",h,!1),t.addEventListener("mouseout",x,!1)),u}var{util:ic}=window.csGlobal.rivet,{addClass:Fn,transitionEnd:wo,removeClass:qn,getOuterHeight:vo,makeAlternatingSynchronizer:sc}=ic,ac=t=>sc(e=>{if(!t.classList.contains("x-collapsed")){e();return}t.setAttribute("aria-hidden","false");let n=vo(t);qn(t,"x-collapsed"),Fn(t,"x-collapsing"),t.offsetHeight,t.style.height=`${n}px`,wo(t,()=>{qn(t,"x-collapsing"),t.style.height="",e()})},e=>{t.setAttribute("aria-hidden","true"),t.style.height=`${vo(t)}px`,Fn(t,"x-collapsing"),t.offsetHeight,t.style.height="",wo(t,()=>{qn(t,"x-collapsing"),Fn(t,"x-collapsed"),e()})},void 0,!t.classList.contains("x-collapsed")),Bn=new WeakMap,cc=t=>(Bn.has(t)||Bn.set(t,ac(t)),Bn.get(t));function bo(t,e){cc(t)(e)}var{util:So}=window.csGlobal.rivet,{listener:lc,debounce:To,addClass:uc,removeClass:fc,hasClass:lt,toggleClass:K,oncePassive:Ao,scrollingDisable:dc,scrollingEnable:Eo}=So,ze=null,Fe=t=>document.querySelector(`[data-x-toggleable="${t}"][data-x-toggle]`),pc=t=>document.querySelector(`[data-x-toggleable="${t}"]:not([data-x-toggle])`),Co=t=>t?t.parentElement.matches(".x-nav-tabs-item")?"classic-tab":t.getAttribute("data-x-toggle"):null,mc=t=>["tab","classic-tab"].includes(Co(t)),$n=t=>t&&t.getAttribute("data-x-toggle-group"),gc=t=>!!document.querySelector(`[data-x-toggle-group="${t||""}"].x-active`),ut=(t,e)=>t&&(t.matches(e)?t:t.closest(e)),hc=(t,e)=>t&&(t.matches(e)?t:t.querySelector(e)),yc=t=>ut(t,"[data-x-toggle]"),Lo=t=>ut(t,"[data-x-toggleable]"),Q=t=>t&&t.getAttribute("data-x-toggleable"),qe=t=>t&&t.getAttribute("data-x-toggle-hash"),xc=t=>t&&t.matches(".mce-content-body"),wc=D(function(){window.dispatchEvent(new Event("resize"))},250),vc=D(function(){window.dispatchEvent(new Event("rvt-scan"))},250);function bc(t,e=""){return document.querySelector(`[data-x-toggleable=${t}]${e}`)}var Sc=t=>{let{marginTop:e,marginBottom:n}=getComputedStyle(t);return t.offsetHeight+parseInt(e)+parseInt(n)},re=()=>window.location.hash.replace("#",""),ko=t=>document.querySelectorAll(`[data-x-toggle-group="${$n(t)}"]:not([data-x-toggleable="${Q(t)}"])`),Tc=t=>document.querySelectorAll(`[data-x-toggle-group="${$n(t)}"].x-active`);So.toggle={getOthersInGroup:ko,getActiveInGroup:Tc};var Ac=t=>{let e=yc(t);return[e,Q(e)]},Oo=t=>Array.from(document.querySelectorAll("[data-x-toggle-hash]")).filter(e=>qe(e)===t).map(Q),oe=t=>ut(t,"[data-x-toggleable]:not([data-x-toggle])"),Ec=30,Be=(t,e)=>{let n=[],r,o=t,i=0;for(;r=o&&oe(o);){if(i>=Ec){console.warn("Broke toggleable ancestry depth limit ",t,n);break}let s=Q(r);if(o=Fe(s),n.includes(s))break;n.push(s),++i}return e&&n.shift(),n};window.TCOToggleStates||(window.TCOToggleStates=new Map,window.TCOToggleScrollBlocking=new Map);var Cc=!1,ft=window.TCOToggleStates,_n=window.TCOToggleScrollBlocking,dt=!1;function Lc(){let t=[];for(let[e,n]of ft){let r=pc(e);n&&_n.has(e)&&t.push(e),r||ft.delete(e)}t.find(e=>!ft.has(e))&&requestAnimationFrame(Eo)}window.integrityCheckTimeout||(window.integrityCheckTimeout=null);var kc=function(){clearTimeout(window.integrityCheckTimeout),window.integrityCheckTimeout=setTimeout(Lc,500)};window.xToggleIntegrityCheck||(window.xToggleIntegrityCheck=kc);function Oc(t){let e=Fe(t);switch(Co(e)){case"collapse":case 1:case"layered":return e.matches(".x-active");case"tab":return gc($n(e));case"classic-tab":return e.parentElement.matches(".active");case"collapse-b":return!e.matches(".collapsed")}return e?e.classList&&e.classList.contains("x-active"):null}function zt(t){return ft.has(t)||ft.set(t,Oc(t)),ft.get(t)}function N({id:t,state:e,_triggeringGroup:n,force:r,hashUpdate:o=!window.csGlobal.isPreview}){let i=zt(t);if(typeof e=="undefined"&&(e=!i),e&&window.xLastToggleable!==t)window.xLastToggleable=t,window.xToggleStack.push(t);else if(!e){let a=window.xToggleStack.indexOf(t);a!==-1&&window.xToggleStack.splice(a,1),window.xLastToggleable===t&&(window.xLastToggleable=window.xToggleStack[window.xToggleStack.length-1])}let s=Fe(t);!r&&!n&&(xc(s)||!e&&mc(s))||(ft.set(t,e),(i!==e||r)&&(Ic(t,e),o&&qc(s,e)),n||ko(s).forEach(a=>{N({force:r,id:Q(a),state:!1,_triggeringGroup:!0,hashUpdate:o})}))}window.xLastToggleable="";window.xToggleStack=[];window.xToggleGetState=t=>zt(t);window.xToggleGetStateFromNode=t=>zt(Q(t));window.xToggleUpdate=(t,e)=>N({id:t,state:e});window.xToggleDelete=t=>ft.delete(t);window.xToggleGetId=Q;window.xGetLastToggleable=function(){return window.xLastToggleable};var Io=!1;window.xToggleTempUnlock=()=>{Io=!!dt,dt=!1};window.xToggleTempRelock=()=>{dt=Io};window.xToggleSetLocking=t=>{dt=!!t};window.xToggleHashUpdate=Po;function Ic(t,e){Array.from(document.querySelectorAll(`[data-x-toggleable="${t}"]`)).forEach(n=>{n.dispatchEvent(new CustomEvent("tco-toggle",{bubbles:!1,detail:{state:e,id:t}}))})}function Ho(t){return lc(t,"tco-toggle",({currentTarget:e,detail:{state:n,id:r}})=>{var o;if(n||Hc(t),t.hasAttribute("data-x-toggle-overlay")&&(n?((o=document.querySelector(`[data-x-toggleable=${r}][role="dialog"]`))==null?void 0:o.hasAttribute("data-x-disable-body-scroll"))&&(requestAnimationFrame(dc),_n.set(r,!0)):n||(_n.delete(r),requestAnimationFrame(Eo))),t.hasAttribute("aria-hidden")&&t.setAttribute("aria-hidden",!n),t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded",n),t.hasAttribute("aria-selected")&&t.setAttribute("aria-selected",n),t.hasAttribute("data-x-toggle-collapse")?bo(t,n):t.hasAttribute("data-x-toggle-layered")?yo(t,n):lt(t,"x-anchor")?Rt(t,n):t.getAttribute("data-x-toggle")==="collapse-b"?K(t,"collapsed",!n):lt(t.parentElement,"x-nav-tabs-item")?K(t.parentElement,"active",n):lt(t,"x-tab-pane")?K(t,"active",n):lt(t,"x-dropdown")?$e(t,n):K(t,"x-active",n),lt(t,"x-modal")&&wc(),vc(),t.matches("[data-x-toggle]")){if(K(t.querySelector(".x-toggle"),"x-active",n),Cc)return;Array.from(t.querySelectorAll("[data-x-toggle-anim]")).forEach(i=>{lt(i,"x-running")||Ao(i,"animationiteration",()=>{fc(i,"x-running"),lt(t,"x-active")||i.removeAttribute("style")}),uc(i,"x-running"),lt(t,"x-active")&&(i.style.animationName=i.getAttribute("data-x-toggle-anim"))})}if(n&&!window.csGlobal.isPreview)if(e.querySelector("[data-x-search][data-x-search-autofocus]")){let i=e.querySelector("[data-x-search][data-x-search-autofocus] input");if(Bc(i,350),!cn())return;Ao(e,"transitionend",function(){i.scrollIntoView(!0)})}else{let i=t.querySelector('[tabindex="-1"]');i&&i.focus&&(i.focus(),setTimeout(function(){i.focus()},250))}})}function Hc(t){let e=t.querySelectorAll("[data-x-toggleable]")||[];for(let n=0;n<e.length;++n){let r=e[n];N({id:r.getAttribute("data-x-toggleable"),state:!1})}}var Pc=t=>{let e=oe(t);return!!(e&&(e.matches(".x-modal")&&!t.closest(".x-modal-content")||e.matches(".x-off-canvas")&&!t.closest(".x-off-canvas-content")))},Mc=(t,{exclude:e=[]}={})=>{if(dt)return;let n=[...Be(t,Pc(t)),...e].filter(r=>!!r);Array.from(document.querySelectorAll("[data-x-toggleable].x-dropdown, [data-x-toggleable].x-off-canvas, [data-x-toggleable].x-modal")).map(r=>r.getAttribute("data-x-toggleable")).filter(r=>!n.includes(r)).forEach(r=>N({id:r,state:!1}))};function Dn(){Po(re())}function Po(t){Oo(t).forEach(n=>{N({id:n,state:!0})})}var Rc=(t,e,n)=>{if(!t||e.isContentEditable)return!1;if(t.matches("[data-x-toggle-hover]")){if(dt)return!0;if(n)return!1}let r=t.querySelector("[data-x-toggle-nested-trigger]");return r?ut(e,"[data-x-toggle-nested-trigger]")===r:ut(e,"[data-x-toggle]")===t};var Nn=({ignoreHoverToggle:t=!0}={})=>e=>{let n=e.target,[r,o]=Ac(n);Rc(r,n,t)&&(ut(n,"a[href]")&&e.preventDefault(),N({id:o}));let i=!o&&ut(e.target,"[data-x-toggle-close]"),s=i&&Q(Lo(i));if(s&&N({id:s}),!s&&n.hasAttribute("data-x-toggle-direct-close")){let d=Q(Lo(n));N({id:d})}let a=ut(e.target,"a[href]"),c=a?a.getAttribute("href").replace("#","").trim():"",p=c&&c===re()?Oo(re()):[];if(p.length>0&&p.forEach(f=>{N({id:f,state:!0})}),window.xLastToggleable){if(!bc(window.xLastToggleable,".x-dropdown[data-x-dropdown-direct-close]"))return;Mc(e.target,{exclude:[o,s,...p]})}},z={},zc=t=>e=>{N({id:t,state:!0})},Fc=t=>e=>(z[t].canHoverLeave=!1,N({id:t,state:!1}),()=>{});function Mo(t){let e=Q(t),n=Fe(e);if(!n)return;let r=t.getAttribute("data-x-hoverintent");r=JSON.parse(r||"{}");let{interval:o=100,timeout:i=100}=r;return z[e]={canHoverLeave:!1,cancelEnter:()=>{},cancelLeave:()=>{},hasHoverToggle:()=>!!document.querySelector(`[data-x-toggleable="${e}"][data-x-toggle-hover]`),onEnter:To(zc(e),o),onLeave:To(Fc(e),i)},[q(n,"mouseenter",()=>{z[e].hasHoverToggle()&&(z[e].canHoverLeave=!0,z[e].cancelLeave(),zt(e)||(z[e].cancelEnter=z[e].onEnter()))}),q(n,"mouseleave",()=>{z[e].canHoverLeave&&(dt||(z[e].cancelLeave=z[e].onLeave()),z[e].onEnter.cancel())}),q(t,"mouseenter",()=>{let s=[e,...Be(oe(t))];setTimeout(()=>{s.forEach(a=>{z[a]&&z[a].cancelLeave()})})}),q(t,"mouseleave",({toElement:s})=>{[e,...Be(oe(t))].forEach(u=>{z[u]&&z[u].canHoverLeave&&(dt||(z[u].cancelLeave=z[u].onLeave()))}),Be(oe(s)).forEach(u=>{z[u]&&z[u].cancelLeave()})}),q(n,"touchstart",function(){!z[e].hasHoverToggle()||N({id:e})})]}function $e(t,e){let r=window.getComputedStyle(t)["transition-duration"];if(r=r?parseFloat(r.replace("s","")):0,ze&&(ze(),ze=null),!r){K(t,"x-active",e),K(t,"x-active-animate",e);return}let o=r*1e3,i=e?"x-active":"x-active-animate",s=e?"x-active-animate":"x-active",a=e?15:o;requestAnimationFrame(function(){K(t,i,e),window.dispatchEvent(new CustomEvent("resize"))});let c=setTimeout(function(){requestAnimationFrame(function(){K(t,s,e)})},a);return ze=function(){!c||(clearTimeout(c),K(t,"x-active",e),K(t,"x-active-animate",e))}}function Ro(t){if(t.tagName==="BUTTON")return;let e=Nn({ignoreHoverToggle:!1});t.addEventListener("keydown",n=>{n.key==="Enter"&&e(n)})}function zo(t){let e=function(){let n=hc(t,".x-current-layer"),r=Array.from(n.children).filter(o=>o.matches("li")).reduce((o,i)=>o+Sc(i),0);t.style.height=`${r}px`};return e(),ct(e)}function qc(t,e){let n=qe(t);if(!n)return;let r=e?n:"";!e&&`#${n}`!==window.location.hash||`#${r}`!==window.location.hash&&(history.pushState(null,null,"#"+r),window.dispatchEvent(new CustomEvent("hashchange")))}function Bc(t,e){if(e||(e=100),t){var n=document.createElement("input");n.style.position="fixed",n.style.top=t.offsetTop+7+"px",n.style.left=t.offsetLeft+"px",n.style.height=0,n.style.opacity=0,document.body.appendChild(n),n.focus(),setTimeout(function(){t.focus(),t.click(),document.body.removeChild(n)},e)}}var{addClass:Wn,siblings:$c,once:_c,removeClass:_e,hasClass:ie,listener:se,makeGetComputedStyle:Fo,makeGetComputedFloatValues:Dc}=b,Nc={interval:25,timeout:25,sensitivity:9};function qo(t){Uc(t),Yc()}var Vn=t=>t?t.getBoundingClientRect():null,Wc=Fo("position"),Vc=Fo("direction"),Gc=Dc(["paddingLeft","paddingTop","paddingRight","paddingBottom","borderTopWidth","borderBottomWidth"]);function Uc(t){t=Object.assign({selectors:[],indicatingSelector:"a",rootElementEvents:!1,transitionTimeout:null,requireClick(){return!1},toggleOnFocus:!0,activate(o){t.indicatingSelector?Wn(o.querySelector(t.indicatingSelector),t.activeClass):Wn(o,t.activeClass);let i=o.querySelector(t.nestedSelector);if(ie(i,"x-dropdown")){t.transitionTimeout&&t.transitionTimeout(),t.transitionTimeout=$e(i,!0);return}Wn(i,t.activeClass)},deactivate(o){t.indicatingSelector?_e(o.querySelector(t.indicatingSelector),t.activeClass):_e(o,t.activeClass);let i=o.querySelector(t.nestedSelector);if(ie(i,"x-dropdown")){t.transitionTimeout&&t.transitionTimeout(),t.transitionTimeout=$e(i,!1);return}_e(i,t.activeClass)},isActive(o){return t.indicatingSelector?ie(o.querySelector(t.indicatingSelector),t.activeClass):ie(o,t.activeClass)},deactivateChildren(o,i){Array.from(o.querySelectorAll(t.nestedSelector)).forEach(s=>{!ie(s,t.activeClass)||(_e(s,t.activeClass),typeof i=="function"&&i(s))})},deactivateChild:null,activeClass:"x-active",nestedSelector:".sub-menu",findSiblings:null,closeSiblings:!0},typeof t=="object"?t:{});function e(o){var i=!1;o._stemAllowFocusIn=!0;let s=o.closest("[data-x-hoverintent]"),a=s&&s.getAttribute("data-x-hoverintent"),c=a?JSON.parse(a):Nc,u=t.rootElementEvents?o:o.querySelector(t.indicatingSelector)||o,p=se(u,"mousedown",g),f=se(u,"touch",g),d=[p,f];if(d.push(se(u,"touchstart",()=>{o._stemAllowFocusIn=!1})),t.toggleOnFocus&&(d.push(se(u,"focusin",l)),d.push(se(o,"focusout",m))),!t.requireClick(o)){d.push(Tt(u,"touchstart",()=>{i=!0}));let h=xo(o,function(){i||(p(),f(),n(o,!0))},function(){i||n(o,!1)});try{h.options(c)}catch{}d.push(()=>h.remove())}function l(){o._stemAllowFocusIn&&r(o,n(o,!0))}function m(){setTimeout(()=>{o.contains(document.activeElement)||r(o,n(o,!1))},0)}function g(h){h.type==="mousedown"&&_c(h.currentTarget,"click",y=>void y.preventDefault()),h.preventDefault(),h.stopPropagation();let x=n(o);r(o,x),x&&t.closeSiblings&&(typeof t.findSiblings=="function"?t.findSiblings(o):$c(o)).forEach(w=>{n(w,!1),r(w,!1)})}return d}t.selectors.forEach(o=>{S(o,e)});function n(o,i){return o._stemAllowFocusIn=!0,typeof i=="undefined"&&(i=!t.isActive(o)),i?(typeof t.beforeActivate=="function"&&t.beforeActivate(o),t.activate(o),typeof t.afterActivate=="function"&&t.afterActivate(o)):(typeof t.beforeDeactivate=="function"&&t.beforeDeactivate(o),t.deactivate(o),typeof t.afterDeactivate=="function"&&t.afterDeactivate(o)),t.isActive(o)}function r(o,i){typeof t.deactivateChildren=="function"&&(clearTimeout(o._stemCloseChildrenTimer),i||(o._stemCloseChildrenTimer=setTimeout(function(){t.deactivateChildren(o,t.deactivateChild)},1e3)))}}function Yc(){function t(l){var m=[];function g(x,y){if(!y&&x.hasAttribute("data-x-stem")){m.push(x),t(x);return}if(x.children)for(var w=0;w<x.children.length;w++)g(x.children[w])}g(l,!0);let h=function(){o(l),setTimeout(()=>{m.forEach(n)},0)};l.addEventListener("x-stem:update",h,!1)}let e=[];function n(l){l&&l.dispatchEvent(new CustomEvent("x-stem:update"))}S("[data-x-stem-menu-top], [data-x-stem-root]",l=>{requestAnimationFrame(()=>{t(l),n(l),e.push(l)})});let r=D(function(){e.forEach(n)},50);window.addEventListener("tco-toggle",({detail:{state:l}={}})=>{l&&r()},W),window.addEventListener("resize",r,W),window.addEventListener("scroll",r,W),window.addEventListener("cs-sticky-bar-post-transition",r,W),V(r);function o(l){if(!l)return;let m=d(l);if(!m)return;let g=Vn(m),h=a(l),x=h==="data-x-stem-root"?Vn(document.querySelector(`[data-x-toggleable="${l.getAttribute("data-x-toggleable")}"][data-x-toggle]`)):g;if(!x)return;let y=l.getAttribute("data-x-stem-force");if(y){let P=y.indexOf("d")!==-1,B=y.indexOf("r")!==-1;l.setAttribute("data-x-stem",y),h==="data-x-stem-root"&&i(l,g,x,B,P),h||p(l,B,P);return}let w=Vn(l),{top:T,left:O,bottom:v,right:E}=s(w,x,h),{x:H,y:F}=c(l,h),R=u(H,window.innerWidth-E,O),L=u(F,window.innerHeight-v,T);l.setAttribute("data-x-stem",(L?"d":"u")+(R?"r":"l")),h==="data-x-stem-root"&&i(l,g,x,R,L),h||p(l,R,L)}function i(l,m,{top:g,left:h,bottom:x,right:y,height:w,width:T},O,v){let E=l.getAttribute("data-x-stem-root")||l.getAttribute("data-x-stem-force")||"",H=E.indexOf("h")!==-1,F=E.indexOf("c")!==-1;if(O&&!F){let L=h-m.left;l.style.left=`${H?L+T:L}px`,l.style.right="auto"}else if(F){let L=l.getBoundingClientRect(),P=h-L.width/2+T/2;P=Math.max(0,P);let B=window.innerWidth-L.width;P=Math.min(P,B),l.style.left=`${P}px`,l.style.right="auto"}else{let L=m.right-y;l.style.right=`${H?L+T:L}px`,l.style.left="auto"}let R=window.getComputedStyle(l);if(v){let L=g-m.top,P=H?L:L+w;R.position==="fixed"&&(P+=m.top),l.style.top=`${P}px`,l.style.bottom="auto"}else{let L=m.bottom-x,P=H?L:L+w;R.position==="fixed"&&(P+=m.bottom),l.style.bottom=`${P}px`,l.style.top="auto"}}function s({height:l,width:m},g,h){let x={top:g.top-l,right:g.left+g.width+m,bottom:g.top+l,left:g.left-m};return h&&(x.right+=g.width,x.bottom+=g.height),x}function a(l){return l.hasAttribute("data-x-stem-menu-top")?"data-x-stem-menu-top":l.hasAttribute("data-x-stem-root")?"data-x-stem-root":null}function c(l,m){if(m){var g=Vc(l)==="ltr",h=l.getAttribute(m).indexOf("r")!==-1;return{y:!0,x:!!(g^h)}}let x=f(l);return{y:x.indexOf("d")!==-1,x:x.indexOf("r")!==-1}}function u(l,m,g){if(l&&m<0){if(m<g)return!1}else if(g<0&&g<m)return!0;return l}function p(l,m,g){let{paddingLeft:h,paddingTop:x,paddingRight:y,paddingBottom:w,borderTopWidth:T,borderBottomWidth:O}=Gc(l);if(m?(l.style.marginLeft=h!==0?`${h}px`:null,l.style.marginRight=null):(l.style.marginRight=y!==0?`${y}px`:null,l.style.marginLeft=null),g){let v=x+T;l.style.marginTop=v!==0?`${v*-1}px`:null,l.style.marginBottom=null}else{let v=w+O;l.style.marginBottom=v!==0?`${(w+O)*-1}px`:null,l.style.marginTop=null}}function f(l){return l.parentElement===null?"tr":l.parentElement.hasAttribute("data-x-stem-force")?l.parentElement.getAttribute("data-x-stem-force"):l.parentElement.hasAttribute("data-x-stem")?l.parentElement.getAttribute("data-x-stem"):f(l.parentElement)}function d(l){if(l.parentElement===null)return document.body;let m=Wc(l.parentElement);return m==="relative"||m==="absolute"?l.parentElement:d(l.parentElement)}}var{onLoad:Bo,addClass:Ld,hasClass:$o,removeClass:kd,debounce:_o,animateToElement:jc,listener:Do,listenerPassive:No,onScroll:Qc,onResize:Jc,PASSIVE_ARGS:Kc}=b,Wo=t=>t.match(/#[a-zA-Z]/);qo({selectors:[".x-menu-inline .menu-item-has-children",".x-menu-dropdown .menu-item-has-children"],beforeActivate:t=>Rt(t.querySelector("a"),!0),beforeDeactivate:t=>Rt(t.querySelector("a"),!1),deactivateChild:t=>Rt(t.querySelector("a"),!1)});Bo(()=>{let{selector:t,duration:e,easing:n,initialMove:r,before:o,after:i,allowScroll:s}=_.apply("hash_scrolling_config",{selector:window.csJsData.linkSelector,easing:"ease-out",duration:500,initialMove:!0,before:f=>_.apply("hash_scrolling_before",f),after:f=>_.apply("hash_scrolling_before",f),allowScroll:(...f)=>_.apply("hash_scrolling_allow",!0,...f)});function a(f){try{return document.querySelector(f)}catch{}return null}function c(){let f=!1;return No(document.body,"touchstart",()=>{f=!1}),No(document.body,"touchmove",()=>{f=!0}),()=>f}let u=(f,d=!0,l)=>{jc(f,()=>Pt()*-1,d?e:0,n,l)},p=c();r&&window.location.hash&&(u(a(window.location.hash),!1),setTimeout(()=>u(a(window.location.hash),!1),300)),Do(document.body,"click",f=>{let d=f.target.matches(t)?f.target:f.target.closest(t);if(!d||f.tcoAbortScroll)return;let l=d.getAttribute("href"),m=l.split("#");if(!m[1])return;let g=m[0].replace(location.origin,""),h=`#${m[1]}`;if(p()||g&&g!==location.pathname||!Wo(l)||!s(d,f,h))return;let x=a(h);!x||(window.history.pushState&&(window.history.pushState(null,null,h),window.dispatchEvent(new CustomEvent("tcoHistoryPush"))),f.preventDefault(),o({anchor:d,target:x,hash:h}),u(x,!0,()=>void i({anchor:d,target:x,hash:h})))})});Bo(()=>{let t=!1,e,n=new Map,r=new Map,o=[],i=_o(()=>{var f,d;let p=[];for(let[l,m]of r){let g=((d=(f=m.getBoundingClientRect())==null?void 0:f.top)!=null?d:0)+window.scrollY;p.push({y:g,href:l})}o=p.sort((l,m)=>m.y-l.y)},100),s=p=>{if(e!==p){for(let[f,d]of n)if(f===p)for(let l of d)$o(l,"x-anchor")&&zn(l,!0),_.apply("scrollspy_activate",l);else for(let l of d)$o(l,"x-anchor")&&zn(l,!1),_.apply("scrollspy_deactivate",l);e=p}};S(window.csJsData.linkSelector,p=>{let f=_.apply("scrollspy_ignore_patterns",["#/","#wp-toolbar"]),d=`#${p.getAttribute("href").trim().split("#").pop()}`;if(!(!Wo(d)||f.find(l=>l.match(d))))return n.has(d)||n.set(d,new Set),n.get(d).add(p),[S(d,l=>(r.has(d)||r.set(d,l),i(),()=>{r.has(d)&&r.delete(d)})),()=>{var l;(l=n.get(d))==null||l.delete(p)},Do(p,"click",()=>{t=!0,s(d)},{capture:!0})]}),Jc(i);let a=()=>{var f,d;let p=window.scrollY+Pt()+Y()+1;return Math.ceil(window.innerHeight+window.scrollY)>=Math.floor(document.body.offsetHeight)?(d=(f=o.map((l,m)=>({...l,i:m})).sort((l,m)=>m.y-p-(l.y-p))[0])==null?void 0:f.i)!=null?d:-1:o.findIndex(({y:l})=>p>=l)},c=()=>{t=!1;let p=a();s(p===-1?"":o[p].href)},u=_o(c,250);u(),window.addEventListener("hashchange",function(p){s(window.location.hash)},Kc),Qc(()=>{o.length<=0||(t?u():c())})});var{transitionEnd:Zc,addClass:Gn,removeClass:Un,farthest:Vo,getCachedJsonAttribute:Go,oncePassive:Id,listenerPassive:Xc,evaluateCondition:tl,expandElementValue:Uo,makeAlternatingSynchronizer:Yn,makeElementWeakMap:jn,getDurations:el,lockMotion:Yo,forceOpaque:nl,runAnimation:jo,elementMeta:rl,waypoint:Qo,parseTime:ol}=b,vt="x-effect-enter",Qn="x-effect-entering",nt="x-effect-exit",Jn="x-effect-exiting",De="x-effect-animated",Ne="x-effect-holding",il="x-effect-opacity";S("[data-x-single-anim]",(t,e)=>{if(!t.classList.contains("x-always-active"))return Xc(t.closest(".x-anchor, .x-text"),"mouseenter",()=>void jo(t,{animation:e,remove:!0}))});var Jo=jn({scrollEffects:!1}),sl=()=>{let{get:t,set:e,has:n}=jn();return r=>(n(r)||e(r,{effects:[],particles:[]}),t(r))},We=sl(),Ko=jn(0),Zo=t=>{Ko.set(t,el(t))},Xo=(t,e="transitionTime")=>{var n,r;return(r=(n=Ko.get(t))==null?void 0:n[e])!=null?r:0},ti=(t,{from:e,to:n,trans:r,record:o=!1},i=()=>{})=>(t.classList.remove(e),t.classList.add(r),t.classList.add(n),o&&Zo(t),t.csAnimationEndingTimeout&&clearTimeout(t.csAnimationEndingTimeout),()=>{t.csAnimationEndingTimeout=setTimeout(function(){t.classList.remove(r)},250),i()}),ei=(t,e,n=()=>{})=>jo(t,{className:De,animation:e,remove:!0,timeout:!0},n),al=(t,e)=>{t.classList.contains(nt)||(t.classList.contains(De)||t.classList.add(De),t.style.setProperty("animation-duration","0ms","important"),t.style.setProperty("animation-name",e))},ni=(t,e,n)=>Zc(t,ti(t,e,n)),ri=(t,e,n,r)=>ei(t,e,ti(t,n,r)),cl=(t,e,n)=>ri(t,e,{from:nt,to:vt,trans:Qn,record:!0},n),oi=(t,e,n)=>ri(t,e,{from:vt,to:nt,trans:Jn},n),ll=(t,e)=>ni(t,{from:nt,to:vt,trans:Qn,record:!0},e),ii=(t,e)=>ni(t,{from:vt,to:nt,trans:Jn},e),ul=(t,e,n)=>{Gn(t,Ne),setTimeout(nl(t,{after:()=>{Un(t,Ne),n()}}),e)},si=(t,e,n)=>{let r=Yo(t,e==="fade"?"opacity":null);t.style.setProperty("opacity",e==="fade"?0:1,"important");let o=Xo(t);Gn(t,Ne),setTimeout(()=>{Un(t,vt),Un(t,Ne),Gn(t,nt),t.style.removeProperty("opacity"),r(),n()},o)};function fl(t,e,n){let r,o=0,i,s,{durationBase:a,animationAlt:c}=Go(t,"data-x-effect"),u=ol(a),p=()=>{window.removeEventListener("mousemove",h),c?n(()=>{f()}):(n(),u?setTimeout(()=>{f()},u):f())},f=()=>{t.addEventListener("mouseenter",m)},d=()=>{let{top:y,left:w,width:T,height:O}=t.getBoundingClientRect();r.push({top:y+window.scrollY,left:w+window.scrollX,width:T,height:O})},l=y=>{o=0,r=[],d(),e(),u?(clearTimeout(i),i=setTimeout(()=>{d()},u)):d()},m=y=>{clearTimeout(s),window.addEventListener("mousemove",h),l(y)},g=y=>{o++,o>10&&r.length===r.filter(T=>x(T,y.clientX,y.clientY,window.scrollX,window.scrollY)).length&&p()},h=y=>{g(y),clearTimeout(s),s=setTimeout(()=>{g(y)},300)},x=({top:y,left:w,height:T,width:O},v,E,H,F)=>{let R=y-F,L=w-H,P=v>L&&v<L+O,B=E>R&&E<R+T;return!P||!B};f()}S("[data-x-effect-provider]",(t,e="")=>{e.split(" ").filter(r=>!Vo(t,`[data-x-effect-provider*="${r}"]`)).length>0&&Kn(t)});function Kn(t){let e=We(t);e.registered||fl(t,()=>{let{registered:n,...r}=We(t);Object.keys(r).forEach(o=>{r[o].forEach(({setup:i})=>{i&&i()})})},()=>{let{registered:n,...r}=We(t);Object.keys(r).forEach(o=>{r[o].forEach(({teardown:i})=>{i&&i()})})}),e.registered=!0}function ai(t,e,n=()=>{},r=()=>{},o=!0){let i=Vo(e,`[data-x-effect-provider*="${t}"]`),s=o?i||e:i;return s?(We(s)[t].push({el:e,setup:n,teardown:r}),e===s):!1}S(".x-anchor",t=>{ai("particles",t)&&Kn(t)});S("[data-x-effect]",(t,e)=>{try{return ai("effects",t,()=>{e.animationAlt&&ei(t,e.animationAlt)},()=>{},!t.matches("x-anchor"))&&Kn(t),wl(t,e)}catch(n){console.warn(n)}},1e3);var dl=(t,e)=>Yn(n=>ll(t,n),n=>e==="transform"?ii(t,n):si(t,e,n)),pl=(t,e,{animationEnter:n,animationExit:r})=>Yn(o=>cl(t,n,o),o=>e==="animation"?oi(t,r,o):si(t,e,o)),ml=(t,e,n)=>Yn(r=>{if(Zo(t),["transform","animation"].includes(e)){e==="animation"&&(t.classList.remove(De),t.style.setProperty("opacity",0,"important"),t.style.removeProperty("animation-name"),t.offsetHeight,t.style.removeProperty("opacity"));let o=Yo(t,"opacity");t.classList.remove(nt),o()}r()},r=>{switch(e){case"none":return ul(t,Xo(t),r);case"transform":return ii(t,r);case"animation":return oi(t,n.animationExit,r);default:r()}}),ci=(t,e,n)=>{let r=Go(t,"data-x-effect");if(r.scroll){let o=a=>a==="effect"?r.animationEnter&&r.animationExit?"animation":"transform":a;e!=="effect"&&n!=="effect"&&(t.classList.add("x-no-at"),t.classList.remove("x-effect-exit"),setTimeout(()=>{t.classList.remove("x-no-at")}));let i=o(e),s=o(n);switch(s==="animation"&&al(t,r.animationExit),e==="effect"&&["fade","none"].includes(n)&&t.classList.add(il),i){case"animation":return pl(t,o(n),r);case"transform":return dl(t,s,r);case"fade":return ml(t,s,r)}}return!1};tt("effects",(t,{key:e,condition:n,enter:r,exit:o})=>{Jo.set(t,{scrollEffects:"managed"});let i=ci(t,Uo(t,r),Uo(t,o));return i?s=>void i(tl(t,n,s,e)):()=>{}});var gl=(t,{behaviorScroll:e})=>{let n=ci(t,"effect","effect"),r=!1;return o=>{let[i,s]=o.split(":");e==="reset"&&i==="exit"&&s==="down"||e==="fire-once"&&r||(i==="enter"&&(r=!0),n(i==="enter"))}},hl=t=>{let e=parseInt(t);return t.includes("px")?()=>e:()=>window.innerHeight*e/100},yl=t=>{let e=parseInt(t);return t.includes("px")?()=>window.innerHeight-e:()=>window.innerHeight-window.innerHeight*(parseInt(e)/100)};function xl(t,e){let n,r=()=>{},o=()=>{let{effectRivet:i}=rl.get(e);i?r=Zt([[t,...i]]):setTimeout(o,10)};return o(),()=>{clearTimeout(n),r()}}function wl(t,e){let n=t.closest("[data-x-slide], [data-x-slide-goto]");if(n&&t!==n)return xl(t,n);if(!e.scroll||Jo.get(t).scrollEffects==="managed")return;if(window.csGlobal.isPreview&&t.classList.add(nt),e.forceScrollEffect){t.classList.remove(vt),t.classList.remove(Qn),t.classList.remove(nt),t.classList.remove(Jn),e.forceScrollEffect==="in"&&t.classList.add(vt),e.forceScrollEffect==="out"&&t.classList.add(nt);return}let r=gl(t,e),{offsetTop:o="10%",offsetBottom:i="10%"}=e;Qo(t,s=>void r(`${s==="up"?"enter":"exit"}:${s}`),hl(o),!1),Qo(t,s=>void r(`${s!=="up"?"enter":"exit"}:${s}`),yl(i),!1)}var{defer:vl,addClass:li,toggleClass:bl,removeClass:Sl,listenerPassive:Ft}=b;S("[data-x-search]",function(t){let e=t.querySelector("input"),n=()=>vl(()=>e.focus()),r=()=>bl(t,"x-search-has-content",!!e.value);return[Ft(e,"input",r),Ft(t,"mousedown",()=>{li(t,"x-search-focused"),n()}),Ft(t,"focusin",()=>void li(t,"x-search-focused")),Ft(t,"focusout",()=>void Sl(t,"x-search-focused")),Ft(t.querySelector("[data-x-search-clear]"),"click",()=>{e.value="",e.focus(),r()}),Ft(t.querySelector("[data-x-search-submit]"),"click",()=>{window.csGlobal.isPreview||t.submit()})]});S("[data-x-element-bg-layer]",(t,{parallaxDir:e="v",parallaxRev:n=!1,parallaxSize:r=""})=>{let o=t.closest(".x-bg"),i=e==="h";t.style.opacity="1",i?(n||(t.style.left="auto",t.style.right="0"),t.style.width=r):(n||(t.style.top="auto",t.style.bottom="0"),t.style.height=r);let s=!1;function a(){if(s)return;s=!0;let{width:u,height:p,top:f,bottom:d}=o.getBoundingClientRect();if(f<=window.innerHeight&&d>=0){let{width:l,height:m}=t.getBoundingClientRect(),g=n?-1:1,h=i?l-u:m-p,x=1-d/(window.innerHeight+p),y=`${parseInt(x*h*g,10)}px`,w=i?`translate3d(${y}, 0, 0)`:`translate3d(0, ${y}, 0)`;t.style.transform=w}s=!1}function c(){requestAnimationFrame(a)}return[on(c),ct(c),V(c)]});V(function(){S("[data-x-scroll-link]",function(t,e){let n=document.querySelector(e);return q(t,"wheel",function(r){n.scrollTop+=r.deltaY})})});var{addClass:Tl,removeClass:ui,hasClass:Al,listenerPassive:ae}=b;function Zn(t,e){(e!=null?e:!Al(t,"is-active"))?(ui(t,"has-not-flipped"),Tl(t,"is-active")):ui(t,"is-active")}var Ve=!1;S("[data-x-element-card]",t=>[ae(t,"touchstart",()=>{Ve=!0}),ae(t,"click",({target:e})=>{t.contains(e.closest("a"))||Zn(t)}),ae(t,"pointerenter",()=>{setTimeout(function(){Ve||Zn(t,!0)},15)}),ae(t,"pointerleave",()=>{Ve||Zn(t,!1)}),ae(t,"touchend",()=>{setTimeout(function(){Ve=!1},100)})]);var{makeRafLoop:El}=b;function Cl(t,e){let n=`${t}`;for(;n.length<e;)n=`0${n}`;return n}function Ll(){let t=new WeakMap;return function(e,n){t.has(e)||t.set(e,e.innerHTML),n!==t.get(e)&&(e.innerHTML=n,t.set(e,n))}}function fi({el:t,end:e,serverTime:n,leadingZeros:r=!0,hideEmpty:o=!0,loadingClass:i="is-loading",completeClass:s="is-complete",digitClass:a="x-countdown-digit",completeMessageTag:c="div",completeMessageContent:u="",completeMessageClass:p="x-countdown-complete",hideOnComplete:f=!1,selectors:d={days:"[data-x-countdown-d]",hours:"[data-x-countdown-h]",minutes:"[data-x-countdown-m]",seconds:"[data-x-countdown-s]",daysLabel:"[data-x-countdown-label-d]",hoursLabel:"[data-x-countdown-label-h]",minutesLabel:"[data-x-countdown-label-m]",secondsLabel:"[data-x-countdown-label-s]",parent:"[data-x-countdown-unit]",aria:"[data-x-countdown-aria]"},singularLabels:l={d:"Day",h:"Hour",m:"Minute",s:"Second"},pluralLabels:m={d:"Days",h:"Hours",m:"Minutes",s:"Seconds"},ariaLabel:g="Countdown ends in {{d}} days, {{h}} hours, and {{m}} minutes."}={}){if(!t)return;let h={days:t.querySelector(d.days),hours:t.querySelector(d.hours),minutes:t.querySelector(d.minutes),seconds:t.querySelector(d.seconds)},{days:x,hours:y,minutes:w,seconds:T}=h,O={d:t.querySelector(d.daysLabel),h:t.querySelector(d.hoursLabel),m:t.querySelector(d.minutesLabel),s:t.querySelector(d.secondsLabel)},v=new Date(e).getTime(),E=new Date(n).getTime()-new Date().getTime(),H=Ll(),F=I=>{let M=Math.abs(I),$=parseInt(M/86400);M%=86400;let ot=parseInt(M/3600);M%=3600;let bt=parseInt(M/60);M%=60;let er=parseInt(M);return x||(ot+=$*24),y||(bt+=ot*60),w||(er+=bt*60),{diffDays:$,diffHours:ot,diffMinutes:bt,diffSeconds:er}},R=()=>{if(Object.keys(h).forEach(G),f){t.style.display="none",H(t,"");return}if(!u)return;let I=document.createElement(c);I.innerHTML=u,p&&I.classList.add(p),t.append(I),t.classList.add(s)},L=I=>(r?Cl(I,2):I.toString()).split("").map($=>`<span class="${a}">${$}</span>`).join(""),P=(I,M)=>{if(!I||!O[I])return;let $=O[I];H($,(M?l:m)[I])},B=({diffDays:I,diffHours:M,diffMinutes:$,diffSeconds:ot})=>{x&&(H(x,L(I)),P("d",I===1)),y&&(H(y,L(M)),P("h",M===1)),w&&(H(w,L($)),P("m",$===1)),T&&(H(T,L(ot)),P("s",ot===1))},ce=({diffDays:I,diffHours:M,diffMinutes:$,diffSeconds:ot})=>{let bt=t.querySelector(d.aria);bt&&H(bt,g.replace(/{{d}}/g,I).replace(/{{h}}/g,M).replace(/{{m}}/g,$).replace(/{{s}}/g,ot))},G=I=>{if(!h[I])return;let M=h[I].closest(d.parent);M&&(M.remove(),h[I]=null)},rt=({diffDays:I,diffHours:M,diffMinutes:$})=>{I===0&&G("days"),I===0&&M===0&&G("hours"),I===0&&M===0&&$===0&&G("minutes")},xi=E+new Date().getTime();return El(I=>{let M=xi+I;if(M>v)return R(),!1;let $=F((M-v)/1e3);o&&rt($),B($),ce($),I===0&&t.classList.remove(i)})}S("[data-x-element-countdown]",(t,e={})=>fi({el:t,...e}));var{waypoint:kl,tween:Ol,getPrecisionLength:Il,getPrecisionLengthWithCommas:Hl,round:Pl}=b,Ml=",",di=".",Rl=/(-)?(\d+)(\.\d+)?/;function zl({el:t,from:e,to:n,commaSeparatedDecimal:r=!1,...o}){let i=typeof e=="undefined"?t.textContent:e,s=typeof n=="undefined"?t.textContent:n,a=r?di:Ml,c=s.toString().includes(a)||i.toString().includes(a),u=r?Hl:Il,p=Math.max(u(s),u(i)),f=m=>{if(!m)return console.warn("Input invalid",m),"";let[,g="",h="",x=""]=m.match(Rl),y=h.split("").reverse(),w=[];for(;y.length;)w.push(y.splice(0,3).reverse().join(""));return`${g}${w.reverse().join(a)}${x}`},d=pi(i,a),l=pi(s,a);Ol(d,{...o,update:m=>{let g=Pl(m,Math.pow(10,p)).toFixed(p),h=c?f(g):g;t.textContent=r?h.replace(/\.(\d+)$/,",$1"):h}})(l)}function pi(t,e){let n=0;return e===di&&(n=t.split(",")||[],n=n[1]||0,n&&n.length&&(n=n/Math.pow(10,n.length))),parseFloat(t.replace(new RegExp("\\"+e,"g"),""))+n}S("[data-x-element-counter]",(t,{to:e,speed:n,selector:r=".x-counter-number",commaSeparatedDecimal:o})=>kl(t,()=>void zl({el:t.querySelector(r),to:e,commaSeparatedDecimal:o,duration:n}),"85%"));function mi(t,e={}){try{return JSON.parse(t)}catch{}return e}var{intersect:Fl,hasClass:at,addClass:ql,removeClass:Xn,unwrapHtmlTemplate:Bl,dispatch:gi,listener:Ge,onResize:$l,elementIsVisibleInViewport:_l}=b,Dl=["playpause","progress"],Nl=["playpause","current","progress","duration","tracks","volume","fullscreen"];S("[data-x-element-mejs]",(t,{poster:e,options:n={}})=>{let r=at(t,"bg")||at(t,"x-video-bg"),o=at(t,"vimeo")||at(t,"youtube"),i=t.getAttribute("data-x-video-options")||n;i=typeof i=="string"?mi(i):i||{};let s=[];if(r&&(e&&(ql(t,"poster"),t.style.backgroundImage=`url(${e})`,setTimeout(()=>void Xn(t,"transparent"),500)),Bl(t.querySelector('script[type="text/template"]')),s.push(Ge(t,"xmejs-start",()=>{Xn(t.querySelector(".transparent"),"transparent"),Xn(t,"transparent")}))),!window.mejs)return;let a=t.querySelector(".x-mejs");if(!a||at(a.parentElement,"mejs-mediaelement"))return;let c=l=>{console.warn("MEJS media error.",l),l.stopPropagation()},u=at(a,"advanced-controls")?Nl:Dl;u=window.csGlobal.csHooks.apply("cs_mejs_video_features",u,t);let p=window.csGlobal.csHooks.apply("cs_mejs_video_player_args",{pluginPath:window._wpmejsSettings.pluginPath,startVolume:1,features:r?[]:u,audioWidth:"100%",audioHeight:32,audioVolume:"vertical",videoWidth:"100%",videoHeight:"100%",videoVolume:"vertical",pauseOtherPlayers:!1,alwaysShowControls:!0,hideVolumeOnTouchDevices:!1,setDimensions:!1,stretching:"responsive",autoRewind:!1,success:f,error(l){c(l)}});try{window.jQuery(a).mediaelementplayer(p)}catch(l){c(l)}function f(l,m,g){let h=!0,x=!0;at(t,"autoplay")&&(!i.pause_out_of_view||_l(t))&&(m.setAttribute("autoplay",!0),setTimeout(()=>{m.play()},100));let y=()=>{m.attributes.hasOwnProperty("autoplay")&&h&&(l.play(),h=!1),m.attributes.hasOwnProperty("muted")&&x&&(l.setMuted(!0),x=!1),l.removeEventListener("canplay",y)};l.addEventListener("canplay",y);let w=g.controls[0].querySelector(".mejs-volume-button");l.addEventListener("volumechange",()=>l.setVolume(at(w,"mejs-mute")?1:0)),l.addEventListener("ended",()=>{m.attributes.hasOwnProperty("loop")&&l.play()}),r||l.addEventListener("playing",()=>{Object.keys(window.mejs.players).filter(T=>T!==g.id&&!window.mejs.players[T].xIsVideoBG).map(T=>window.mejs.players[T]).forEach(T=>T.pause())}),g.isVideo===!0&&(g.xIsVideoBG=r,d(l,m,g))}function d(l,m,{container:g,controls:h}){if(l.addEventListener("timeupdate",function y(){gi(t,"xmejs-start"),l.removeEventListener("timeupdate",y)}),Ge(g[0],"fullscreenchange",()=>{document.fullscreenElement||l.removeAttribute("style")}),o&&s.push(Ge(t,"xmejs-start",()=>{var y;(y=t.querySelector("video.x-mejs"))==null||y.removeAttribute("poster")})),r)l.addEventListener("playing",()=>{l.setMuted(!0),gi(t,"xmejs-bgvideoready")}),s.push(Ge(t,"xmejs-bgvideoready",x)),s.push($l(x));else{let y=()=>h.stop().animate({opacity:1},150),w=()=>h.stop().animate({opacity:0},150);l.addEventListener("playing",()=>g.on("mouseenter",y).on("mouseleave",w)),l.addEventListener("pause",()=>{g.off("mouseenter mouseleave"),y()})}i.pause_out_of_view&&s.push(Wl(t,l));function x(){let y=t.querySelector(o?".me-plugin":"video"),w=m.videoWidth,T=m.videoHeight,O=o||w===0?1280:w,v=o||T===0?720:T,E=t.offsetWidth||0,H=t.offsetHeight||0,F=E/O,R=H/v,L=F>R?F:R,P=Math.ceil(L*O+20),B=Math.ceil(L*v+20),ce=Math.ceil((P-E)/2),G=Math.ceil((B-H)/2);y.style.width=`${P}px`,y.style.height=`${B}px`}}return s});function Wl(t,e){let n=at(t,"autoplay");return Fl(t,function(r){if(r.isIntersecting){n&&e.play();return}e.pause()})}var{waypoint:Vl}=b;S("[data-x-element-statbar]",(t,{triggerOffset:e}={})=>{Vl(t,()=>{Array.from(t.querySelectorAll(".x-statbar-bar, .x-statbar-label")).forEach(n=>{n.classList.add("x-active")})},e)});var{listener:Gd,onLoad:Gl,onResize:Ul,getOuterHeight:Yl}=b;S("[data-x-element-tabs]",(t,{equalPanelHeight:e})=>{if(!e)return;let n=Array.from(t.querySelectorAll(".x-tabs-panels")),r=()=>{let o=n.reduce((i,s)=>Math.max(i,Yl(s)),0);n.forEach(i=>{i.style.height=`${o}px`})};return r(),[Gl(r),Ul(r)]});S("script[data-cs-late-style]",function(t){let e=document.createElement("style");e.setAttribute("id",`cs-late-css-${t.getAttribute("data-cs-late-style")}`),e.appendChild(window.document.createTextNode(t.textContent)),window.document.head.appendChild(e),t.remove()});var{listener:hi,PASSIVE_ARGS:jl}=b;S("[data-x-toggleable]",t=>Ho(t));var tr=!1,yi=!1;document.addEventListener("readystatechange",()=>{document.readyState==="complete"&&!yi&&(yi=!0,window.document.body.addEventListener("click",Nn()),S("[data-x-toggleable]",t=>{let e=Q(t),n=qe(t);(zt(e)||n&&n===re())&&(window.csGlobal.isPreview&&(tr=!0,t.setAttribute("data-x-disable-animation",!0)),N({id:e,state:!0,force:!0,hashUpdate:!1}),tr&&(tr=!1,setTimeout(()=>{t.removeAttribute("data-x-disable-animation")},60)))}))},jl);S("[data-x-toggleable]:not([data-x-toggle])",Mo);S("[data-x-toggle]",Ro);V(function(){let t=[];S("[data-x-esc-close]",function(e){let n=Q(e);return n||console.warn("No toggle id setup for element, but using data-x-esc-close",e),t.push(n),function(){let r=t.indexOf(n);r!==-1&&t.splice(r,1)}}),q(window,"keyup",function(e){if(e.key!=="Escape")return;let n=window.xGetLastToggleable();!n||!window.xToggleGetState(n)||!t.includes(n)||N({id:n,state:!1})})});S("[data-x-toggle-layered-root]",zo);_.filter("hash_scrolling_allow",(t,e,n)=>n.target.hasAttribute("data-x-toggle-nested-trigger")||n.target.hasAttribute("data-x-skip-scroll")?!1:t);_.action("hash_scrolling_before",({anchor:t})=>{let e=t.closest(".x-modal.x-active, .x-off-canvas.x-active");e&&!(t.hasAttribute("data-x-toggleable")&&!t.querySelector("[data-x-toggle-nested-trigger]"))&&N({id:e.getAttribute("data-x-toggleable"),state:!1})});U("ontoggleclose",(t,e,n)=>hi(n.closest("[data-x-toggleable]"),"tco-toggle",r=>{r.detail.state&&t()}),{defaultOption:"throttle"});U("ontoggleopen",(t,e,n)=>hi(n.closest("[data-x-toggleable]"),"tco-toggle",r=>{r.detail.state||t()}),{defaultOption:"throttle"});window.addEventListener("tcoHistoryPush",Dn,!1);window.addEventListener("hashchange",Dn,!1);var mp=window.csGlobal;})();
