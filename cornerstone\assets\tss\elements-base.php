[{"1": [{"2": ["maybe-direct-child", [{"3": "input"}], [{"4": [{"3": "input"}, [{"5": {"6": "> "}}]]}, {"5": {"6": ""}}]]}, {"2": ["changed-from", [{"3": "input"}, {"3": "default"}], [{"5": {"7": ["not", {"8": [{"3": "input"}, "==", {"3": "default"}]}]}}]]}, {"2": ["empty-or-transparent", [{"3": "input"}], [{"5": {"8": [{"9": ["empty", "($input)", [{"3": "input"}]]}, "or", {"8": [{"3": "input"}, "==", {"10": "transparent"}]}]}}]]}, {"2": ["is-transparent", [{"3": "input"}], [{"5": {"8": [{"3": "input"}, "==", {"10": "transparent"}]}}]]}, {"2": ["is-bool", [{"3": "input"}], [{"5": {"8": [{"8": [{"3": "input"}, "==", {"10": true}]}, "or", {"8": [{"3": "input"}, "==", {"10": false}]}]}}]]}, {"2": ["is-set", [{"3": "current"}, {"3": "base"}], [{"5": {"8": [{"7": ["not", {"9": ["empty", "($current)", [{"3": "current"}]]}]}, "or", {"9": ["changed-from", "($current, $base)", [{"3": "current"}, {"3": "base"}]]}]}}]]}, {"2": ["changed", [{"3": "test"}, {"3": "current"}, {"3": "base"}, {"11": ["has<PERSON><PERSON><PERSON>", {"10": false}]}], [{"4": [{"3": "has<PERSON><PERSON><PERSON>"}, [{"5": {"12": "1"}}]]}, {"5": {"8": [{"9": ["changed-from", "($current, $test)", [{"3": "current"}, {"3": "test"}]]}, "or", {"9": ["changed-from", "($current, $base)", [{"3": "current"}, {"3": "base"}]]}]}}]]}, {"2": ["to-bool", [{"3": "input"}], [{"4": [{"3": "input"}, [{"5": {"12": "1"}}]]}, {"5": {"12": "0"}}]]}, {"2": ["webkit-mask-composite", [{"3": "input"}], [{"4": [{"8": [{"3": "input"}, "==", {"10": "exclude"}]}, [{"5": {"10": "xor"}}]]}, {"4": [{"8": [{"3": "input"}, "==", {"10": "intersect"}]}, [{"5": {"10": "destination-in"}}]]}, {"4": [{"8": [{"3": "input"}, "==", {"10": "subtract"}]}, [{"5": {"10": "source-out"}}]]}, {"4": [{"8": [{"3": "input"}, "==", {"12": "add"}]}, [{"5": {"10": "source-over"}}]]}]]}, {"13": ["linotype", [{"3": "ff"}, {"3": "fsize"}, {"3": "fstyle"}, {"3": "fw"}, {"3": "lh"}, {"3": "ls"}, {"3": "lsForce"}, {"3": "lsHasOffset"}, {"3": "ta"}, {"3": "ta<PERSON><PERSON>ce"}, {"3": "td"}, {"3": "tt"}, {"3": "ttForce"}], [{"14": ["font-family", {"9": ["global-ff", "($ff)", [{"3": "ff"}]]}, false]}, {"14": ["font-size", {"3": "fsize"}, false]}, {"14": ["font-style", {"3": "fstyle"}, false]}, {"14": ["font-weight", {"9": ["global-fw", "($ff,$fw)", [{"3": "ff"}, {"3": "fw"}]]}, false]}, {"14": ["line-height", {"3": "lh"}, false]}, {"4": [{"8": [{"3": "lsForce"}, "or", {"7": ["not", {"9": ["empty", "($ls)", [{"3": "ls"}]]}]}]}, [{"14": ["letter-spacing", {"3": "ls"}, false]}, {"4": [{"8": [{"3": "lsHasOffset"}, "and", {"7": ["not", {"9": ["empty", "($ls)", [{"3": "ls"}]]}]}]}, [{"14": ["margin-right", {"9": ["calc", {"15": ["(%s * -1)", [{"3": "ls"}]]}, [{"8": [{"10": {"15": ["%s", [{"3": "ls"}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}, false]}]]}]]}, {"4": [{"8": [{"3": "ta<PERSON><PERSON>ce"}, "or", {"7": ["not", {"9": ["empty", "($ta)", [{"3": "ta"}]]}]}]}, [{"14": ["text-align", {"3": "ta"}, false]}]]}, {"4": [{"7": ["not", {"9": ["empty", "($td)", [{"3": "td"}]]}]}, [{"14": ["text-decoration", {"3": "td"}, false]}]]}, {"4": [{"8": [{"3": "ttForce"}, "or", {"7": ["not", {"9": ["empty", "($tt)", [{"3": "tt"}]]}]}]}, [{"14": ["text-transform", {"3": "tt"}, false]}]]}]]}, {"13": ["border", [{"3": "width"}, {"3": "style"}, {"3": "base"}, {"3": "alt"}, {"11": ["colorOnly", {"10": false}]}, {"11": ["fallback", {"10": false}]}], [{"4": [{"8": [{"7": ["not", {"9": ["empty", "($width)", [{"3": "width"}]]}]}, "and", {"7": ["not", {"9": ["empty", "($style)", [{"3": "style"}]]}]}]}, [{"4": [{"3": "colorOnly"}, [{"14": ["border-color", {"9": ["global-color", "($base)", [{"3": "base"}]]}, false]}], {"16": [{"14": ["border-width", {"3": "width"}, false]}, {"14": ["border-style", {"3": "style"}, false]}, {"4": [{"9": ["is-gradient", "($base)", [{"3": "base"}]]}, [{"14": ["border-image", {"9": ["build-gradient", "($base)", [{"3": "base"}]]}, false]}, {"14": ["border-image-slice", {"12": "1"}, false]}, {"14": ["border-color", {"10": "transparent"}, false]}], {"16": [{"14": ["border-color", {"9": ["global-color", "($base)", [{"3": "base"}]]}, false]}]}]}]}]}], {"17": [{"8": [{"3": "fallback"}, "or", {"7": ["not", {"9": ["is-base", "()", []]}]}]}, [{"4": [{"3": "colorOnly"}, [{"14": ["border-color", {"10": "transparent"}, false]}], {"16": [{"14": ["border-width", {"12": "0"}, false]}]}]}]]}]}]]}, {"13": ["border-alt", [{"3": "width"}, {"3": "style"}, {"3": "base"}, {"3": "alt"}], [{"4": [{"8": [{"7": ["not", {"9": ["empty", "($width)", [{"3": "width"}]]}]}, "and", {"7": ["not", {"9": ["empty", "($style)", [{"3": "style"}]]}]}]}, [{"4": [{"8": [{"7": ["not", {"9": ["empty-or-transparent", "($alt)", [{"3": "alt"}]]}]}, "and", {"8": [{"3": "alt"}, "!=", {"3": "base"}]}]}, [{"4": [{"9": ["is-gradient", "($alt)", [{"3": "alt"}]]}, [{"14": ["border-image", {"9": ["build-gradient", "($alt)", [{"3": "alt"}]]}, false]}, {"14": ["border-image-slice", {"12": "1"}, false]}, {"14": ["border-color", {"10": "transparent"}, false]}], {"16": [{"14": ["border-color", {"9": ["global-color", "($alt)", [{"3": "alt"}]]}, false]}]}]}]]}]]}]]}, {"13": ["box-prop", [{"3": "prop"}, {"3": "base"}, {"3": "current"}, {"11": ["fallback", {"6": "_off"}]}], [{"4": [{"8": [{"9": ["is-set", "($current, $base)", [{"3": "current"}, {"3": "base"}]]}, "and", {"7": ["not", {"9": ["off", "($base)", [{"3": "base"}]]}]}]}, [{"14": [{"15": ["%s", [{"3": "prop"}]]}, {"3": "current"}, false]}], {"17": [{"8": [{"3": "fallback"}, "!=", {"6": "_off"}]}, [{"14": [{"15": ["%s", [{"3": "prop"}]]}, {"3": "fallback"}, false]}]]}]}]]}, {"13": ["margin", [{"3": "base"}, {"3": "current"}, {"3": "fallback"}], [{"18": ["box-prop", [{"19": "margin"}, {"3": "base"}, {"3": "current"}, {"3": "fallback"}]]}]]}, {"13": ["padding", [{"3": "base"}, {"3": "current"}, {"3": "fallback"}], [{"18": ["box-prop", [{"19": "padding"}, {"3": "base"}, {"3": "current"}, {"3": "fallback"}]]}]]}, {"13": ["border-radius", [{"3": "base"}, {"3": "current"}, {"3": "fallback"}], [{"18": ["box-prop", [{"19": "border-radius"}, {"3": "base"}, {"3": "current"}, {"3": "fallback"}]]}]]}, {"13": ["aspect-ratio", [{"11": ["prefix", {"6": ""}]}], [{"18": ["changedmixin", [{"19": ""}, {"19": {"15": ["%saspect_ratio_value", [{"3": "prefix"}]]}}, {"6": "aspect-ratio"}]]}]]}, {"13": ["background-color", [{"3": "key"}, {"3": "altKey"}], [{"20": ["base", {"9": ["get-base", "($key)", [{"3": "key"}]]}]}, {"20": ["current", {"9": ["get", "($key)", [{"3": "key"}]]}]}, {"20": ["alt", {"9": ["get", "($altKey)", [{"3": "altKey"}]]}]}, {"4": [{"9": ["is-gradient", "($current)", [{"3": "current"}]]}, [{"14": ["background-image", {"9": ["build-gradient", "($current)", [{"3": "current"}]]}, false]}], {"16": [{"4": [{"8": [{"9": ["is-transparent", "($current)", [{"3": "current"}]]}, "and", {"7": ["not", {"9": ["is-transparent", "($base)", [{"3": "base"}]]}]}]}, [{"14": ["background-color", {"9": ["global-color", "($current)", [{"3": "current"}]]}, false]}], {"16": [{"18": ["background-color-not-transparent-or-empty", [{"3": "current"}, {"3": "alt"}]]}, {"4": [{"9": ["is-gradient", "($base)", [{"3": "base"}]]}, [{"14": ["background-image", {"10": "unset"}, false]}]]}]}]}]}]}]]}, {"13": ["background-color-not-transparent-or-empty", [{"3": "base"}, {"3": "alt"}], [{"20": ["bothUnset", {"8": [{"9": ["empty-or-transparent", "($base)", [{"3": "base"}]]}, "and", {"9": ["empty", "($alt)", [{"3": "alt"}]]}]}]}, {"4": [{"7": ["not", {"3": "bothUnset"}]}, [{"14": ["background-color", {"9": ["global-color", "($base)", [{"3": "base"}]]}, false]}]]}]]}, {"13": ["background-color-alt", [{"3": "base"}, {"3": "alt"}], [{"4": [{"8": [{"3": "base"}, "!=", {"3": "alt"}]}, [{"4": [{"8": [{"9": ["empty", "($alt)", [{"3": "alt"}]]}, "and", {"9": ["is-gradient", "($base)", [{"3": "base"}]]}]}, [], {"17": [{"9": ["is-gradient", "($alt)", [{"3": "alt"}]]}, [{"14": ["background-image", {"9": ["build-gradient", "($alt)", [{"3": "alt"}]]}, false]}]]}, {"16": [{"14": ["background-color", {"9": ["global-color", "($alt)", [{"3": "alt"}]]}, false]}, {"4": [{"9": ["is-gradient", "($base)", [{"3": "base"}]]}, [{"14": ["background-image", {"10": "unset"}, false]}]]}]}]}]]}]]}, {"13": ["text-color", [{"3": "base"}, {"3": "alt"}], [{"4": [{"9": ["is-gradient", "($base)", [{"3": "base"}]]}, [{"14": ["background-image", {"9": ["build-gradient", "($base)", [{"3": "base"}]]}, false]}, {"14": ["background-clip", {"10": "text"}, false]}, {"14": ["color", {"10": "transparent"}, false]}], {"16": [{"14": ["color", {"9": ["global-color", "($base)", [{"3": "base"}]]}, false]}]}]}]]}, {"13": ["text-color-alt", [{"3": "base"}, {"3": "alt"}], [{"4": [{"8": [{"3": "base"}, "!=", {"3": "alt"}]}, [{"4": [{"9": ["is-gradient", "($alt)", [{"3": "alt"}]]}, [{"14": ["background-image", {"9": ["build-gradient", "($alt)", [{"3": "alt"}]]}, false]}, {"14": ["background-clip", {"10": "text"}, false]}, {"14": ["color", {"10": "transparent"}, false]}], {"16": [{"14": ["color", {"9": ["global-color", "($alt)", [{"3": "alt"}]]}, false]}]}]}]]}]]}, {"13": ["theme-options-alt-color-fallback", [{"3": "property"}, {"3": "base"}, {"3": "alt"}], [{"4": [{"9": ["empty", "($alt)", [{"3": "alt"}]]}, [{"14": [{"15": ["%s", [{"3": "property"}]]}, {"9": ["global-color", "($base)", [{"3": "base"}]]}, false]}], {"16": [{"14": [{"15": ["%s", [{"3": "property"}]]}, {"9": ["global-color", "($alt)", [{"3": "alt"}]]}, false]}]}]}]]}, {"13": ["theme-options-alt-box-shadow-color-fallback", [{"3": "property"}, {"3": "x"}, {"3": "y"}, {"3": "blur"}, {"3": "spread"}, {"3": "base"}, {"3": "alt"}], [{"4": [{"9": ["empty", "($alt)", [{"3": "alt"}]]}, [{"14": [{"15": ["%s", [{"3": "property"}]]}, {"21": [{"3": "x"}, {"3": "y"}, {"3": "blur"}, {"3": "spread"}, {"9": ["global-color", "($base)", [{"3": "base"}]]}]}, false]}], {"16": [{"14": [{"15": ["%s", [{"3": "property"}]]}, {"21": [{"3": "x"}, {"3": "y"}, {"3": "blur"}, {"3": "spread"}, {"9": ["global-color", "($alt)", [{"3": "alt"}]]}]}, false]}]}]}]]}, {"13": ["shadow-base", [{"3": "prop"}, {"3": "dimensions"}, {"3": "base"}, {"3": "alt"}, {"3": "noAltAllowNone"}, {"3": "fallback"}], [{"4": [{"7": ["not", {"9": ["empty", "($dimensions)", [{"3": "dimensions"}]]}]}, [{"4": [{"7": ["not", {"9": ["empty-or-transparent", "($base)", [{"3": "base"}]]}]}, [{"14": [{"15": ["%s", [{"3": "prop"}]]}, {"9": ["merge", "($dimensions,global-color($base))", [{"3": "dimensions"}, {"9": ["global-color", "($base)", [{"3": "base"}]]}]]}, false]}], {"17": [{"8": [{"3": "noAltAllowNone"}, "or", {"7": ["not", {"9": ["empty-or-transparent", "($alt)", [{"3": "alt"}]]}]}]}, [{"14": [{"15": ["%s", [{"3": "prop"}]]}, {"10": "none"}, false]}]]}]}], {"17": [{"3": "fallback"}, [{"14": [{"15": ["%s", [{"3": "prop"}]]}, {"10": "none"}, false]}]]}]}]]}, {"13": ["shadow-alt", [{"3": "prop"}, {"3": "dimensions"}, {"3": "base"}, {"3": "alt"}], [{"4": [{"7": ["not", {"9": ["empty", "($dimensions)", [{"3": "dimensions"}]]}]}, [{"4": [{"8": [{"7": ["not", {"9": ["empty", "($alt)", [{"3": "alt"}]]}]}, "and", {"8": [{"3": "alt"}, "!=", {"3": "base"}]}]}, [{"14": [{"15": ["%s", [{"3": "prop"}]]}, {"9": ["merge", "($dimensions,global-color($alt))", [{"3": "dimensions"}, {"9": ["global-color", "($alt)", [{"3": "alt"}]]}]]}, false]}]]}]]}]]}, {"13": ["box-shadow", [{"3": "dimensions"}, {"3": "base"}, {"3": "alt"}, {"3": "noAltAllowNone"}, {"3": "fallback"}], [{"18": ["shadow-base", [{"19": "box-shadow"}, {"3": "dimensions"}, {"3": "base"}, {"3": "alt"}, {"3": "noAltAllowNone"}, {"3": "fallback"}]]}]]}, {"13": ["box-shadow-alt", [{"3": "dimensions"}, {"3": "base"}, {"3": "alt"}], [{"18": ["shadow-alt", [{"19": "box-shadow"}, {"3": "dimensions"}, {"3": "base"}, {"3": "alt"}]]}]]}, {"13": ["text-shadow", [{"3": "dimensions"}, {"3": "base"}, {"3": "alt"}, {"3": "noAltAllowNone"}, {"3": "fallback"}], [{"18": ["shadow-base", [{"19": "text-shadow"}, {"3": "dimensions"}, {"3": "base"}, {"3": "alt"}, {"3": "noAltAllowNone"}, {"3": "fallback"}]]}]]}, {"13": ["text-shadow-alt", [{"3": "dimensions"}, {"3": "base"}, {"3": "alt"}], [{"18": ["shadow-alt", [{"19": "text-shadow"}, {"3": "dimensions"}, {"3": "base"}, {"3": "alt"}]]}]]}, {"13": ["icon-dropshadow", [{"3": "dimensions"}, {"3": "color"}], [{"20": ["shadowDim", {"9": ["get", "($dimensions)", [{"3": "dimensions"}]]}]}, {"20": ["shadowColor", {"9": ["global-color", "(get($color))", [{"9": ["get", "($color)", [{"3": "color"}]]}]]}]}, {"4": [{"7": ["not", {"9": ["off", "($shadowDim)", [{"3": "shadowDim"}]]}]}, [{"14": ["filter", {"19": {"15": ["drop-shadow(%s %s)", [{"3": "shadowDim"}, {"3": "shadowColor"}]]}}, false]}]]}]]}, {"13": ["motion", [{"3": "type"}, {"3": "duration"}, {"3": "timing_function"}, {"3": "base_duration"}, {"11": ["base_timing_function", {"19": "cubic-bezier(0.400, 0.000, 0.200, 1.000)"}]}], [{"4": [{"7": ["not", {"3": "base_duration"}]}, [{"4": [{"8": [{"3": "type"}, "==", {"6": "transition"}]}, [{"20": ["base_duration", {"22": ["300", "ms"]}]}], {"17": [{"8": [{"3": "type"}, "==", {"6": "animation"}]}, [{"20": ["base_duration", {"22": ["1000", "ms"]}]}]]}]}]]}, {"4": [{"8": [{"3": "duration"}, "!=", {"3": "base_duration"}]}, [{"14": [{"15": ["%s-duration", [{"3": "type"}]]}, {"3": "duration"}, false]}]]}, {"4": [{"8": [{"3": "timing_function"}, "!=", {"3": "base_timing_function"}]}, [{"14": [{"15": ["%s-timing-function", [{"3": "type"}]]}, {"3": "timing_function"}, false]}]]}]]}, {"13": ["graphic", [{"3": "selector"}, {"11": ["keyPrefix", {"6": ""}]}, {"3": "noBase"}], [{"4": [{"8": [{"3": "keyPrefix"}, "!=", {"6": ""}]}, [{"20": ["keyPrefix", {"19": {"15": ["%s_", [{"3": "keyPrefix"}]]}}]}]]}, {"4": [{"7": ["not", {"3": "selector"}]}, [{"20": ["selector", {"6": "&"}]}]]}, {"4": [{"3": "noBase"}, [{"20": ["baseSelector", {"3": "selector"}]}], {"16": [{"20": ["baseSelector", {"19": {"15": ["%s .x-graphic", [{"3": "selector"}]]}}]}]}]}, {"23": [[{"15": ["%s ", [{"3": "baseSelector"}]]}], [{"18": ["margin", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_margin", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_margin", [{"3": "keyPrefix"}]]}}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_type", [{"3": "keyPrefix"}]]}}]]}, "!=", {"6": "none"}]}, [{"23": [[{"15": ["%s .x-graphic-icon ", [{"3": "selector"}]]}], [{"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_type", [{"3": "keyPrefix"}]]}}]]}, "==", {"19": "svg"}]}, [{"14": ["font-size", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_font_size", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_font_size", [{"3": "keyPrefix"}]]}}]]}, false]}, {"20": ["graphicWidthAndHeight", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_font_size", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_font_size", [{"3": "keyPrefix"}]]}}]]}]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}, "!=", {"19": "auto"}]}, [{"20": ["graphicWidthAndHeight", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}]}, {"20": ["graphicHeight", {"10": {"15": ["%s", [{"3": "graphicWidthAndHeight"}]]}}]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, "!=", {"19": "auto"}]}, [{"20": ["graphicHeight", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"14": ["width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}, false]}, {"23": [["> svg"], [{"14": ["fill", {"9": ["global-color", {"15": ["(get(%s))", [{"19": {"15": ["%sgraphic_icon_color", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_color", [{"3": "keyPrefix"}]]}}]]}]]}, false]}, {"18": ["icon-dropshadow", [{"19": {"15": ["%sgraphic_icon_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sgraphic_icon_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]]}], {"16": [{"14": ["font-size", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_font_size", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_font_size", [{"3": "keyPrefix"}]]}}]]}, false]}, {"14": ["width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_width", [{"3": "keyPrefix"}]]}}]]}, false]}, {"18": ["text-color", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-shadow", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]}]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_width", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["style", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_style", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_style", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_radius", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_radius", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_radius", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_radius", [{"3": "keyPrefix"}]]}}]]}]]}, {"4": [{"9": ["changed", {"15": ["('auto', get(%s), get-base(%s))", [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, [{"6": "auto"}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["height", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, false]}, {"14": ["line-height", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_height", [{"3": "keyPrefix"}]]}}]]}, false]}]]}, {"18": ["background-color", [{"19": {"15": ["%sgraphic_icon_bg_color", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sgraphic_icon_bg_color_alt", [{"3": "keyPrefix"}]]}}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_type", [{"3": "keyPrefix"}]]}}]]}, "==", {"19": "svg"}]}, [{"23": [[{"15": ["%s:hover .x-graphic-icon > svg", [{"3": "selector"}]]}, {"15": ["%s[class*=\"active\"] .x-graphic-icon > svg", [{"3": "selector"}]]}, {"15": ["[data-x-effect-provider*=\"colors\"]:hover %s .x-graphic-icon > svg ", [{"3": "selector"}]]}], [{"14": ["fill", {"9": ["global-color", {"15": ["(get(%s))", [{"19": {"15": ["%sgraphic_icon_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_color_alt", [{"3": "keyPrefix"}]]}}]]}]]}, false]}, {"18": ["icon-dropshadow", [{"19": {"15": ["%sgraphic_icon_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sgraphic_icon_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_has_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_has_alt", [{"3": "keyPrefix"}]]}}]]}, "==", {"10": true}]}, [{"23": [[{"15": ["%s:hover .x-graphic-icon", [{"3": "selector"}]]}, {"15": ["%s[class*=\"active\"] .x-graphic-icon", [{"3": "selector"}]]}, {"15": ["[data-x-effect-provider*=\"colors\"]:hover %s .x-graphic-icon ", [{"3": "selector"}]]}], [{"18": ["text-color-alt", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["border-alt", [{"11": ["width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_width", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["style", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_style", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_style", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_border_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_border_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["background-color-alt", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_bg_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_bg_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_bg_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_bg_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_type", [{"3": "keyPrefix"}]]}}]]}, "!=", {"19": "svg"}]}, [{"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-shadow-alt", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_icon_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_icon_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_type", [{"3": "keyPrefix"}]]}}]]}, "!=", {"6": "none"}]}, [{"23": [[{"15": ["%s .x-graphic-image ", [{"3": "selector"}]]}], [{"4": [{"9": ["is-set", {"15": ["(get(%s), get-base(%s))", [{"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["max-width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_image_max_width", [{"3": "keyPrefix"}]]}}]]}, false]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sgraphic_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sgraphic_type", [{"3": "keyPrefix"}]]}}]]}, "!=", {"6": "none"}]}, [{"23": [[{"15": ["%s .x-toggle ", [{"3": "selector"}]]}], [{"18": ["text-color", [{"11": ["base", {"9": ["get", "(toggle_color)", [{"10": "toggle_color"}]]}]}, {"11": ["alt", {"9": ["get", "(toggle_color_alt)", [{"10": "toggle_color_alt"}]]}]}]]}]]}, {"23": [[{"15": ["%s:hover .x-toggle", [{"3": "selector"}]]}, {"15": ["%s[class*=\"active\"] .x-toggle", [{"3": "selector"}]]}, {"15": ["[data-x-effect-provider*=\"colors\"]:hover %s .x-toggle ", [{"3": "selector"}]]}], [{"18": ["text-color-alt", [{"11": ["base", {"9": ["get", "(toggle_color)", [{"10": "toggle_color"}]]}]}, {"11": ["alt", {"9": ["get", "(toggle_color_alt)", [{"10": "toggle_color_alt"}]]}]}]]}]]}, {"4": [{"9": ["contains", "(get(toggle_type),'burger')", [{"9": ["get", "(toggle_type)", [{"10": "toggle_type"}]]}, {"6": "burger"}]]}, [{"23": [[{"15": ["%s .x-toggle-burger ", [{"3": "selector"}]]}], [{"14": ["width", {"9": ["get", "(toggle_burger_width)", [{"10": "toggle_burger_width"}]]}, false]}, {"14": ["margin", {"21": [{"9": ["get", "(toggle_burger_spacing)", [{"10": "toggle_burger_spacing"}]]}, {"12": "0"}]}, false]}, {"14": ["font-size", {"9": ["get", "(toggle_burger_size)", [{"10": "toggle_burger_size"}]]}, false]}]]}, {"23": [[{"15": ["%s .x-toggle-burger-bun-t ", [{"3": "selector"}]]}], [{"14": ["transform", {"9": ["translate3d", {"15": ["(0, calc(%s * -1), 0)", [{"9": ["get", "(toggle_burger_spacing)", [{"10": "toggle_burger_spacing"}]]}]]}, [{"12": "0"}, {"9": ["calc", {"15": ["(%s * -1)", [{"9": ["get", "(toggle_burger_spacing)", [{"10": "toggle_burger_spacing"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_burger_spacing)", [{"10": "toggle_burger_spacing"}]]}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}, {"12": "0"}]]}, false]}]]}, {"23": [[{"15": ["%s .x-toggle-burger-bun-b ", [{"3": "selector"}]]}], [{"14": ["transform", {"9": ["translate3d", {"15": ["(0, %s, 0)", [{"9": ["get", "(toggle_burger_spacing)", [{"10": "toggle_burger_spacing"}]]}]]}, [{"12": "0"}, {"10": {"15": ["%s", [{"9": ["get", "(toggle_burger_spacing)", [{"10": "toggle_burger_spacing"}]]}]]}}, {"12": "0"}]]}, false]}]]}]]}, {"4": [{"9": ["contains", "(get(toggle_type),'grid')", [{"9": ["get", "(toggle_type)", [{"10": "toggle_type"}]]}, {"6": "grid"}]]}, [{"23": [[{"15": ["%s .x-toggle-grid ", [{"3": "selector"}]]}], [{"14": ["margin", {"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}, false]}, {"14": ["font-size", {"9": ["get", "(toggle_grid_size)", [{"10": "toggle_grid_size"}]]}, false]}]]}, {"23": [[{"15": ["%s .x-toggle-grid-center ", [{"3": "selector"}]]}], [{"14": ["box-shadow", {"24": [{"21": [{"9": ["calc", {"15": ["(%s * -1)", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}, {"9": ["calc", {"15": ["(%s * -1)", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}]}, {"21": [{"12": "0"}, {"9": ["calc", {"15": ["(%s * -1)", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}]}, {"21": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, {"9": ["calc", {"15": ["(%s * -1)", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}]}, {"21": [{"9": ["calc", {"15": ["(%s * -1)", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}, {"12": "0"}]}, {"21": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, {"12": "0"}]}, {"21": [{"9": ["calc", {"15": ["(%s * -1)", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}, "*", {"7": ["-", {"12": "1"}]}]}]]}, {"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}]}, {"21": [{"12": "0"}, {"10": {"15": ["%s", [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]]}}]}, {"21": [{"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}, {"9": ["get", "(toggle_grid_spacing)", [{"10": "toggle_grid_spacing"}]]}]}]}, false]}]]}]]}, {"4": [{"9": ["contains", "(get(toggle_type),'more')", [{"9": ["get", "(toggle_type)", [{"10": "toggle_type"}]]}, {"6": "more"}]]}, [{"23": [[{"15": ["%s .x-toggle-more-h", [{"3": "selector"}]]}, {"15": ["%s .x-toggle-more-v ", [{"3": "selector"}]]}], [{"14": ["margin", {"9": ["get", "(toggle_more_spacing)", [{"10": "toggle_more_spacing"}]]}, false]}, {"14": ["font-size", {"9": ["get", "(toggle_more_size)", [{"10": "toggle_more_size"}]]}, false]}]]}, {"23": [[{"15": ["%s .x-toggle-more-1 ", [{"3": "selector"}]]}], [{"14": ["transform", {"9": ["translate3d", {"15": ["(-%s , 0, 0)", [{"9": ["get", "(toggle_more_spacing)", [{"10": "toggle_more_spacing"}]]}]]}, [{"7": ["-", {"10": {"15": ["%s", [{"9": ["get", "(toggle_more_spacing)", [{"10": "toggle_more_spacing"}]]}]]}}]}, {"12": "0"}, {"12": "0"}]]}, false]}]]}, {"23": [[{"15": ["%s .x-toggle-more-3 ", [{"3": "selector"}]]}], [{"14": ["transform", {"9": ["translate3d", {"15": ["(%s, 0, 0)", [{"9": ["get", "(toggle_more_spacing)", [{"10": "toggle_more_spacing"}]]}]]}, [{"10": {"15": ["%s", [{"9": ["get", "(toggle_more_spacing)", [{"10": "toggle_more_spacing"}]]}]]}}, {"12": "0"}, {"12": "0"}]]}, false]}]]}]]}]]}]]}, {"13": ["particle", [{"3": "isDirectChild"}, {"3": "selector"}, {"11": ["keyPrefix", {"6": ""}]}, {"11": ["duration", {"6": "300ms"}]}, {"11": ["timing_functiom", {"6": "cubic-bezier(0.400, 0.000, 0.200, 1.000)"}]}], [{"4": [{"8": [{"3": "keyPrefix"}, "!=", {"6": ""}]}, [{"20": ["keyPrefix", {"19": {"15": ["%s_", [{"3": "keyPrefix"}]]}}]}]]}, {"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle", [{"3": "keyPrefix"}]]}}]]}, [{"23": [[{"15": ["%s%s ", [{"9": ["maybe-direct-child", "($isDirectChild)", [{"3": "isDirectChild"}]]}, {"3": "selector"}]]}], [{"14": ["width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_width", [{"3": "keyPrefix"}]]}}]]}, false]}, {"14": ["height", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_height", [{"3": "keyPrefix"}]]}}]]}, false]}, {"18": ["border-radius", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sparticle_border_radius", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_border_radius", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_border_radius", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_border_radius", [{"3": "keyPrefix"}]]}}]]}]]}, {"4": [{"9": ["is-gradient", {"15": ["(get(%s))", [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["background-image", {"9": ["build-gradient", {"15": ["(get(%s))", [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}]]}, false]}, {"23": [["& > span"], [{"14": ["background-color", {"10": "unset"}, false]}]]}], {"16": [{"18": ["text-color", [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_color", [{"3": "keyPrefix"}]]}}]]}]]}]}]}, {"14": ["transform-origin", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_transform_origin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_transform_origin", [{"3": "keyPrefix"}]]}}]]}, false]}, {"4": [{"9": ["changed-from", "($duration, '300ms')", [{"3": "duration"}, {"6": "300ms"}]]}, [{"14": ["transition-duration", {"3": "duration"}, false]}]]}, {"4": [{"9": ["changed-from", "($timing_function, 'cubic-bezier(0.400, 0.000, 0.200, 1.000)')", [{"3": "timing_function"}, {"6": "cubic-bezier(0.400, 0.000, 0.200, 1.000)"}]]}, [{"14": ["transition-timing-function", {"3": "timing_function"}, false]}]]}]]}, {"23": [["[data-x-effect-provider*=\"particles\"]:hover &", "&:hover"], [{"23": [[{"15": ["%s%s ", [{"9": ["maybe-direct-child", "($isDirectChild)", [{"3": "isDirectChild"}]]}, {"3": "selector"}]]}], [{"4": [{"9": ["is-set", {"15": ["(get(%s), get-base(%s))", [{"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["transition-delay", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_delay", [{"3": "keyPrefix"}]]}}]]}, false]}]]}, {"4": [{"9": ["changed-from", "($duration, '300ms')", [{"3": "duration"}, {"6": "300ms"}]]}, [{"4": [{"9": ["changed", {"15": ["('none',get(%s),get-base(%s))", [{"19": {"15": ["%sparticle_scale", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sparticle_scale", [{"3": "keyPrefix"}]]}}]]}, [{"6": "none"}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sparticle_scale", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_scale", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sparticle_scale", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sparticle_scale", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["transition-duration", {"24": [{"22": ["0", "ms"]}, {"3": "duration"}]}, false]}], {"16": [{"14": ["transition-duration", {"3": "duration"}, false]}]}]}]]}]]}]]}]]}]]}, {"13": ["anchor", [{"11": ["selector", {"6": "&.x-anchor"}]}, {"11": ["keyPrefix", {"6": ""}]}], [{"4": [{"8": [{"3": "keyPrefix"}, "!=", {"6": ""}]}, [{"20": ["keyPrefix", {"19": {"15": ["%s_", [{"3": "keyPrefix"}]]}}]}]]}, {"23": [[{"15": ["%s ", [{"3": "selector"}]]}], [{"18": ["changedmixin", [{"6": "auto"}, {"19": {"15": ["%sanchor_width", [{"3": "keyPrefix"}]]}}, {"6": "width"}]]}, {"4": [{"9": ["is-set", {"15": ["(get(%s), get-base(%s))", [{"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["min-width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_min_width", [{"3": "keyPrefix"}]]}}]]}, false]}]]}, {"4": [{"9": ["is-set", {"15": ["(get(%s), get-base(%s))", [{"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["max-width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_max_width", [{"3": "keyPrefix"}]]}}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"19": {"15": ["%sanchor_height", [{"3": "keyPrefix"}]]}}, {"6": "height"}]]}, {"4": [{"9": ["is-set", {"15": ["(get(%s), get-base(%s))", [{"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["min-height", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_min_height", [{"3": "keyPrefix"}]]}}]]}, false]}]]}, {"4": [{"9": ["is-set", {"15": ["(get(%s), get-base(%s))", [{"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["max-height", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_max_height", [{"3": "keyPrefix"}]]}}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_margin", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_margin", [{"3": "keyPrefix"}]]}}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_width", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["style", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_style", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_style", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_radius", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_radius", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_radius", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_radius", [{"3": "keyPrefix"}]]}}]]}]]}, {"14": ["font-size", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_base_font_size", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_base_font_size", [{"3": "keyPrefix"}]]}}]]}, false]}, {"18": ["background-color", [{"19": {"15": ["%sanchor_bg_color", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sanchor_bg_color_alt", [{"3": "keyPrefix"}]]}}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}, {"23": [[{"15": ["%s .x-anchor-content ", [{"3": "selector"}]]}], [{"18": ["flexbox", [{"19": {"15": ["%sanchor", [{"3": "keyPrefix"}]]}}]]}, {"18": ["padding", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_padding", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_padding", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_padding", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_padding", [{"3": "keyPrefix"}]]}}]]}]]}]]}, {"23": [[{"15": ["%s:hover", [{"3": "selector"}]]}, {"15": ["%s[class*=\"active\"]", [{"3": "selector"}]]}, {"15": ["[data-x-effect-provider*=\"colors\"]:hover %s ", [{"3": "selector"}]]}], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_width", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_width", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["style", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_style", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_style", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_border_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_border_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["background-color-alt", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_bg_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_bg_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_bg_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_bg_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_box_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_box_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_box_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}, {"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text", [{"3": "keyPrefix"}]]}}]]}, [{"23": [[{"15": ["%s .x-anchor-text ", [{"3": "selector"}]]}], [{"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_overflow", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_overflow", [{"3": "keyPrefix"}]]}}]]}, "and", {"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_flex_direction", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_flex_direction", [{"3": "keyPrefix"}]]}}]]}, "==", {"6": "column"}]}]}, [{"14": ["width", {"22": ["100", "%"]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_margin", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_margin", [{"3": "keyPrefix"}]]}}]]}]]}]]}, {"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_overflow", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_overflow", [{"3": "keyPrefix"}]]}}]]}, [{"23": [[{"15": ["%s .x-anchor-text-primary", [{"3": "selector"}]]}, {"15": ["%s .x-anchor-text-secondary ", [{"3": "selector"}]]}], [{"14": ["overflow", {"10": "hidden"}, false]}, {"14": ["text-overflow", {"10": "ellipsis"}, false]}, {"14": ["white-space", {"10": "nowrap"}, false]}]]}]]}, {"23": [[{"15": ["%s .x-anchor-text-primary ", [{"3": "selector"}]]}], [{"18": ["linotype", [{"11": ["ff", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_font_family", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_font_family", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["fsize", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_font_size", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_font_size", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["fstyle", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_font_style", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_font_style", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["fw", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_font_weight", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_font_weight", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["lh", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_line_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_line_height", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["ls", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_letter_spacing", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_letter_spacing", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["ta", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_align", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_align", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["td", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_decoration", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_decoration", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["tt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_transform", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_transform", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["lsHasOffset", {"10": true}]}]]}, {"18": ["text-shadow", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-color", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}, {"23": [[{"15": ["%s:hover .x-anchor-text-primary", [{"3": "selector"}]]}, {"15": ["%s[class*=\"active\"] .x-anchor-text-primary", [{"3": "selector"}]]}, {"15": ["[data-x-effect-provider*=\"colors\"]:hover %s .x-anchor-text-primary ", [{"3": "selector"}]]}], [{"18": ["text-color-alt", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-shadow-alt", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}, {"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_has_template", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_has_template", [{"3": "keyPrefix"}]]}}]]}, [{"4": [{"8": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_secondary_content", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_secondary_content", [{"3": "keyPrefix"}]]}}]]}, "!=", {"6": ""}]}, "or", {"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_interactive_content_text_secondary_content", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_interactive_content_text_secondary_content", [{"3": "keyPrefix"}]]}}]]}, "!=", {"6": ""}]}]}, [{"23": [[{"15": ["%s .x-anchor-text-secondary ", [{"3": "selector"}]]}], [{"4": [{"9": ["changed", {"15": ["(true,get(%s),get-base(%s))", [{"19": {"15": ["%sanchor_text_reverse", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sanchor_text_reverse", [{"3": "keyPrefix"}]]}}]]}, [{"10": true}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_reverse", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_reverse", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_reverse", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_reverse", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["margin-top", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_spacing", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_spacing", [{"3": "keyPrefix"}]]}}]]}, false]}], {"16": [{"14": ["margin-bottom", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_text_spacing", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_text_spacing", [{"3": "keyPrefix"}]]}}]]}, false]}]}]}, {"18": ["linotype", [{"11": ["ff", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_font_family", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_font_family", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["fsize", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_font_size", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_font_size", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["fstyle", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_font_style", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_font_style", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["fw", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_font_weight", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_font_weight", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["lh", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_line_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_line_height", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["ls", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_letter_spacing", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_letter_spacing", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["ta", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_align", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_align", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["td", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_decoration", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_decoration", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["tt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_transform", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_transform", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["lsHasOffset", {"10": true}]}]]}, {"18": ["text-shadow", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-color", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}, {"23": [[{"15": ["%s:hover .x-anchor-text-secondary", [{"3": "selector"}]]}, {"15": ["%s[class*=\"active\"] .x-anchor-text-secondary", [{"3": "selector"}]]}, {"15": ["[data-x-effect-provider*=\"colors\"]:hover %s .x-anchor-text-secondary ", [{"3": "selector"}]]}], [{"18": ["text-color-alt", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-shadow-alt", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_has_template", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_has_template", [{"3": "keyPrefix"}]]}}]]}, "and", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_graphic", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_graphic", [{"3": "keyPrefix"}]]}}]]}]}, [{"18": ["graphic", [{"3": "selector"}, {"11": ["keyPrefix", {"19": {"15": ["%sanchor", [{"3": "keyPrefix"}]]}}]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_has_template", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_has_template", [{"3": "keyPrefix"}]]}}]]}, "and", {"8": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, "==", {"6": "menu-item"}]}, "and", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator", [{"3": "keyPrefix"}]]}}]]}]}]}, [{"23": [[{"15": ["%s .x-anchor-sub-indicator ", [{"3": "selector"}]]}], [{"18": ["changedmixin", [{"6": "auto"}, {"19": {"15": ["%sanchor_sub_indicator_width", [{"3": "keyPrefix"}]]}}, {"6": "width"}]]}, {"4": [{"9": ["changed", {"15": ["('auto',get(%s),get-base(%s))", [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}, {"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, [{"6": "auto"}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}]]}, [{"14": ["height", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, false]}, {"14": ["line-height", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_height", [{"3": "keyPrefix"}]]}}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_margin", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_margin", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_margin", [{"3": "keyPrefix"}]]}}]]}]]}, {"14": ["font-size", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_font_size", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_font_size", [{"3": "keyPrefix"}]]}}]]}, false]}, {"18": ["text-shadow", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-color", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}, {"23": [[{"15": ["%s:hover .x-anchor-sub-indicator", [{"3": "selector"}]]}, {"15": ["%s[class*=\"active\"] .x-anchor-sub-indicator", [{"3": "selector"}]]}, {"15": ["[data-x-effect-provider*=\"colors\"]:hover %s .x-anchor-sub-indicator ", [{"3": "selector"}]]}], [{"18": ["text-color-alt", [{"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}, {"18": ["text-shadow-alt", [{"11": ["dimensions", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_dimensions", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["base", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color", [{"3": "keyPrefix"}]]}}]]}]}, {"11": ["alt", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_sub_indicator_text_shadow_color_alt", [{"3": "keyPrefix"}]]}}]]}]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, "==", {"6": "menu-item"}]}, [{"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"18": ["particle", [{"11": ["isDirectChild", {"10": true}]}, {"11": ["selector", {"6": ".is-primary"}]}, {"11": ["keyPrefix", {"19": {"15": ["%sanchor_primary", [{"3": "keyPrefix"}]]}}]}]]}]]}, {"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"18": ["particle", [{"11": ["isDirectChild", {"10": true}]}, {"11": ["selector", {"6": ".is-secondary"}]}, {"11": ["keyPrefix", {"19": {"15": ["%sanchor_secondary", [{"3": "keyPrefix"}]]}}]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, "!=", {"6": "menu-item"}]}, [{"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_primary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_primary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"18": ["particle", [{"11": ["isDirectChild", {"10": true}]}, {"11": ["selector", {"6": ".is-primary"}]}, {"11": ["keyPrefix", {"19": {"15": ["%sanchor_primary", [{"3": "keyPrefix"}]]}}]}]]}]]}, {"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_secondary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_secondary_particle", [{"3": "keyPrefix"}]]}}]]}, [{"18": ["particle", [{"11": ["isDirectChild", {"10": true}]}, {"11": ["selector", {"6": ".is-secondary"}]}, {"11": ["keyPrefix", {"19": {"15": ["%sanchor_secondary", [{"3": "keyPrefix"}]]}}]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_type", [{"3": "keyPrefix"}]]}}]]}, "==", {"6": "menu-item"}]}, [{"23": [[{"15": ["%s", [{"3": "selector"}]]}, {"15": ["%s :not([data-x-particle]) ", [{"3": "selector"}]]}], [{"18": ["motion", [{"19": "transition"}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_duration", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_duration", [{"3": "keyPrefix"}]]}}]]}, {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sanchor_timing_function", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%sanchor_timing_function", [{"3": "keyPrefix"}]]}}]]}]]}]]}]]}]]}, {"13": ["flexbox", [{"11": ["keyPrefix", {"6": ""}]}], [{"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%s_flexbox", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%s_flexbox", [{"3": "keyPrefix"}]]}}]]}, [{"14": ["display", {"10": "flex"}, false]}, {"14": ["flex-direction", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%s_flex_direction", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%s_flex_direction", [{"3": "keyPrefix"}]]}}]]}, false]}, {"14": ["justify-content", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%s_flex_justify", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%s_flex_justify", [{"3": "keyPrefix"}]]}}]]}, false]}, {"14": ["align-items", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%s_flex_align", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%s_flex_align", [{"3": "keyPrefix"}]]}}]]}, false]}, {"18": ["changedmixin", [{"6": "0"}, {"19": {"15": ["%s_flex_gap", [{"3": "keyPrefix"}]]}}, {"6": "gap"}]]}, {"4": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%s_flex_wrap", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%s_flex_wrap", [{"3": "keyPrefix"}]]}}]]}, [{"14": ["flex-wrap", {"10": "wrap"}, false]}, {"14": ["align-content", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%s_flex_align", [{"3": "keyPrefix"}]]}}]]}, [{"19": {"15": ["%s_flex_align", [{"3": "keyPrefix"}]]}}]]}, false]}]]}]]}]]}, {"13": ["bg", [{"11": ["prefix", {"6": ""}]}], [{"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sbg_lower_type", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%sbg_lower_type", [{"3": "prefix"}]]}}]]}, "==", {"6": "color"}]}, [{"18": ["bg-layer-color", [{"19": {"15": ["%sbg_lower_", [{"3": "prefix"}]]}}, {"19": "lower"}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sbg_lower_type", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%sbg_lower_type", [{"3": "prefix"}]]}}]]}, "==", {"6": "img"}]}, [{"18": ["bg-layer-img", [{"19": {"15": ["%sbg_lower_", [{"3": "prefix"}]]}}, {"19": "lower"}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sbg_lower_type", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%sbg_lower_type", [{"3": "prefix"}]]}}]]}, "==", {"6": "image"}]}, [{"18": ["bg-layer-background-image", [{"19": {"15": ["%sbg_lower_", [{"3": "prefix"}]]}}, {"19": "lower"}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sbg_upper_type", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%sbg_upper_type", [{"3": "prefix"}]]}}]]}, "==", {"6": "color"}]}, [{"18": ["bg-layer-color", [{"19": {"15": ["%sbg_upper_", [{"3": "prefix"}]]}}, {"19": "upper"}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sbg_upper_type", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%sbg_upper_type", [{"3": "prefix"}]]}}]]}, "==", {"6": "img"}]}, [{"18": ["bg-layer-img", [{"19": {"15": ["%sbg_upper_", [{"3": "prefix"}]]}}, {"19": "upper"}]]}]]}, {"4": [{"8": [{"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%sbg_upper_type", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%sbg_upper_type", [{"3": "prefix"}]]}}]]}, "==", {"6": "image"}]}, [{"18": ["bg-layer-background-image", [{"19": {"15": ["%sbg_upper_", [{"3": "prefix"}]]}}, {"19": "upper"}]]}]]}]]}, {"13": ["bg-layer-img", [{"3": "prefix"}, {"3": "type"}], [{"23": [[{"15": ["& > .x-bg > .x-bg-layer-%s-img img ", [{"3": "type"}]]}], [{"14": ["object-fit", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%simg_object_fit", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%simg_object_fit", [{"3": "prefix"}]]}}]]}, false]}, {"14": ["object-position", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%simg_object_position", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%simg_object_position", [{"3": "prefix"}]]}}]]}, false]}]]}]]}, {"13": ["bg-layer-background-image", [{"3": "prefix"}, {"3": "type"}], [{"20": ["image", {"9": ["resolve-image", {"15": ["(get(%s, true))", [{"19": {"15": ["%simage", [{"3": "prefix"}]]}}]]}, [{"9": ["get", {"15": ["(%s, true)", [{"19": {"15": ["%simage", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%simage", [{"3": "prefix"}]]}}, {"10": true}]]}]]}]}, {"23": [[{"15": ["& > .x-bg > .x-bg-layer-%s-image ", [{"3": "type"}]]}], [{"14": ["background-image", {"19": {"15": ["url(%s)", [{"3": "image"}]]}}, false]}, {"14": ["background-repeat", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%simage_repeat", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%simage_repeat", [{"3": "prefix"}]]}}]]}, false]}, {"14": ["background-position", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%simage_position", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%simage_position", [{"3": "prefix"}]]}}]]}, false]}, {"14": ["background-size", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%simage_size", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%simage_size", [{"3": "prefix"}]]}}]]}, false]}, {"14": ["background-attachment", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["%simage_attachment", [{"3": "prefix"}]]}}]]}, [{"19": {"15": ["%simage_attachment", [{"3": "prefix"}]]}}]]}, false]}]]}]]}, {"13": ["bg-layer-color", [{"3": "prefix"}, {"3": "type"}], [{"23": [[{"15": ["& > .x-bg > .x-bg-layer-%s-color ", [{"3": "type"}]]}], [{"18": ["background-color", [{"19": {"15": ["%scolor", [{"3": "prefix"}]]}}]]}]]}]]}, {"13": ["changedmixin", [{"11": ["default", {"6": ""}]}, {"11": ["key", {"6": ""}]}, {"11": ["property", {"6": ""}]}], [{"4": [{"8": [{"9": ["changed", "($default, get($key), get-base($key))", [{"3": "default"}, {"9": ["get", "($key)", [{"3": "key"}]]}, {"9": ["get-base", "($key)", [{"3": "key"}]]}]]}, "or", {"9": ["get", {"15": ["(%s)", [{"19": {"15": ["_has_changes_%s", [{"3": "key"}]]}}]]}, [{"19": {"15": ["_has_changes_%s", [{"3": "key"}]]}}]]}]}, [{"14": [{"15": ["%s", [{"3": "property"}]]}, {"9": ["get", "($key)", [{"3": "key"}]]}, false]}]]}]]}, {"25": ["parameters", [], [{"20": ["styleId", {"9": ["get", "(style_id)", [{"10": "style_id"}]]}]}, {"26": [["key"], {"9": ["get", "(_bp_var_keys)", [{"10": "_bp_var_keys"}]]}, [{"14": [{"15": ["%s", [{"9": ["normalize-parameter-key", "($key, $styleId)", [{"3": "key"}, {"3": "styleId"}]]}]]}, {"9": ["pre-process", "(get($key))", [{"9": ["get", "($key)", [{"3": "key"}]]}]]}, false]}]]}]]}, {"25": ["graphic", [{"3": "selector"}, {"11": ["keyPrefix", {"6": ""}]}, {"3": "noBase"}], [{"18": ["graphic", [{"3": "selector"}, {"3": "keyPrefix"}, {"3": "noBase"}]]}]]}, {"25": ["anchor", [{"11": ["selector", {"6": "&.x-anchor"}]}, {"11": ["keyPrefix", {"6": ""}]}], [{"18": ["anchor", [{"3": "selector"}, {"3": "keyPrefix"}]]}]]}, {"25": ["particle", [{"3": "isDirectChild"}, {"3": "selector"}], [{"18": ["particle", [{"11": ["isDirectChild", {"3": "isDirectChild"}]}, {"11": ["selector", {"3": "selector"}]}, {"11": ["keyPrefix", {"6": ""}]}, {"11": ["duration", {"9": ["get", "(duration)", [{"10": "duration"}]]}]}, {"11": ["timing_function", {"9": ["get", "(timing_function)", [{"10": "timing_function"}]]}]}]]}]]}, {"25": ["effects", [{"3": "selectors"}, {"3": "transition_base"}, {"3": "animation_base"}, {"3": "provided_scroll_transition"}], [{"18": ["changedmixin", [{"12": "1"}, {"10": "effects_opacity"}, {"6": "opacity"}]]}, {"4": [{"9": ["is-set", "(get(effects_filter), get-base(effects_filter))", [{"9": ["get", "(effects_filter)", [{"10": "effects_filter"}]]}, {"9": ["get-base", "(effects_filter)", [{"10": "effects_filter"}]]}]]}, [{"14": ["filter", {"9": ["get", "(effects_filter)", [{"10": "effects_filter"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(effects_transform), get-base(effects_transform))", [{"9": ["get", "(effects_transform)", [{"10": "effects_transform"}]]}, {"9": ["get-base", "(effects_transform)", [{"10": "effects_transform"}]]}]]}, [{"14": ["transform", {"9": ["get", "(effects_transform)", [{"10": "effects_transform"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "50% 50%"}, {"10": "effects_transform_origin"}, {"6": "transform-origin"}]]}, {"4": [{"8": [{"9": ["contains", "(get(effects_transform),'3d')", [{"9": ["get", "(effects_transform)", [{"10": "effects_transform"}]]}, {"6": "3d"}]]}, "or", {"8": [{"9": ["contains", "(get(effects_transform),'Z' )", [{"9": ["get", "(effects_transform)", [{"10": "effects_transform"}]]}, {"6": "Z"}]]}, "or", {"8": [{"9": ["contains", "(get(effects_transform_alt),'3d')", [{"9": ["get", "(effects_transform_alt)", [{"10": "effects_transform_alt"}]]}, {"6": "3d"}]]}, "or", {"8": [{"9": ["contains", "(get(effects_transform_alt),'Z' )", [{"9": ["get", "(effects_transform_alt)", [{"10": "effects_transform_alt"}]]}, {"6": "Z"}]]}, "or", {"8": [{"9": ["contains", "(get(effects_transform_enter),'3d')", [{"9": ["get", "(effects_transform_enter)", [{"10": "effects_transform_enter"}]]}, {"6": "3d"}]]}, "or", {"8": [{"9": ["contains", "(get(effects_transform_enter),'Z' )", [{"9": ["get", "(effects_transform_enter)", [{"10": "effects_transform_enter"}]]}, {"6": "Z"}]]}, "or", {"8": [{"9": ["contains", "(get(effects_transform_exit),'3d')", [{"9": ["get", "(effects_transform_exit)", [{"10": "effects_transform_exit"}]]}, {"6": "3d"}]]}, "or", {"9": ["contains", "(get(effects_transform_exit),'Z' )", [{"9": ["get", "(effects_transform_exit)", [{"10": "effects_transform_exit"}]]}, {"6": "Z"}]]}]}]}]}]}]}]}]}, [{"14": ["transform-style", {"10": "preserve-3d"}, false]}]]}, {"18": ["changedmixin", [{"6": "visible"}, {"10": "effects_backface_visibility"}, {"6": "backface-visibility"}]]}, {"18": ["changedmixin", [{"6": "normal"}, {"10": "effects_mix_blend_mode"}, {"6": "mix-blend-mode"}]]}, {"4": [{"9": ["is-set", "(get(effects_backdrop_filter), get-base(effects_backdrop_filter))", [{"9": ["get", "(effects_backdrop_filter)", [{"10": "effects_backdrop_filter"}]]}, {"9": ["get-base", "(effects_backdrop_filter)", [{"10": "effects_backdrop_filter"}]]}]]}, [{"14": ["-webkit-backdrop-filter", {"9": ["get", "(effects_backdrop_filter)", [{"10": "effects_backdrop_filter"}]]}, false]}, {"14": ["backdrop-filter", {"9": ["get", "(effects_backdrop_filter)", [{"10": "effects_backdrop_filter"}]]}, false]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(effects_alt)", [{"10": "effects_alt"}]]}, "==", {"10": true}]}, "and", {"8": [{"9": ["get", "(effects_type_alt)", [{"10": "effects_type_alt"}]]}, "==", {"6": "animation"}]}]}, [{"18": ["motion", [{"19": "animation"}, {"9": ["get", "('effects_duration_animation_alt')", [{"6": "effects_duration_animation_alt"}]]}, {"9": ["get", "('effects_timing_function_animation_alt')", [{"6": "effects_timing_function_animation_alt"}]]}, {"9": ["get", "('animation_base')", [{"6": "animation_base"}]]}]]}]]}, {"18": ["motion", [{"19": "transition"}, {"9": ["get", "(effects_duration)", [{"10": "effects_duration"}]]}, {"9": ["get", "(effects_timing_function)", [{"10": "effects_timing_function"}]]}, {"3": "transition_base"}]]}, {"26": [["selector"], {"3": "selectors"}, [{"23": [[{"15": ["%s ", [{"3": "selector"}]]}], [{"18": ["motion", [{"19": "transition"}, {"9": ["get", "('effects_duration')", [{"6": "effects_duration"}]]}, {"9": ["get", "('effects_timing_function')", [{"6": "effects_timing_function"}]]}, {"9": ["get", "('transition_base')", [{"6": "transition_base"}]]}]]}]]}]]}, {"4": [{"9": ["get", "(effects_scroll)", [{"10": "effects_scroll"}]]}, [{"4": [{"8": [{"9": ["get", "(effects_pointer_events_scroll)", [{"10": "effects_pointer_events_scroll"}]]}, "==", {"6": "auto"}]}, [{"23": [["&.x-effect-exit", "&.x-effect-entering", "&.x-effect-exiting"], [{"14": ["pointer-events", {"10": "auto"}, true]}]]}]]}, {"4": [{"7": ["not", {"3": "provided_scroll_transition"}]}, [{"23": [[{"15": [":where(body:not(.tco-disable-effects)) &:is(.x-effect-exit%s .x-effect-entering%s .x-effect-exiting) ", [{"6": ","}, {"6": ","}]]}], [{"14": ["animation-delay", {"9": ["get", "(effects_delay_scroll)", [{"10": "effects_delay_scroll"}]]}, false]}, {"14": ["animation-duration", {"9": ["get", "(effects_duration_scroll)", [{"10": "effects_duration_scroll"}]]}, false]}, {"14": ["animation-timing-function", {"9": ["get", "(effects_timing_function_scroll)", [{"10": "effects_timing_function_scroll"}]]}, false]}, {"14": ["transition-delay", {"9": ["get", "(effects_delay_scroll)", [{"10": "effects_delay_scroll"}]]}, false]}, {"14": ["transition-duration", {"9": ["get", "(effects_duration_scroll)", [{"10": "effects_duration_scroll"}]]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(effects_timing_function_scroll)", [{"10": "effects_timing_function_scroll"}]]}, false]}]]}]]}, {"23": [["&:is(.x-effect-enter)"], [{"4": [{"8": [{"8": [{"9": ["get", "(effects_opacity_enter)", [{"10": "effects_opacity_enter"}]]}, "!=", {"12": "1"}]}, "or", {"8": [{"9": ["get", "(effects_opacity_exit)", [{"10": "effects_opacity_exit"}]]}, "!=", {"12": "1"}]}]}, [{"14": ["opacity", {"9": ["get", "(effects_opacity_enter)", [{"10": "effects_opacity_enter"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(effects_filter_enter), get-base(effects_filter_enter))", [{"9": ["get", "(effects_filter_enter)", [{"10": "effects_filter_enter"}]]}, {"9": ["get-base", "(effects_filter_enter)", [{"10": "effects_filter_enter"}]]}]]}, [{"14": ["filter", {"9": ["get", "(effects_filter_enter)", [{"10": "effects_filter_enter"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(effects_type_scroll)", [{"10": "effects_type_scroll"}]]}, "==", {"6": "transform"}]}, [{"4": [{"9": ["is-set", "(get(effects_transform_enter), get-base(effects_transform_enter))", [{"9": ["get", "(effects_transform_enter)", [{"10": "effects_transform_enter"}]]}, {"9": ["get-base", "(effects_transform_enter)", [{"10": "effects_transform_enter"}]]}]]}, [{"14": ["transform", {"9": ["get", "(effects_transform_enter)", [{"10": "effects_transform_enter"}]]}, false]}]]}]]}]]}, {"23": [[":where(body:not(.tco-disable-effects)) &:is(.x-effect-exit)"], [{"4": [{"8": [{"8": [{"9": ["get", "(effects_opacity_exit)", [{"10": "effects_opacity_exit"}]]}, "!=", {"12": "1"}]}, "or", {"8": [{"9": ["get", "(effects_opacity_enter)", [{"10": "effects_opacity_enter"}]]}, "!=", {"12": "1"}]}]}, [{"14": ["opacity", {"9": ["get", "(effects_opacity_exit)", [{"10": "effects_opacity_exit"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(effects_filter_exit), get-base(effects_filter_exit))", [{"9": ["get", "(effects_filter_exit)", [{"10": "effects_filter_exit"}]]}, {"9": ["get-base", "(effects_filter_exit)", [{"10": "effects_filter_exit"}]]}]]}, [{"14": ["filter", {"9": ["get", "(effects_filter_exit)", [{"10": "effects_filter_exit"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(effects_type_scroll)", [{"10": "effects_type_scroll"}]]}, "==", {"6": "transform"}]}, [{"4": [{"9": ["is-set", "(get(effects_transform_exit), get-base(effects_transform_exit))", [{"9": ["get", "(effects_transform_exit)", [{"10": "effects_transform_exit"}]]}, {"9": ["get-base", "(effects_transform_exit)", [{"10": "effects_transform_exit"}]]}]]}, [{"14": ["transform", {"9": ["get", "(effects_transform_exit)", [{"10": "effects_transform_exit"}]]}, false]}]]}]]}]]}]]}, {"4": [{"9": ["get", "(effects_alt)", [{"10": "effects_alt"}]]}, [{"23": [[":where(body:not(.tco-disable-effects)) &:hover", ":where(body:not(.tco-disable-effects)) [data-x-effect-provider*=\"effects\"]:hover &"], [{"4": [{"8": [{"8": [{"9": ["get", "(effects_opacity_alt)", [{"10": "effects_opacity_alt"}]]}, "!=", {"12": "1"}]}, "or", {"8": [{"8": [{"9": ["get", "(effects_opacity)", [{"10": "effects_opacity"}]]}, "!=", {"12": "1"}]}, "or", {"8": [{"8": [{"9": ["get", "(effects_scroll)", [{"10": "effects_scroll"}]]}, "and", {"8": [{"9": ["get", "(effects_opacity_enter)", [{"10": "effects_opacity_enter"}]]}, "!=", {"12": "1"}]}]}, "or", {"8": [{"9": ["get", "(effects_opacity_exit)", [{"10": "effects_opacity_exit"}]]}, "!=", {"12": "1"}]}]}]}]}, [{"14": ["opacity", {"9": ["get", "(effects_opacity_alt)", [{"10": "effects_opacity_alt"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(effects_filter_alt), get-base(effects_filter_alt))", [{"9": ["get", "(effects_filter_alt)", [{"10": "effects_filter_alt"}]]}, {"9": ["get-base", "(effects_filter_alt)", [{"10": "effects_filter_alt"}]]}]]}, [{"14": ["filter", {"9": ["get", "(effects_filter_alt)", [{"10": "effects_filter_alt"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(effects_type_alt)", [{"10": "effects_type_alt"}]]}, "==", {"6": "transform"}]}, [{"4": [{"9": ["is-set", "(get(effects_transform_alt), get-base(effects_transform_alt))", [{"9": ["get", "(effects_transform_alt)", [{"10": "effects_transform_alt"}]]}, {"9": ["get-base", "(effects_transform_alt)", [{"10": "effects_transform_alt"}]]}]]}, [{"14": ["transform", {"9": ["get", "(effects_transform_alt)", [{"10": "effects_transform_alt"}]]}, false]}]]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", "(effects_mask)", [{"10": "effects_mask"}]]}, "and", {"8": [{"9": ["get", "(effects_type_mask)", [{"10": "effects_type_mask"}]]}, "!=", {"10": "none"}]}]}, [{"4": [{"8": [{"9": ["get", "(effects_type_mask)", [{"10": "effects_type_mask"}]]}, "==", {"10": "linear"}]}, [{"20": ["alphaLinearOuter", {"9": ["to-bool", "(get(effects_mask_linear_application) == inner)", [{"8": [{"9": ["get", "(effects_mask_linear_application)", [{"10": "effects_mask_linear_application"}]]}, "==", {"10": "inner"}]}]]}]}, {"20": ["alphaLinearInner", {"9": ["to-bool", "(get(effects_mask_linear_application) == outer)", [{"8": [{"9": ["get", "(effects_mask_linear_application)", [{"10": "effects_mask_linear_application"}]]}, "==", {"10": "outer"}]}]]}]}, {"14": ["-webkit-mask-image", {"9": ["linear-gradient", {"15": ["(%s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s)", [{"9": ["get", "(effects_mask_linear_direction)", [{"10": "effects_mask_linear_direction"}]]}, {"3": "alphaLinearOuter"}, {"9": ["get", "(effects_mask_linear_outer_stop_begin)", [{"10": "effects_mask_linear_outer_stop_begin"}]]}, {"3": "alphaLinearInner"}, {"9": ["get", "(effects_mask_linear_inner_stop_begin)", [{"10": "effects_mask_linear_inner_stop_begin"}]]}, {"3": "alphaLinearInner"}, {"9": ["get", "(effects_mask_linear_inner_stop_end)", [{"10": "effects_mask_linear_inner_stop_end"}]]}, {"3": "alphaLinearOuter"}, {"9": ["get", "(effects_mask_linear_outer_stop_end)", [{"10": "effects_mask_linear_outer_stop_end"}]]}]]}, [{"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_direction)", [{"10": "effects_mask_linear_direction"}]]}]]}}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearOuter"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearOuter"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_outer_stop_begin)", [{"10": "effects_mask_linear_outer_stop_begin"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearInner"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearInner"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_inner_stop_begin)", [{"10": "effects_mask_linear_inner_stop_begin"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearInner"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearInner"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_inner_stop_end)", [{"10": "effects_mask_linear_inner_stop_end"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearOuter"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearOuter"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_outer_stop_end)", [{"10": "effects_mask_linear_outer_stop_end"}]]}]]}}]}]]}, false]}, {"14": ["mask-image", {"9": ["linear-gradient", {"15": ["(%s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s)", [{"9": ["get", "(effects_mask_linear_direction)", [{"10": "effects_mask_linear_direction"}]]}, {"3": "alphaLinearOuter"}, {"9": ["get", "(effects_mask_linear_outer_stop_begin)", [{"10": "effects_mask_linear_outer_stop_begin"}]]}, {"3": "alphaLinearInner"}, {"9": ["get", "(effects_mask_linear_inner_stop_begin)", [{"10": "effects_mask_linear_inner_stop_begin"}]]}, {"3": "alphaLinearInner"}, {"9": ["get", "(effects_mask_linear_inner_stop_end)", [{"10": "effects_mask_linear_inner_stop_end"}]]}, {"3": "alphaLinearOuter"}, {"9": ["get", "(effects_mask_linear_outer_stop_end)", [{"10": "effects_mask_linear_outer_stop_end"}]]}]]}, [{"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_direction)", [{"10": "effects_mask_linear_direction"}]]}]]}}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearOuter"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearOuter"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_outer_stop_begin)", [{"10": "effects_mask_linear_outer_stop_begin"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearInner"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearInner"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_inner_stop_begin)", [{"10": "effects_mask_linear_inner_stop_begin"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearInner"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearInner"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_inner_stop_end)", [{"10": "effects_mask_linear_inner_stop_end"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaLinearOuter"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaLinearOuter"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_linear_outer_stop_end)", [{"10": "effects_mask_linear_outer_stop_end"}]]}]]}}]}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(effects_type_mask)", [{"10": "effects_type_mask"}]]}, "==", {"10": "radial"}]}, [{"20": ["alphaRadialOuter", {"9": ["to-bool", "(get(effects_mask_radial_application) == inner)", [{"8": [{"9": ["get", "(effects_mask_radial_application)", [{"10": "effects_mask_radial_application"}]]}, "==", {"10": "inner"}]}]]}]}, {"20": ["alphaRadialInner", {"9": ["to-bool", "(get(effects_mask_radial_application) == outer)", [{"8": [{"9": ["get", "(effects_mask_radial_application)", [{"10": "effects_mask_radial_application"}]]}, "==", {"10": "outer"}]}]]}]}, {"14": ["-webkit-mask-image", {"9": ["radial-gradient", {"15": ["(%s %s at %s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s)", [{"9": ["get", "(effects_mask_radial_shape)", [{"10": "effects_mask_radial_shape"}]]}, {"9": ["get", "(effects_mask_radial_size)", [{"10": "effects_mask_radial_size"}]]}, {"9": ["get", "(effects_mask_radial_center)", [{"10": "effects_mask_radial_center"}]]}, {"3": "alphaRadialInner"}, {"9": ["get", "(effects_mask_radial_inner_stop)", [{"10": "effects_mask_radial_inner_stop"}]]}, {"3": "alphaRadialOuter"}, {"9": ["get", "(effects_mask_radial_outer_stop)", [{"10": "effects_mask_radial_outer_stop"}]]}]]}, [{"21": [{"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_shape)", [{"10": "effects_mask_radial_shape"}]]}]]}}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_size)", [{"10": "effects_mask_radial_size"}]]}]]}}, {"10": "at"}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_center)", [{"10": "effects_mask_radial_center"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaRadialInner"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaRadialInner"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_inner_stop)", [{"10": "effects_mask_radial_inner_stop"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaRadialOuter"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaRadialOuter"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_outer_stop)", [{"10": "effects_mask_radial_outer_stop"}]]}]]}}]}]]}, false]}, {"14": ["mask-image", {"9": ["radial-gradient", {"15": ["(%s %s at %s, rgba(0, 0, 0, %s) %s, rgba(0, 0, 0, %s) %s)", [{"9": ["get", "(effects_mask_radial_shape)", [{"10": "effects_mask_radial_shape"}]]}, {"9": ["get", "(effects_mask_radial_size)", [{"10": "effects_mask_radial_size"}]]}, {"9": ["get", "(effects_mask_radial_center)", [{"10": "effects_mask_radial_center"}]]}, {"3": "alphaRadialInner"}, {"9": ["get", "(effects_mask_radial_inner_stop)", [{"10": "effects_mask_radial_inner_stop"}]]}, {"3": "alphaRadialOuter"}, {"9": ["get", "(effects_mask_radial_outer_stop)", [{"10": "effects_mask_radial_outer_stop"}]]}]]}, [{"21": [{"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_shape)", [{"10": "effects_mask_radial_shape"}]]}]]}}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_size)", [{"10": "effects_mask_radial_size"}]]}]]}}, {"10": "at"}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_center)", [{"10": "effects_mask_radial_center"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaRadialInner"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaRadialInner"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_inner_stop)", [{"10": "effects_mask_radial_inner_stop"}]]}]]}}]}, {"21": [{"9": ["rgba", {"15": ["(0, 0, 0, %s)", [{"3": "alphaRadialOuter"}]]}, [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"10": {"15": ["%s", [{"3": "alphaRadialOuter"}]]}}]]}, {"10": {"15": ["%s", [{"9": ["get", "(effects_mask_radial_outer_stop)", [{"10": "effects_mask_radial_outer_stop"}]]}]]}}]}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(effects_type_mask)", [{"10": "effects_type_mask"}]]}, "==", {"10": "image"}]}, [{"14": ["-webkit-mask-image", {"9": ["url", {"15": ["(%s)", [{"9": ["resolve-image", "(get(effects_mask_image_src))", [{"9": ["get", "(effects_mask_image_src)", [{"10": "effects_mask_image_src"}]]}]]}]]}, [{"10": {"15": ["%s", [{"9": ["resolve-image", "(get(effects_mask_image_src))", [{"9": ["get", "(effects_mask_image_src)", [{"10": "effects_mask_image_src"}]]}]]}]]}}]]}, false]}, {"14": ["mask-image", {"9": ["url", {"15": ["(%s)", [{"9": ["resolve-image", "(get(effects_mask_image_src))", [{"9": ["get", "(effects_mask_image_src)", [{"10": "effects_mask_image_src"}]]}]]}]]}, [{"10": {"15": ["%s", [{"9": ["resolve-image", "(get(effects_mask_image_src))", [{"9": ["get", "(effects_mask_image_src)", [{"10": "effects_mask_image_src"}]]}]]}]]}}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(effects_type_mask)", [{"10": "effects_type_mask"}]]}, "==", {"10": "custom"}]}, [{"14": ["-webkit-mask-image", {"9": ["get", "(effects_mask_custom_mask_image)", [{"10": "effects_mask_custom_mask_image"}]]}, false]}, {"14": ["mask-image", {"9": ["get", "(effects_mask_custom_mask_image)", [{"10": "effects_mask_custom_mask_image"}]]}, false]}]]}, {"14": ["-webkit-mask-composite", {"9": ["webkit-mask-composite", "(get(effects_mask_composite))", [{"9": ["get", "(effects_mask_composite)", [{"10": "effects_mask_composite"}]]}]]}, false]}, {"14": ["mask-composite", {"9": ["get", "(effects_mask_composite)", [{"10": "effects_mask_composite"}]]}, false]}, {"14": ["-webkit-mask-position", {"9": ["get", "(effects_mask_position)", [{"10": "effects_mask_position"}]]}, false]}, {"14": ["mask-position", {"9": ["get", "(effects_mask_position)", [{"10": "effects_mask_position"}]]}, false]}, {"14": ["-webkit-mask-size", {"9": ["get", "(effects_mask_size)", [{"10": "effects_mask_size"}]]}, false]}, {"14": ["mask-size", {"9": ["get", "(effects_mask_size)", [{"10": "effects_mask_size"}]]}, false]}, {"14": ["-webkit-mask-repeat", {"9": ["get", "(effects_mask_repeat)", [{"10": "effects_mask_repeat"}]]}, false]}, {"14": ["mask-repeat", {"9": ["get", "(effects_mask_repeat)", [{"10": "effects_mask_repeat"}]]}, false]}]]}]]}, {"25": ["menu", [], [{"4": [{"8": [{"9": ["get", "(menu_type)", [{"10": "menu_type"}]]}, "!=", {"6": "dropdown"}]}, [{"18": ["margin", [{"9": ["get-base", "(menu_margin)", [{"10": "menu_margin"}]]}, {"9": ["get", "(menu_margin)", [{"10": "menu_margin"}]]}]]}, {"14": ["font-size", {"9": ["get", "(menu_base_font_size)", [{"10": "menu_base_font_size"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(menu_type)", [{"10": "menu_type"}]]}, "==", {"6": "inline"}]}, [{"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "left"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "right"}]}]}, [{"18": ["flexbox", [{"6": "menu_col"}]]}], {"16": [{"18": ["flexbox", [{"6": "menu_row"}]]}]}]}, {"14": ["align-self", {"9": ["get", "(menu_align_self)", [{"10": "menu_align_self"}]]}, false]}, {"14": ["flex", {"9": ["get", "(menu_flex)", [{"10": "menu_flex"}]]}, false]}, {"23": [["> li", "> li > a"], [{"14": ["flex", {"9": ["get", "(menu_items_flex)", [{"10": "menu_items_flex"}]]}, false]}]]}]]}, {"4": [{"8": [{"9": ["get", "(menu_type)", [{"10": "menu_type"}]]}, "==", {"6": "layered"}]}, [{"23": [["&", ".x-anchor"], [{"14": ["transition-duration", {"9": ["get", "(effects_duration)", [{"10": "effects_duration"}]]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(effects_timing_function)", [{"10": "effects_timing_function"}]]}, false]}]]}]]}, {"4": [{"8": [{"9": ["get", "(menu_type)", [{"10": "menu_type"}]]}, "==", {"6": "collapsed"}]}, [{"23": [["[data-x-toggle-collapse]"], [{"14": ["transition-duration", {"9": ["get", "(effects_duration)", [{"10": "effects_duration"}]]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(effects_timing_function)", [{"10": "effects_timing_function"}]]}, false]}]]}]]}]]}, {"25": ["section", [], [{"23": [["&.x-section"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "section_overflow"}, {"6": "overflow"}]]}, {"18": ["margin", [{"9": ["get-base", "(section_margin)", [{"10": "section_margin"}]]}, {"9": ["get", "(section_margin)", [{"10": "section_margin"}]]}, {"22": ["0", "px"]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(section_border_width)", [{"10": "section_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(section_border_style)", [{"10": "section_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(section_border_color)", [{"10": "section_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(section_border_color_alt)", [{"10": "section_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(section_border_radius)", [{"10": "section_border_radius"}]]}, {"9": ["get", "(section_border_radius)", [{"10": "section_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(section_padding)", [{"10": "section_padding"}]]}, {"9": ["get", "(section_padding)", [{"10": "section_padding"}]]}, {"22": ["0", "px"]}]]}, {"18": ["changedmixin", [{"6": "1em"}, {"10": "section_base_font_size"}, {"6": "font-size"}]]}, {"4": [{"9": ["is-set", "(get(section_text_align), get-base(section_text_align))", [{"9": ["get", "(section_text_align)", [{"10": "section_text_align"}]]}, {"9": ["get-base", "(section_text_align)", [{"10": "section_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(section_text_align)", [{"10": "section_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "section_bg_color"}, {"10": "section_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(section_box_shadow_dimensions)", [{"10": "section_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(section_box_shadow_color)", [{"10": "section_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(section_box_shadow_color_alt)", [{"10": "section_box_shadow_color_alt"}]]}]}]]}, {"14": ["z-index", {"9": ["get", "(section_z_index)", [{"10": "section_z_index"}]]}, false]}]]}, {"23": [["&.x-section:hover", "&.x-section[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-section"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(section_border_width)", [{"10": "section_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(section_border_style)", [{"10": "section_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(section_border_color)", [{"10": "section_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(section_border_color_alt)", [{"10": "section_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(section_bg_color)", [{"10": "section_bg_color"}]]}, {"9": ["get", "(section_bg_color_alt)", [{"10": "section_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(section_box_shadow_dimensions)", [{"10": "section_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(section_box_shadow_color)", [{"10": "section_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(section_box_shadow_color_alt)", [{"10": "section_box_shadow_color_alt"}]]}]}]]}]]}, {"18": ["bg", []]}]]}, {"25": ["layout-div", [], [{"23": [["&"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_div_overflow_x"}, {"6": "overflow-x"}]]}, {"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_div_overflow_y"}, {"6": "overflow-y"}]]}, {"18": ["flexbox", [{"6": "layout_div"}]]}, {"14": ["flex", {"9": ["get", "(layout_div_flex)", [{"10": "layout_div_flex"}]]}, false]}, {"18": ["changedmixin", [{"6": "relative"}, {"10": "layout_div_position"}, {"6": "position"}]]}, {"4": [{"8": [{"9": ["changed", "('static', get(layout_div_position), get-base(layout_div_position))", [{"6": "static"}, {"9": ["get", "(layout_div_position)", [{"10": "layout_div_position"}]]}, {"9": ["get-base", "(layout_div_position)", [{"10": "layout_div_position"}]]}]]}, "or", {"9": ["get", "(\"_has_changes_layout_div_position\")", [{"19": "_has_changes_layout_div_position"}]]}]}, [{"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_top"}, {"6": "top"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_left"}, {"6": "left"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_right"}, {"6": "right"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_bottom"}, {"6": "bottom"}]]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_z_index"}, {"6": "z-index"}]]}, {"4": [{"8": [{"9": ["get", "(layout_div_global_container)", [{"10": "layout_div_global_container"}]]}, "==", {"10": false}]}, [{"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_div_max_width), get-base(layout_div_max_width))", [{"9": ["get", "(layout_div_max_width)", [{"10": "layout_div_max_width"}]]}, {"9": ["get-base", "(layout_div_max_width)", [{"10": "layout_div_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_div_max_width)", [{"10": "layout_div_max_width"}]]}, false]}]]}]]}, {"4": [{"9": ["is-set", "(get(layout_div_min_width), get-base(layout_div_min_width))", [{"9": ["get", "(layout_div_min_width)", [{"10": "layout_div_min_width"}]]}, {"9": ["get-base", "(layout_div_min_width)", [{"10": "layout_div_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(layout_div_min_width)", [{"10": "layout_div_min_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(layout_div_min_height), get-base(layout_div_min_height))", [{"9": ["get", "(layout_div_min_height)", [{"10": "layout_div_min_height"}]]}, {"9": ["get-base", "(layout_div_min_height)", [{"10": "layout_div_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(layout_div_min_height)", [{"10": "layout_div_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_div_max_height), get-base(layout_div_max_height))", [{"9": ["get", "(layout_div_max_height)", [{"10": "layout_div_max_height"}]]}, {"9": ["get-base", "(layout_div_max_height)", [{"10": "layout_div_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(layout_div_max_height)", [{"10": "layout_div_max_height"}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", "(layout_div_margin)", [{"10": "layout_div_margin"}]]}, {"9": ["get", "(layout_div_margin)", [{"10": "layout_div_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(layout_div_border_width)", [{"10": "layout_div_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_div_border_style)", [{"10": "layout_div_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_border_color)", [{"10": "layout_div_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_border_color_alt)", [{"10": "layout_div_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(layout_div_border_radius)", [{"10": "layout_div_border_radius"}]]}, {"9": ["get", "(layout_div_border_radius)", [{"10": "layout_div_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_div_padding)", [{"10": "layout_div_padding"}]]}, {"9": ["get", "(layout_div_padding)", [{"10": "layout_div_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(layout_div_base_font_size)", [{"10": "layout_div_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_div_text_align), get-base(layout_div_text_align))", [{"9": ["get", "(layout_div_text_align)", [{"10": "layout_div_text_align"}]]}, {"9": ["get-base", "(layout_div_text_align)", [{"10": "layout_div_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(layout_div_text_align)", [{"10": "layout_div_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "layout_div_bg_color"}, {"10": "layout_div_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(layout_div_box_shadow_dimensions)", [{"10": "layout_div_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_box_shadow_color)", [{"10": "layout_div_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_box_shadow_color_alt)", [{"10": "layout_div_box_shadow_color_alt"}]]}]}]]}, {"18": ["aspect-ratio", [{"6": "layout_div_"}]]}, {"23": [["> *"], [{"4": [{"8": [{"9": ["get", "(layout_div_pointer_events)", [{"10": "layout_div_pointer_events"}]]}, "==", {"6": "none-self"}]}, [{"14": ["pointer-events", {"10": "auto"}, false]}]]}]]}, {"23": [[{"15": ["%s& ", [{"9": ["get", "(layout_div_tag)", [{"10": "layout_div_tag"}]]}]]}], [{"4": [{"8": [{"8": [{"9": ["get", "(layout_div_pointer_events)", [{"10": "layout_div_pointer_events"}]]}, "==", {"6": "none-self"}]}, "or", {"8": [{"9": ["get", "(layout_div_pointer_events)", [{"10": "layout_div_pointer_events"}]]}, "==", {"6": "none-all"}]}]}, [{"14": ["pointer-events", {"10": "none"}, false]}]]}]]}]]}, {"23": [["&:hover", "&[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(layout_div_border_width)", [{"10": "layout_div_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_div_border_style)", [{"10": "layout_div_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_border_color)", [{"10": "layout_div_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_border_color_alt)", [{"10": "layout_div_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(layout_div_bg_color)", [{"10": "layout_div_bg_color"}]]}, {"9": ["get", "(layout_div_bg_color_alt)", [{"10": "layout_div_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(layout_div_box_shadow_dimensions)", [{"10": "layout_div_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_box_shadow_color)", [{"10": "layout_div_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_box_shadow_color_alt)", [{"10": "layout_div_box_shadow_color_alt"}]]}]}]]}]]}, {"18": ["bg", []]}]]}, {"25": ["layout-row", [], [{"23": [["&.x-row"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_row_overflow"}, {"6": "overflow"}]]}, {"14": ["z-index", {"9": ["get", "(layout_row_z_index)", [{"10": "layout_row_z_index"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(layout_row_global_container)", [{"10": "layout_row_global_container"}]]}, "==", {"10": false}]}, [{"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_row_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_row_max_width), get-base(layout_row_max_width))", [{"9": ["get", "(layout_row_max_width)", [{"10": "layout_row_max_width"}]]}, {"9": ["get-base", "(layout_row_max_width)", [{"10": "layout_row_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_row_max_width)", [{"10": "layout_row_max_width"}]]}, false]}]]}]]}, {"18": ["margin", [{"9": ["get-base", "(layout_row_margin)", [{"10": "layout_row_margin"}]]}, {"9": ["get", "(layout_row_margin)", [{"10": "layout_row_margin"}]]}]]}, {"4": [{"8": [{"9": ["off", "(get-base(layout_row_margin))", [{"9": ["get-base", "(layout_row_margin)", [{"10": "layout_row_margin"}]]}]]}, "or", {"9": ["empty", "(off(get-base(layout_row_margin)))", [{"9": ["off", "(get-base(layout_row_margin))", [{"9": ["get-base", "(layout_row_margin)", [{"10": "layout_row_margin"}]]}]]}]]}]}, [{"14": ["margin-left", {"10": "auto"}, false]}, {"14": ["margin-right", {"10": "auto"}, false]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(layout_row_border_width)", [{"10": "layout_row_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_row_border_style)", [{"10": "layout_row_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_row_border_color)", [{"10": "layout_row_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_row_border_color_alt)", [{"10": "layout_row_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(layout_row_border_radius)", [{"10": "layout_row_border_radius"}]]}, {"9": ["get", "(layout_row_border_radius)", [{"10": "layout_row_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_row_padding)", [{"10": "layout_row_padding"}]]}, {"9": ["get", "(layout_row_padding)", [{"10": "layout_row_padding"}]]}, {"22": ["1", "px"]}]]}, {"14": ["font-size", {"9": ["get", "(layout_row_base_font_size)", [{"10": "layout_row_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_row_text_align), get-base(layout_row_text_align))", [{"9": ["get", "(layout_row_text_align)", [{"10": "layout_row_text_align"}]]}, {"9": ["get-base", "(layout_row_text_align)", [{"10": "layout_row_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(layout_row_text_align)", [{"10": "layout_row_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "layout_row_bg_color"}, {"10": "layout_row_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(layout_row_box_shadow_dimensions)", [{"10": "layout_row_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_row_box_shadow_color)", [{"10": "layout_row_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_row_box_shadow_color_alt)", [{"10": "layout_row_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["& > .x-row-inner"], [{"4": [{"9": ["is-bool", "(get(layout_row_reverse))", [{"9": ["get", "(layout_row_reverse)", [{"10": "layout_row_reverse"}]]}]]}, [{"4": [{"8": [{"9": ["get", "(layout_row_reverse)", [{"10": "layout_row_reverse"}]]}, "==", {"10": false}]}, [{"14": ["flex-direction", {"10": "row"}, false]}]]}, {"4": [{"8": [{"9": ["get", "(layout_row_reverse)", [{"10": "layout_row_reverse"}]]}, "==", {"10": true}]}, [{"14": ["flex-direction", {"10": "row-reverse"}, false]}]]}], {"16": [{"14": ["flex-direction", {"9": ["get", "(layout_row_reverse)", [{"10": "layout_row_reverse"}]]}, false]}]}]}, {"14": ["justify-content", {"9": ["get", "(layout_row_flex_justify)", [{"10": "layout_row_flex_justify"}]]}, false]}, {"14": ["align-items", {"9": ["get", "(layout_row_flex_align)", [{"10": "layout_row_flex_align"}]]}, false]}, {"14": ["align-content", {"9": ["get", "(layout_row_flex_align)", [{"10": "layout_row_flex_align"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_row_padding), get-base(layout_row_padding))", [{"9": ["get", "(layout_row_padding)", [{"10": "layout_row_padding"}]]}, {"9": ["get-base", "(layout_row_padding)", [{"10": "layout_row_padding"}]]}]]}, [{"14": ["margin", {"21": [{"9": ["calc", {"15": ["((%s / 2) * -1)", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}, [{"8": [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}}, "/", {"12": "2"}]}, "*", {"7": ["-", {"12": "1"}]}]}]]}, {"9": ["calc", {"15": ["((%s / 2) * -1)", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}, [{"8": [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}}, "/", {"12": "2"}]}, "*", {"7": ["-", {"12": "1"}]}]}]]}]}, false]}], {"16": [{"14": ["margin", {"21": [{"9": ["calc", {"15": ["(((%s / 2) + 1px) * -1)", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}, [{"8": [{"8": [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}}, "/", {"12": "2"}]}, "+", {"22": ["1", "px"]}]}, "*", {"7": ["-", {"12": "1"}]}]}]]}, {"9": ["calc", {"15": ["(((%s / 2) + 1px) * -1)", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}, [{"8": [{"8": [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}}, "/", {"12": "2"}]}, "+", {"22": ["1", "px"]}]}, "*", {"7": ["-", {"12": "1"}]}]}]]}]}, false]}]}]}]]}, {"23": [["&.x-row:hover", "&.x-row[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-row"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(layout_row_border_width)", [{"10": "layout_row_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_row_border_style)", [{"10": "layout_row_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_row_border_color)", [{"10": "layout_row_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_row_border_color_alt)", [{"10": "layout_row_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(layout_row_bg_color)", [{"10": "layout_row_bg_color"}]]}, {"9": ["get", "(layout_row_bg_color_alt)", [{"10": "layout_row_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(layout_row_box_shadow_dimensions)", [{"10": "layout_row_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_row_box_shadow_color)", [{"10": "layout_row_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_row_box_shadow_color_alt)", [{"10": "layout_row_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["& > .x-row-inner > *"], [{"4": [{"9": ["changed", "(false,get(layout_row_grow),get-base(layout_row_grow))", [{"10": false}, {"9": ["get", "(layout_row_grow)", [{"10": "layout_row_grow"}]]}, {"9": ["get-base", "(layout_row_grow)", [{"10": "layout_row_grow"}]]}]]}, [{"4": [{"9": ["get", "(layout_row_grow)", [{"10": "layout_row_grow"}]]}, [{"14": ["flex-grow", {"12": "1"}, false]}], {"16": [{"14": ["flex-grow", {"12": "0"}, false]}]}]}]]}, {"4": [{"9": ["is-set", "(get(layout_row_gap_row), get-base(layout_row_gap_row))", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}, {"9": ["get-base", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}, [{"14": ["margin-top", {"9": ["calc", {"15": ["(%s / 2)", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}}, "/", {"12": "2"}]}]]}, false]}, {"14": ["margin-bottom", {"9": ["calc", {"15": ["(%s / 2)", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_row)", [{"10": "layout_row_gap_row"}]]}]]}}, "/", {"12": "2"}]}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_row_gap_column), get-base(layout_row_gap_column))", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}, {"9": ["get-base", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}, [{"14": ["margin-right", {"9": ["calc", {"15": ["(%s / 2)", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}}, "/", {"12": "2"}]}]]}, false]}, {"14": ["margin-left", {"9": ["calc", {"15": ["(%s / 2)", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}}, "/", {"12": "2"}]}]]}, false]}]]}]]}, {"18": ["bg", []]}]]}, {"25": ["layout-row-columns", [], [{"14": ["--gap", {"10": {"15": ["%s", [{"9": ["get", "(layout_row_gap_column)", [{"10": "layout_row_gap_column"}]]}]]}}, false]}]]}, {"25": ["layout-column", [], [{"23": [["&.x-col"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_column_overflow"}, {"6": "overflow"}]]}, {"18": ["flexbox", [{"6": "layout_column"}]]}, {"14": ["z-index", {"9": ["get", "(layout_column_z_index)", [{"10": "layout_column_z_index"}]]}, false]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_column_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_column_min_width), get-base(layout_column_min_width))", [{"9": ["get", "(layout_column_min_width)", [{"10": "layout_column_min_width"}]]}, {"9": ["get-base", "(layout_column_min_width)", [{"10": "layout_column_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(layout_column_min_width)", [{"10": "layout_column_min_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_column_max_width), get-base(layout_column_max_width))", [{"9": ["get", "(layout_column_max_width)", [{"10": "layout_column_max_width"}]]}, {"9": ["get-base", "(layout_column_max_width)", [{"10": "layout_column_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_column_max_width)", [{"10": "layout_column_max_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_column_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(layout_column_min_height), get-base(layout_column_min_height))", [{"9": ["get", "(layout_column_min_height)", [{"10": "layout_column_min_height"}]]}, {"9": ["get-base", "(layout_column_min_height)", [{"10": "layout_column_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(layout_column_min_height)", [{"10": "layout_column_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_column_max_height), get-base(layout_column_max_height))", [{"9": ["get", "(layout_column_max_height)", [{"10": "layout_column_max_height"}]]}, {"9": ["get-base", "(layout_column_max_height)", [{"10": "layout_column_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(layout_column_max_height)", [{"10": "layout_column_max_height"}]]}, false]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(layout_column_border_width)", [{"10": "layout_column_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_column_border_style)", [{"10": "layout_column_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_column_border_color)", [{"10": "layout_column_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_column_border_color_alt)", [{"10": "layout_column_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(layout_column_border_radius)", [{"10": "layout_column_border_radius"}]]}, {"9": ["get", "(layout_column_border_radius)", [{"10": "layout_column_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_column_padding)", [{"10": "layout_column_padding"}]]}, {"9": ["get", "(layout_column_padding)", [{"10": "layout_column_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(layout_column_base_font_size)", [{"10": "layout_column_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_column_text_align), get-base(layout_column_text_align))", [{"9": ["get", "(layout_column_text_align)", [{"10": "layout_column_text_align"}]]}, {"9": ["get-base", "(layout_column_text_align)", [{"10": "layout_column_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(layout_column_text_align)", [{"10": "layout_column_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "layout_column_bg_color"}, {"10": "layout_column_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(layout_column_box_shadow_dimensions)", [{"10": "layout_column_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_column_box_shadow_color)", [{"10": "layout_column_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_column_box_shadow_color_alt)", [{"10": "layout_column_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["&.x-col:hover", "&.x-col[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-col"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(layout_column_border_width)", [{"10": "layout_column_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_column_border_style)", [{"10": "layout_column_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_column_border_color)", [{"10": "layout_column_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_column_border_color_alt)", [{"10": "layout_column_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(layout_column_bg_color)", [{"10": "layout_column_bg_color"}]]}, {"9": ["get", "(layout_column_bg_color_alt)", [{"10": "layout_column_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(layout_column_box_shadow_dimensions)", [{"10": "layout_column_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_column_box_shadow_color)", [{"10": "layout_column_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_column_box_shadow_color_alt)", [{"10": "layout_column_box_shadow_color_alt"}]]}]}]]}]]}, {"18": ["bg", []]}]]}, {"25": ["bar", [], [{"23": [["&.x-bar"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "bar_overflow"}, {"6": "overflow"}]]}, {"4": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "top"}]}, [{"4": [{"8": [{"8": [{"9": ["get", "(bar_position_top)", [{"10": "bar_position_top"}]]}, "==", {"6": "absolute"}]}, "or", {"8": [{"8": [{"9": ["get", "(bar_sticky)", [{"10": "bar_sticky"}]]}, "==", {"10": true}]}, "and", {"8": [{"9": ["get", "(bar_sticky_hide_initially)", [{"10": "bar_sticky_hide_initially"}]]}, "==", {"10": true}]}]}]}, [{"14": ["width", {"9": ["calc", {"15": ["(100\\% - (%s * 2))", [{"9": ["get", "(bar_margin_sides)", [{"10": "bar_margin_sides"}]]}]]}, [{"8": [{"22": ["100", "%"]}, "-", {"8": [{"10": {"15": ["%s", [{"9": ["get", "(bar_margin_sides)", [{"10": "bar_margin_sides"}]]}]]}}, "*", {"12": "2"}]}]}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(bar_position_top)", [{"10": "bar_position_top"}]]}, "==", {"6": "absolute"}]}, [{"14": ["margin-top", {"9": ["get", "(bar_margin_top)", [{"10": "bar_margin_top"}]]}, false]}, {"14": ["margin-left", {"9": ["get", "(bar_margin_sides)", [{"10": "bar_margin_sides"}]]}, false]}, {"14": ["margin-right", {"9": ["get", "(bar_margin_sides)", [{"10": "bar_margin_sides"}]]}, false]}]]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "left"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "right"}]}]}, [{"14": ["width", {"9": ["get", "(bar_width)", [{"10": "bar_width"}]]}, false]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "left"}]}, "and", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "right"}]}]}, [{"14": ["height", {"9": ["get", "(bar_height)", [{"10": "bar_height"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(bar_height)", [{"10": "bar_height"}]]}, "==", {"6": "auto"}]}, [{"18": ["padding", [{"9": ["get-base", "(bar_padding)", [{"10": "bar_padding"}]]}, {"9": ["get", "(bar_padding)", [{"10": "bar_padding"}]]}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(bar_border_width)", [{"10": "bar_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(bar_border_style)", [{"10": "bar_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(bar_border_color)", [{"10": "bar_border_color"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(bar_border_radius)", [{"10": "bar_border_radius"}]]}, {"9": ["get", "(bar_border_radius)", [{"10": "bar_border_radius"}]]}]]}, {"14": ["font-size", {"9": ["get", "(bar_base_font_size)", [{"10": "bar_base_font_size"}]]}, false]}, {"18": ["background-color", [{"10": "bar_bg_color"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(bar_box_shadow_dimensions)", [{"10": "bar_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(bar_box_shadow_color)", [{"10": "bar_box_shadow_color"}]]}]}]]}, {"14": ["z-index", {"9": ["get", "(bar_z_index)", [{"10": "bar_z_index"}]]}, false]}]]}, {"23": [[".x-bar-content"], [{"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "left"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "right"}]}]}, [{"18": ["flexbox", [{"6": "bar_col"}]]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "left"}]}, "and", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "right"}]}]}, [{"18": ["flexbox", [{"6": "bar_row"}]]}]]}, {"4": [{"9": ["changed", "('auto',get(bar_content_length),get-base(bar_content_length))", [{"6": "auto"}, {"9": ["get", "(bar_content_length)", [{"10": "bar_content_length"}]]}, {"9": ["get-base", "(bar_content_length)", [{"10": "bar_content_length"}]]}]]}, [{"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "left"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "right"}]}]}, [{"14": ["flex-grow", {"12": "0"}, false]}, {"14": ["flex-shrink", {"12": "1"}, false]}, {"14": ["flex-basis", {"9": ["get", "(bar_content_length)", [{"10": "bar_content_length"}]]}, false]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "left"}]}, "and", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "right"}]}]}, [{"4": [{"7": ["not", {"9": ["get", "(bar_global_container)", [{"10": "bar_global_container"}]]}]}, [{"14": ["flex-grow", {"12": "0"}, false]}, {"14": ["flex-shrink", {"12": "1"}, false]}, {"14": ["flex-basis", {"9": ["get", "(bar_content_length)", [{"10": "bar_content_length"}]]}, false]}]]}]]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "left"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "right"}]}]}, [{"14": ["width", {"9": ["get", "(bar_width)", [{"10": "bar_width"}]]}, false]}, {"4": [{"9": ["is-set", "(get(bar_content_max_length), get-base(bar_content_max_length))", [{"9": ["get", "(bar_content_max_length)", [{"10": "bar_content_max_length"}]]}, {"9": ["get-base", "(bar_content_max_length)", [{"10": "bar_content_max_length"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(bar_content_max_length)", [{"10": "bar_content_max_length"}]]}, false]}]]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "left"}]}, "and", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "right"}]}]}, [{"14": ["height", {"9": ["get", "(bar_height)", [{"10": "bar_height"}]]}, false]}, {"4": [{"7": ["not", {"9": ["get", "(bar_global_container)", [{"10": "bar_global_container"}]]}]}, [{"4": [{"9": ["is-set", "(get(bar_content_max_length), get-base(bar_content_max_length))", [{"9": ["get", "(bar_content_max_length)", [{"10": "bar_content_max_length"}]]}, {"9": ["get-base", "(bar_content_max_length)", [{"10": "bar_content_max_length"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(bar_content_max_length)", [{"10": "bar_content_max_length"}]]}, false]}]]}]]}]]}]]}, {"23": [["&.x-bar-outer-spacers:before", "&.x-bar-outer-spacers:after"], [{"14": ["flex-basis", {"9": ["get", "(bar_outer_spacing)", [{"10": "bar_outer_spacing"}]]}, false]}, {"14": ["width", {"9": ["get", "(bar_outer_spacing)", [{"10": "bar_outer_spacing"}]]}, true]}, {"14": ["height", {"9": ["get", "(bar_outer_spacing)", [{"10": "bar_outer_spacing"}]]}, false]}]]}, {"23": [["&.x-bar-space"], [{"14": ["font-size", {"9": ["get", "(bar_base_font_size)", [{"10": "bar_base_font_size"}]]}, false]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "top"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "bottom"}]}]}, [{"14": ["height", {"9": ["get", "(bar_height)", [{"10": "bar_height"}]]}, false]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "left"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "right"}]}]}, [{"14": ["width", {"9": ["get", "(bar_width)", [{"10": "bar_width"}]]}, false]}, {"14": ["flex-basis", {"9": ["get", "(bar_width)", [{"10": "bar_width"}]]}, false]}]]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "left"}]}, "and", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "right"}]}]}, [{"4": [{"9": ["changed", "('auto',get(bar_height),get-base(bar_height))", [{"6": "auto"}, {"9": ["get", "(bar_height)", [{"10": "bar_height"}]]}, {"9": ["get-base", "(bar_height)", [{"10": "bar_height"}]]}]]}, [{"4": [{"8": [{"9": ["get", "(bar_scroll)", [{"10": "bar_scroll"}]]}, "and", {"9": ["get", "(bar_scroll_buttons)", [{"10": "bar_scroll_buttons"}]]}]}, [{"23": [["&.x-bar .x-bar-scroll-button"], [{"14": ["width", {"9": ["get", "(bar_scroll_buttons_width)", [{"10": "bar_scroll_buttons_width"}]]}, false]}, {"14": ["height", {"9": ["get", "(bar_scroll_buttons_height)", [{"10": "bar_scroll_buttons_height"}]]}, false]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(bar_scroll_buttons_border_width)", [{"10": "bar_scroll_buttons_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(bar_scroll_buttons_border_style)", [{"10": "bar_scroll_buttons_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(bar_scroll_buttons_border_color)", [{"10": "bar_scroll_buttons_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(bar_scroll_buttons_border_color_alt)", [{"10": "bar_scroll_buttons_border_color_alt"}]]}]}]]}, {"14": ["font-size", {"9": ["get", "(bar_scroll_buttons_font_size)", [{"10": "bar_scroll_buttons_font_size"}]]}, false]}, {"18": ["text-color", [{"9": ["get", "(bar_scroll_buttons_color)", [{"10": "bar_scroll_buttons_color"}]]}, {"9": ["get", "(bar_scroll_buttons_color_alt)", [{"10": "bar_scroll_buttons_color_alt"}]]}]]}, {"18": ["background-color", [{"10": "bar_scroll_buttons_bg_color"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(bar_scroll_buttons_box_shadow_dimensions)", [{"10": "bar_scroll_buttons_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(bar_scroll_buttons_box_shadow_color)", [{"10": "bar_scroll_buttons_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(bar_scroll_buttons_box_shadow_color_alt)", [{"10": "bar_scroll_buttons_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["&.x-bar .x-bar-scroll-button:hover"], [{"18": ["text-color-alt", [{"9": ["get", "(bar_scroll_buttons_color)", [{"10": "bar_scroll_buttons_color"}]]}, {"9": ["get", "(bar_scroll_buttons_color_alt)", [{"10": "bar_scroll_buttons_color_alt"}]]}]]}, {"18": ["border-alt", [{"11": ["width", {"9": ["get", "(bar_scroll_buttons_border_width)", [{"10": "bar_scroll_buttons_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(bar_scroll_buttons_border_style)", [{"10": "bar_scroll_buttons_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(bar_scroll_buttons_border_color)", [{"10": "bar_scroll_buttons_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(bar_scroll_buttons_border_color_alt)", [{"10": "bar_scroll_buttons_border_color_alt"}]]}]}]]}, {"18": ["background-color", [{"10": "bar_scroll_buttons_bg_color_alt"}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(bar_scroll_buttons_box_shadow_dimensions)", [{"10": "bar_scroll_buttons_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(bar_scroll_buttons_box_shadow_color)", [{"10": "bar_scroll_buttons_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(bar_scroll_buttons_box_shadow_color_alt)", [{"10": "bar_scroll_buttons_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["&.x-bar .x-bar-scroll-button.is-bck"], [{"14": ["left", {"9": ["get", "(bar_scroll_buttons_offset)", [{"10": "bar_scroll_buttons_offset"}]]}, false]}, {"18": ["border", [{"11": ["radius", {"9": ["get", "(bar_scroll_buttons_bck_border_radius)", [{"10": "bar_scroll_buttons_bck_border_radius"}]]}]}]]}]]}, {"23": [["&.x-bar .x-bar-scroll-button.is-fwd"], [{"14": ["right", {"9": ["get", "(bar_scroll_buttons_offset)", [{"10": "bar_scroll_buttons_offset"}]]}, false]}, {"18": ["border", [{"11": ["radius", {"9": ["get", "(bar_scroll_buttons_fwd_border_radius)", [{"10": "bar_scroll_buttons_fwd_border_radius"}]]}]}]]}]]}]]}]]}]]}, {"18": ["bg", []]}]]}, {"25": ["container", [], [{"23": [["&.x-bar-container"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "container_overflow"}, {"6": "overflow"}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "left"}]}, "or", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "==", {"6": "right"}]}]}, [{"18": ["flexbox", [{"6": "container_col"}]]}]]}, {"4": [{"8": [{"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "left"}]}, "and", {"8": [{"9": ["get", "(_region)", [{"10": "_region"}]]}, "!=", {"6": "right"}]}]}, [{"18": ["flexbox", [{"6": "container_row"}]]}]]}, {"14": ["flex", {"9": ["get", "(container_flex)", [{"10": "container_flex"}]]}, false]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "container_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(container_min_width), get-base(container_min_width))", [{"9": ["get", "(container_min_width)", [{"10": "container_min_width"}]]}, {"9": ["get-base", "(container_min_width)", [{"10": "container_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(container_min_width)", [{"10": "container_min_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(container_max_width), get-base(container_max_width))", [{"9": ["get", "(container_max_width)", [{"10": "container_max_width"}]]}, {"9": ["get-base", "(container_max_width)", [{"10": "container_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(container_max_width)", [{"10": "container_max_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "container_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(container_min_height), get-base(container_min_height))", [{"9": ["get", "(container_min_height)", [{"10": "container_min_height"}]]}, {"9": ["get-base", "(container_min_height)", [{"10": "container_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(container_min_height)", [{"10": "container_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(container_max_height), get-base(container_max_height))", [{"9": ["get", "(container_max_height)", [{"10": "container_max_height"}]]}, {"9": ["get-base", "(container_max_height)", [{"10": "container_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(container_max_height)", [{"10": "container_max_height"}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", "(container_margin)", [{"10": "container_margin"}]]}, {"9": ["get", "(container_margin)", [{"10": "container_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(container_border_width)", [{"10": "container_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(container_border_style)", [{"10": "container_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(container_border_color)", [{"10": "container_border_color"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(container_border_radius)", [{"10": "container_border_radius"}]]}, {"9": ["get", "(container_border_radius)", [{"10": "container_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(container_padding)", [{"10": "container_padding"}]]}, {"9": ["get", "(container_padding)", [{"10": "container_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(container_base_font_size)", [{"10": "container_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(container_text_align), get-base(container_text_align))", [{"9": ["get", "(container_text_align)", [{"10": "container_text_align"}]]}, {"9": ["get-base", "(container_text_align)", [{"10": "container_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(container_text_align)", [{"10": "container_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "container_bg_color"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(container_box_shadow_dimensions)", [{"10": "container_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(container_box_shadow_color)", [{"10": "container_box_shadow_color"}]]}]}]]}, {"14": ["z-index", {"9": ["get", "(container_z_index)", [{"10": "container_z_index"}]]}, false]}]]}, {"18": ["bg", []]}]]}, {"25": ["layout-cell", [], [{"23": [["&.x-cell"], [{"4": [{"8": [{"9": ["get", "(layout_cell_column_start)", [{"10": "layout_cell_column_start"}]]}, "==", {"6": ""}]}, [{"20": ["column_start", {"10": "auto"}]}], {"16": [{"20": ["column_start", {"9": ["get", "(layout_cell_column_start)", [{"10": "layout_cell_column_start"}]]}]}]}]}, {"4": [{"8": [{"9": ["get", "(layout_cell_column_end)", [{"10": "layout_cell_column_end"}]]}, "==", {"6": ""}]}, [{"20": ["column_end", {"10": "auto"}]}], {"16": [{"20": ["column_end", {"9": ["get", "(layout_cell_column_end)", [{"10": "layout_cell_column_end"}]]}]}]}]}, {"4": [{"8": [{"9": ["get", "(layout_cell_row_start)", [{"10": "layout_cell_row_start"}]]}, "==", {"6": ""}]}, [{"20": ["row_start", {"10": "auto"}]}], {"16": [{"20": ["row_start", {"9": ["get", "(layout_cell_row_start)", [{"10": "layout_cell_row_start"}]]}]}]}]}, {"4": [{"8": [{"9": ["get", "(layout_cell_row_end)", [{"10": "layout_cell_row_end"}]]}, "==", {"6": ""}]}, [{"20": ["row_end", {"10": "auto"}]}], {"16": [{"20": ["row_end", {"9": ["get", "(layout_cell_row_end)", [{"10": "layout_cell_row_end"}]]}]}]}]}, {"14": ["grid-column-start", {"3": "column_start"}, false]}, {"14": ["grid-column-end", {"3": "column_end"}, false]}, {"14": ["grid-row-start", {"3": "row_start"}, false]}, {"14": ["grid-row-end", {"3": "row_end"}, false]}, {"14": ["justify-self", {"9": ["get", "(layout_cell_justify_self)", [{"10": "layout_cell_justify_self"}]]}, false]}, {"14": ["align-self", {"9": ["get", "(layout_cell_align_self)", [{"10": "layout_cell_align_self"}]]}, false]}, {"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_cell_overflow"}, {"6": "overflow"}]]}, {"18": ["flexbox", [{"6": "layout_cell"}]]}, {"14": ["z-index", {"9": ["get", "(layout_cell_z_index)", [{"10": "layout_cell_z_index"}]]}, false]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_cell_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_cell_min_width), get-base(layout_cell_min_width))", [{"9": ["get", "(layout_cell_min_width)", [{"10": "layout_cell_min_width"}]]}, {"9": ["get-base", "(layout_cell_min_width)", [{"10": "layout_cell_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(layout_cell_min_width)", [{"10": "layout_cell_min_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_cell_max_width), get-base(layout_cell_max_width))", [{"9": ["get", "(layout_cell_max_width)", [{"10": "layout_cell_max_width"}]]}, {"9": ["get-base", "(layout_cell_max_width)", [{"10": "layout_cell_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_cell_max_width)", [{"10": "layout_cell_max_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_cell_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(layout_cell_min_height), get-base(layout_cell_min_height))", [{"9": ["get", "(layout_cell_min_height)", [{"10": "layout_cell_min_height"}]]}, {"9": ["get-base", "(layout_cell_min_height)", [{"10": "layout_cell_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(layout_cell_min_height)", [{"10": "layout_cell_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_cell_max_height), get-base(layout_cell_max_height))", [{"9": ["get", "(layout_cell_max_height)", [{"10": "layout_cell_max_height"}]]}, {"9": ["get-base", "(layout_cell_max_height)", [{"10": "layout_cell_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(layout_cell_max_height)", [{"10": "layout_cell_max_height"}]]}, false]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(layout_cell_border_width)", [{"10": "layout_cell_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_cell_border_style)", [{"10": "layout_cell_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_cell_border_color)", [{"10": "layout_cell_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_cell_border_color_alt)", [{"10": "layout_cell_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(layout_cell_border_radius)", [{"10": "layout_cell_border_radius"}]]}, {"9": ["get", "(layout_cell_border_radius)", [{"10": "layout_cell_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_cell_padding)", [{"10": "layout_cell_padding"}]]}, {"9": ["get", "(layout_cell_padding)", [{"10": "layout_cell_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(layout_cell_base_font_size)", [{"10": "layout_cell_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_cell_text_align), get-base(layout_cell_text_align))", [{"9": ["get", "(layout_cell_text_align)", [{"10": "layout_cell_text_align"}]]}, {"9": ["get-base", "(layout_cell_text_align)", [{"10": "layout_cell_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(layout_cell_text_align)", [{"10": "layout_cell_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "layout_cell_bg_color"}, {"10": "layout_cell_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(layout_cell_box_shadow_dimensions)", [{"10": "layout_cell_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_cell_box_shadow_color)", [{"10": "layout_cell_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_cell_box_shadow_color_alt)", [{"10": "layout_cell_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["&.x-cell:hover", "&.x-cell[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-cell"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(layout_cell_border_width)", [{"10": "layout_cell_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_cell_border_style)", [{"10": "layout_cell_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_cell_border_color)", [{"10": "layout_cell_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_cell_border_color_alt)", [{"10": "layout_cell_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(layout_cell_bg_color)", [{"10": "layout_cell_bg_color"}]]}, {"9": ["get", "(layout_cell_bg_color_alt)", [{"10": "layout_cell_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(layout_cell_box_shadow_dimensions)", [{"10": "layout_cell_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_cell_box_shadow_color)", [{"10": "layout_cell_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_cell_box_shadow_color_alt)", [{"10": "layout_cell_box_shadow_color_alt"}]]}]}]]}]]}, {"18": ["bg", []]}]]}, {"25": ["layout-grid", [], [{"23": [["&.x-grid"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_grid_overflow"}, {"6": "overflow"}]]}, {"4": [{"8": [{"9": ["changed", "('row', get(layout_grid_auto_flow_direction), get-base(layout_grid_auto_flow_direction))", [{"6": "row"}, {"9": ["get", "(layout_grid_auto_flow_direction)", [{"10": "layout_grid_auto_flow_direction"}]]}, {"9": ["get-base", "(layout_grid_auto_flow_direction)", [{"10": "layout_grid_auto_flow_direction"}]]}]]}, "or", {"9": ["changed", "('', get(layout_grid_auto_flow_packing), get-base(layout_grid_auto_flow_packing))", [{"6": ""}, {"9": ["get", "(layout_grid_auto_flow_packing)", [{"10": "layout_grid_auto_flow_packing"}]]}, {"9": ["get-base", "(layout_grid_auto_flow_packing)", [{"10": "layout_grid_auto_flow_packing"}]]}]]}]}, [{"14": ["grid-auto-flow", {"21": [{"9": ["get", "(layout_grid_auto_flow_direction)", [{"10": "layout_grid_auto_flow_direction"}]]}, {"9": ["get", "(layout_grid_auto_flow_packing)", [{"10": "layout_grid_auto_flow_packing"}]]}]}, false]}]]}, {"14": ["grid-gap", {"21": [{"9": ["get", "(layout_grid_gap_row)", [{"10": "layout_grid_gap_row"}]]}, {"9": ["get", "(layout_grid_gap_column)", [{"10": "layout_grid_gap_column"}]]}]}, false]}, {"14": ["justify-content", {"9": ["get", "(layout_grid_justify_content)", [{"10": "layout_grid_justify_content"}]]}, false]}, {"14": ["align-content", {"9": ["get", "(layout_grid_align_content)", [{"10": "layout_grid_align_content"}]]}, false]}, {"14": ["justify-items", {"9": ["get", "(layout_grid_justify_items)", [{"10": "layout_grid_justify_items"}]]}, false]}, {"14": ["align-items", {"9": ["get", "(layout_grid_align_items)", [{"10": "layout_grid_align_items"}]]}, false]}, {"14": ["z-index", {"9": ["get", "(layout_grid_z_index)", [{"10": "layout_grid_z_index"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(layout_grid_global_container)", [{"10": "layout_grid_global_container"}]]}, "==", {"10": false}]}, [{"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_grid_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_grid_max_width), get-base(layout_grid_max_width))", [{"9": ["get", "(layout_grid_max_width)", [{"10": "layout_grid_max_width"}]]}, {"9": ["get-base", "(layout_grid_max_width)", [{"10": "layout_grid_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_grid_max_width)", [{"10": "layout_grid_max_width"}]]}, false]}]]}]]}, {"18": ["margin", [{"9": ["get-base", "(layout_grid_margin)", [{"10": "layout_grid_margin"}]]}, {"9": ["get", "(layout_grid_margin)", [{"10": "layout_grid_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(layout_grid_border_width)", [{"10": "layout_grid_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_grid_border_style)", [{"10": "layout_grid_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_grid_border_color)", [{"10": "layout_grid_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_grid_border_color_alt)", [{"10": "layout_grid_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(layout_grid_border_radius)", [{"10": "layout_grid_border_radius"}]]}, {"9": ["get", "(layout_grid_border_radius)", [{"10": "layout_grid_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_grid_padding)", [{"10": "layout_grid_padding"}]]}, {"9": ["get", "(layout_grid_padding)", [{"10": "layout_grid_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(layout_grid_base_font_size)", [{"10": "layout_grid_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_grid_text_align), get-base(layout_grid_text_align))", [{"9": ["get", "(layout_grid_text_align)", [{"10": "layout_grid_text_align"}]]}, {"9": ["get-base", "(layout_grid_text_align)", [{"10": "layout_grid_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(layout_grid_text_align)", [{"10": "layout_grid_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "layout_grid_bg_color"}, {"10": "layout_grid_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(layout_grid_box_shadow_dimensions)", [{"10": "layout_grid_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_grid_box_shadow_color)", [{"10": "layout_grid_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_grid_box_shadow_color_alt)", [{"10": "layout_grid_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["&.x-grid:hover", "&.x-grid[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-grid"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(layout_grid_border_width)", [{"10": "layout_grid_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_grid_border_style)", [{"10": "layout_grid_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_grid_border_color)", [{"10": "layout_grid_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_grid_border_color_alt)", [{"10": "layout_grid_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(layout_grid_bg_color)", [{"10": "layout_grid_bg_color"}]]}, {"9": ["get", "(layout_grid_bg_color_alt)", [{"10": "layout_grid_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(layout_grid_box_shadow_dimensions)", [{"10": "layout_grid_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_grid_box_shadow_color)", [{"10": "layout_grid_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_grid_box_shadow_color_alt)", [{"10": "layout_grid_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["&.x-grid"], [{"14": ["grid-template-columns", {"9": ["get", "(layout_grid_template_columns)", [{"10": "layout_grid_template_columns"}]]}, false]}, {"14": ["grid-template-rows", {"9": ["get", "(layout_grid_template_rows)", [{"10": "layout_grid_template_rows"}]]}, false]}]]}, {"18": ["bg", []]}]]}, {"25": ["layout-slide-container", [], [{"20": ["isMarquee", {"8": [{"9": ["get", "(layout_slide_container_autoplay)", [{"10": "layout_slide_container_autoplay"}]]}, "==", {"10": "marquee"}]}]}, {"4": [{"8": [{"9": ["get", "(layout_slide_container_layout_type)", [{"10": "layout_slide_container_layout_type"}]]}, "==", {"10": "inline"}]}, [{"14": ["--x-slides-per-page", {"9": ["get", "(layout_slide_container_inline_page_count)", [{"10": "layout_slide_container_inline_page_count"}]]}, false]}, {"14": ["--x-slide-container-gap", {"9": ["get", "(layout_slide_container_inline_gap)", [{"10": "layout_slide_container_inline_gap"}]]}, false]}]]}, {"4": [{"7": ["not", {"3": "isMarquee"}]}, [{"4": [{"8": [{"8": [{"9": ["get", "(layout_slide_container_layout_type)", [{"10": "layout_slide_container_layout_type"}]]}, "==", {"10": "stacked"}]}, "and", {"8": [{"9": ["get", "(layout_slide_container_stacked_entrance)", [{"10": "layout_slide_container_stacked_entrance"}]]}, "==", {"10": "none"}]}]}, [{"14": ["--x-slide-container-transition-duration", {"22": ["0", "ms"]}, false]}], {"17": [{"8": [{"8": [{"9": ["get", "(layout_slide_container_duration)", [{"10": "layout_slide_container_duration"}]]}, "!=", {"22": ["1000", "ms"]}]}, "and", {"8": [{"9": ["get", "(layout_slide_container_duration)", [{"10": "layout_slide_container_duration"}]]}, "!=", {"22": ["1", "s"]}]}]}, [{"14": ["--x-slide-container-transition-duration", {"9": ["get", "(layout_slide_container_duration)", [{"10": "layout_slide_container_duration"}]]}, false]}]]}]}, {"4": [{"8": [{"9": ["get", "(layout_slide_container_timing_function)", [{"10": "layout_slide_container_timing_function"}]]}, "!=", {"9": ["cubic-bezier", "(0.400, 0.000, 0.200, 1.000)", [{"12": "0.400"}, {"12": "0.000"}, {"12": "0.200"}, {"12": "1.000"}]]}]}, [{"14": ["--x-slide-container-transition-timing-function", {"9": ["get", "(layout_slide_container_timing_function)", [{"10": "layout_slide_container_timing_function"}]]}, false]}]]}], {"16": [{"14": ["--x-slide-container-transition-duration", {"22": ["0", "ms"]}, false]}, {"14": ["--x-slide-container-transition-timing-function", {"10": "linear"}, false]}]}]}, {"4": [{"8": [{"8": [{"9": ["get", "(layout_slide_container_autoplay)", [{"10": "layout_slide_container_autoplay"}]]}, "==", {"10": "interval"}]}, "or", {"8": [{"9": ["get", "(layout_slide_container_autoplay)", [{"10": "layout_slide_container_autoplay"}]]}, "==", {"10": "marquee"}]}]}, [{"14": ["--x-slide-container-autoplay-transition-duration", {"9": ["get", "(layout_slide_container_autoplay_duration)", [{"10": "layout_slide_container_autoplay_duration"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "hidden"}, {"10": "layout_slide_container_overflow"}, {"6": "overflow"}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_slide_container_padding)", [{"10": "layout_slide_container_padding"}]]}, {"9": ["get", "(layout_slide_container_padding)", [{"10": "layout_slide_container_padding"}]]}, {"22": ["0", "px"]}]]}, {"14": ["font-size", {"9": ["get", "(layout_slide_container_base_font_size)", [{"10": "layout_slide_container_base_font_size"}]]}, false]}, {"23": [[".x-slide-container-content"], [{"4": [{"8": [{"9": ["get", "(layout_slide_container_content_global_container)", [{"10": "layout_slide_container_content_global_container"}]]}, "==", {"10": false}]}, [{"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_slide_container_content_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_slide_container_content_max_width), get-base(layout_slide_container_content_max_width))", [{"9": ["get", "(layout_slide_container_content_max_width)", [{"10": "layout_slide_container_content_max_width"}]]}, {"9": ["get-base", "(layout_slide_container_content_max_width)", [{"10": "layout_slide_container_content_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_slide_container_content_max_width)", [{"10": "layout_slide_container_content_max_width"}]]}, false]}]]}]]}]]}, {"23": [[".x-slide-container"], [{"4": [{"8": [{"9": ["get", "(layout_slide_container_layout_type)", [{"10": "layout_slide_container_layout_type"}]]}, "==", {"10": "inline"}]}, [{"14": ["align-items", {"9": ["get", "(layout_slide_container_inline_align)", [{"10": "layout_slide_container_inline_align"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(layout_slide_container_layout_type)", [{"10": "layout_slide_container_layout_type"}]]}, "==", {"10": "stacked"}]}, [{"14": ["align-items", {"9": ["get", "(layout_slide_container_stacked_align)", [{"10": "layout_slide_container_stacked_align"}]]}, false]}]]}]]}, {"23": [[".x-slide"], [{"23": [[{"15": ["body:not(.tco-disable-effects) &:where(.x-effect-enter%s .x-effect-exit%s .x-effect-entering%s .x-effect-exiting) ", [{"6": ","}, {"6": ","}, {"6": ","}]]}], [{"14": ["animation-duration", {"9": ["get", "(layout_slide_container_duration)", [{"10": "layout_slide_container_duration"}]]}, false]}, {"14": ["animation-timing-function", {"9": ["get", "(layout_slide_container_timing_function)", [{"10": "layout_slide_container_timing_function"}]]}, false]}, {"14": ["transition-duration", {"9": ["get", "(layout_slide_container_duration)", [{"10": "layout_slide_container_duration"}]]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(layout_slide_container_timing_function)", [{"10": "layout_slide_container_timing_function"}]]}, false]}]]}]]}]]}, {"25": ["layout-slide", [], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_slide_overflow"}, {"6": "overflow"}]]}, {"18": ["flexbox", [{"6": "layout_slide"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_slide_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_slide_min_width), get-base(layout_slide_min_width))", [{"9": ["get", "(layout_slide_min_width)", [{"10": "layout_slide_min_width"}]]}, {"9": ["get-base", "(layout_slide_min_width)", [{"10": "layout_slide_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(layout_slide_min_width)", [{"10": "layout_slide_min_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_slide_max_width), get-base(layout_slide_max_width))", [{"9": ["get", "(layout_slide_max_width)", [{"10": "layout_slide_max_width"}]]}, {"9": ["get-base", "(layout_slide_max_width)", [{"10": "layout_slide_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_slide_max_width)", [{"10": "layout_slide_max_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_slide_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(layout_slide_min_height), get-base(layout_slide_min_height))", [{"9": ["get", "(layout_slide_min_height)", [{"10": "layout_slide_min_height"}]]}, {"9": ["get-base", "(layout_slide_min_height)", [{"10": "layout_slide_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(layout_slide_min_height)", [{"10": "layout_slide_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_slide_max_height), get-base(layout_slide_max_height))", [{"9": ["get", "(layout_slide_max_height)", [{"10": "layout_slide_max_height"}]]}, {"9": ["get-base", "(layout_slide_max_height)", [{"10": "layout_slide_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(layout_slide_max_height)", [{"10": "layout_slide_max_height"}]]}, false]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(layout_slide_border_width)", [{"10": "layout_slide_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_slide_border_style)", [{"10": "layout_slide_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_slide_border_color)", [{"10": "layout_slide_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_slide_border_color_alt)", [{"10": "layout_slide_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(layout_slide_border_radius)", [{"10": "layout_slide_border_radius"}]]}, {"9": ["get", "(layout_slide_border_radius)", [{"10": "layout_slide_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_slide_padding)", [{"10": "layout_slide_padding"}]]}, {"9": ["get", "(layout_slide_padding)", [{"10": "layout_slide_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(layout_slide_base_font_size)", [{"10": "layout_slide_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_slide_text_align), get-base(layout_slide_text_align))", [{"9": ["get", "(layout_slide_text_align)", [{"10": "layout_slide_text_align"}]]}, {"9": ["get-base", "(layout_slide_text_align)", [{"10": "layout_slide_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(layout_slide_text_align)", [{"10": "layout_slide_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "layout_slide_bg_color"}, {"10": "layout_slide_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(layout_slide_box_shadow_dimensions)", [{"10": "layout_slide_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_slide_box_shadow_color)", [{"10": "layout_slide_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_slide_box_shadow_color_alt)", [{"10": "layout_slide_box_shadow_color_alt"}]]}]}]]}, {"23": [["&:hover", "&[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(layout_slide_border_width)", [{"10": "layout_slide_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_slide_border_style)", [{"10": "layout_slide_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_slide_border_color)", [{"10": "layout_slide_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_slide_border_color_alt)", [{"10": "layout_slide_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(layout_slide_bg_color)", [{"10": "layout_slide_bg_color"}]]}, {"9": ["get", "(layout_slide_bg_color_alt)", [{"10": "layout_slide_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(layout_slide_box_shadow_dimensions)", [{"10": "layout_slide_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_slide_box_shadow_color)", [{"10": "layout_slide_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_slide_box_shadow_color_alt)", [{"10": "layout_slide_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [[".x-slide-container.is-inline &"], [{"4": [{"8": [{"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}, "!=", {"10": "auto"}]}, [{"14": ["z-index", {"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}, false]}]]}]]}, {"23": [[".x-slide-container.is-stacked &"], [{"4": [{"8": [{"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}, "==", {"10": "auto"}]}, [{"23": [["&:not(.is-current-slide).x-effect-exit"], [{"14": ["z-index", {"10": "auto"}, false]}]]}, {"23": [["&:not(.is-current-slide).x-effect-holding"], [{"14": ["z-index", {"12": "1"}, false]}]]}, {"23": [["&.is-current-slide"], [{"14": ["z-index", {"12": "2"}, false]}]]}], {"16": [{"23": [["&:not(.is-current-slide).x-effect-exit"], [{"14": ["z-index", {"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}, false]}]]}, {"23": [["&:not(.is-current-slide).x-effect-holding"], [{"14": ["z-index", {"9": ["calc", {"15": ["(%s + 1)", [{"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}]]}}, "+", {"12": "1"}]}]]}, false]}]]}, {"23": [["&.is-current-slide"], [{"14": ["z-index", {"9": ["calc", {"15": ["(%s + 2)", [{"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(layout_slide_z_index)", [{"10": "layout_slide_z_index"}]]}]]}}, "+", {"12": "2"}]}]]}, false]}]]}]}]}]]}, {"18": ["bg", []]}]]}, {"25": ["slide-pagination", [], [{"14": ["--x-slide-pagination-item-bgc", {"9": ["global-color", "(get(slide_pagination_item_bg_color))", [{"9": ["get", "(slide_pagination_item_bg_color)", [{"10": "slide_pagination_item_bg_color"}]]}]]}, false]}, {"14": ["--x-slide-pagination-item-bgc-alt", {"9": ["global-color", "(get(slide_pagination_item_bg_color_alt))", [{"9": ["get", "(slide_pagination_item_bg_color_alt)", [{"10": "slide_pagination_item_bg_color_alt"}]]}]]}, false]}, {"14": ["gap", {"9": ["get", "(slide_pagination_item_gap)", [{"10": "slide_pagination_item_gap"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(slide_pagination_wrap)", [{"10": "slide_pagination_wrap"}]]}, "==", {"10": true}]}, [{"14": ["flex-wrap", {"10": "wrap"}, false]}]]}, {"14": ["justify-content", {"9": ["get", "(slide_pagination_justify)", [{"10": "slide_pagination_justify"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(slide_pagination_direction)", [{"10": "slide_pagination_direction"}]]}, "==", {"10": "row"}]}, [{"4": [{"9": ["changed", "('auto', get(slide_pagination_content_length_main), get-base(slide_pagination_content_length_main))", [{"6": "auto"}, {"9": ["get", "(slide_pagination_content_length_main)", [{"10": "slide_pagination_content_length_main"}]]}, {"9": ["get-base", "(slide_pagination_content_length_main)", [{"10": "slide_pagination_content_length_main"}]]}]]}, [{"14": ["width", {"9": ["get", "(slide_pagination_content_length_main)", [{"10": "slide_pagination_content_length_main"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(slide_pagination_content_max_length_main), get-base(slide_pagination_content_max_length_main))", [{"9": ["get", "(slide_pagination_content_max_length_main)", [{"10": "slide_pagination_content_max_length_main"}]]}, {"9": ["get-base", "(slide_pagination_content_max_length_main)", [{"10": "slide_pagination_content_max_length_main"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(slide_pagination_content_max_length_main)", [{"10": "slide_pagination_content_max_length_main"}]]}, false]}]]}]]}, {"4": [{"8": [{"9": ["get", "(slide_pagination_direction)", [{"10": "slide_pagination_direction"}]]}, "==", {"10": "column"}]}, [{"4": [{"9": ["changed", "('auto', get(slide_pagination_content_length_main), get-base(slide_pagination_content_length_main))", [{"6": "auto"}, {"9": ["get", "(slide_pagination_content_length_main)", [{"10": "slide_pagination_content_length_main"}]]}, {"9": ["get-base", "(slide_pagination_content_length_main)", [{"10": "slide_pagination_content_length_main"}]]}]]}, [{"14": ["height", {"9": ["get", "(slide_pagination_content_length_main)", [{"10": "slide_pagination_content_length_main"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(slide_pagination_content_max_length_main), get-base(slide_pagination_content_max_length_main))", [{"9": ["get", "(slide_pagination_content_max_length_main)", [{"10": "slide_pagination_content_max_length_main"}]]}, {"9": ["get-base", "(slide_pagination_content_max_length_main)", [{"10": "slide_pagination_content_max_length_main"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(slide_pagination_content_max_length_main)", [{"10": "slide_pagination_content_max_length_main"}]]}, false]}]]}]]}, {"18": ["margin", [{"9": ["get-base", "(slide_pagination_margin)", [{"10": "slide_pagination_margin"}]]}, {"9": ["get", "(slide_pagination_margin)", [{"10": "slide_pagination_margin"}]]}]]}, {"14": ["font-size", {"9": ["get", "(slide_pagination_base_font_size)", [{"10": "slide_pagination_base_font_size"}]]}, false]}, {"23": [["li"], [{"4": [{"8": [{"9": ["get", "(slide_pagination_direction)", [{"10": "slide_pagination_direction"}]]}, "==", {"10": "row"}]}, [{"14": ["width", {"9": ["get", "(slide_pagination_item_length_main)", [{"10": "slide_pagination_item_length_main"}]]}, false]}, {"14": ["height", {"9": ["get", "(slide_pagination_item_length_cross)", [{"10": "slide_pagination_item_length_cross"}]]}, false]}, {"23": [["&.is-active"], [{"4": [{"8": [{"9": ["get", "(slide_pagination_item_transform)", [{"10": "slide_pagination_item_transform"}]]}, "==", {"10": "grow-out"}]}, [{"14": ["width", {"9": ["calc", {"15": ["(%s * %s)", [{"9": ["get", "(slide_pagination_item_length_main)", [{"10": "slide_pagination_item_length_main"}]]}, {"9": ["get", "(slide_pagination_item_grow_out_multiplier)", [{"10": "slide_pagination_item_grow_out_multiplier"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(slide_pagination_item_length_main)", [{"10": "slide_pagination_item_length_main"}]]}]]}}, "*", {"10": {"15": ["%s", [{"9": ["get", "(slide_pagination_item_grow_out_multiplier)", [{"10": "slide_pagination_item_grow_out_multiplier"}]]}]]}}]}]]}, false]}]]}]]}]]}, {"4": [{"8": [{"9": ["get", "(slide_pagination_direction)", [{"10": "slide_pagination_direction"}]]}, "==", {"10": "column"}]}, [{"14": ["width", {"9": ["get", "(slide_pagination_item_length_cross)", [{"10": "slide_pagination_item_length_cross"}]]}, false]}, {"14": ["height", {"9": ["get", "(slide_pagination_item_length_main)", [{"10": "slide_pagination_item_length_main"}]]}, false]}, {"23": [["&.is-active"], [{"4": [{"8": [{"9": ["get", "(slide_pagination_item_transform)", [{"10": "slide_pagination_item_transform"}]]}, "==", {"10": "grow-out"}]}, [{"14": ["height", {"9": ["calc", {"15": ["(%s * %s)", [{"9": ["get", "(slide_pagination_item_length_main)", [{"10": "slide_pagination_item_length_main"}]]}, {"9": ["get", "(slide_pagination_item_grow_out_multiplier)", [{"10": "slide_pagination_item_grow_out_multiplier"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(slide_pagination_item_length_main)", [{"10": "slide_pagination_item_length_main"}]]}]]}}, "*", {"10": {"15": ["%s", [{"9": ["get", "(slide_pagination_item_grow_out_multiplier)", [{"10": "slide_pagination_item_grow_out_multiplier"}]]}]]}}]}]]}, false]}]]}]]}]]}, {"14": ["border-radius", {"9": ["get", "(slide_pagination_item_radius)", [{"10": "slide_pagination_item_radius"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(effects_duration)", [{"10": "effects_duration"}]]}, "!=", {"22": ["300", "ms"]}]}, [{"14": ["transition-duration", {"9": ["get", "(effects_duration)", [{"10": "effects_duration"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(effects_timing_function)", [{"10": "effects_timing_function"}]]}, "!=", {"9": ["cubic-bezier", "(0.400, 0.000, 0.200, 1.000)", [{"12": "0.400"}, {"12": "0.000"}, {"12": "0.200"}, {"12": "1.000"}]]}]}, [{"14": ["transition-timing-function", {"9": ["get", "(effects_timing_function)", [{"10": "effects_timing_function"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(slide_pagination_item_transform)", [{"10": "slide_pagination_item_transform"}]]}, "==", {"10": "scale-up"}]}, [{"23": [["&:not(.is-active)"], [{"14": ["transform", {"9": ["scale", {"15": ["(%s)", [{"9": ["get", "(slide_pagination_item_scale_up_starting_scale)", [{"10": "slide_pagination_item_scale_up_starting_scale"}]]}]]}, [{"10": {"15": ["%s", [{"9": ["get", "(slide_pagination_item_scale_up_starting_scale)", [{"10": "slide_pagination_item_scale_up_starting_scale"}]]}]]}}]]}, false]}]]}]]}]]}]]}, {"25": ["form-integration", [], [{"18": ["changedmixin", [{"6": "auto"}, {"10": "form_integration_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(form_integration_max_width), get-base(form_integration_max_width))", [{"9": ["get", "(form_integration_max_width)", [{"10": "form_integration_max_width"}]]}, {"9": ["get-base", "(form_integration_max_width)", [{"10": "form_integration_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(form_integration_max_width)", [{"10": "form_integration_max_width"}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", "(form_integration_margin)", [{"10": "form_integration_margin"}]]}, {"9": ["get", "(form_integration_margin)", [{"10": "form_integration_margin"}]]}]]}]]}, {"25": ["gap", [], [{"4": [{"8": [{"9": ["get", "(gap_direction)", [{"10": "gap_direction"}]]}, "==", {"19": "horizontal"}]}, [{"14": ["padding", {"21": [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"9": ["get", "(gap_size)", [{"10": "gap_size"}]]}]}, false]}], {"16": [{"14": ["padding", {"21": [{"9": ["get", "(gap_size)", [{"10": "gap_size"}]]}, {"12": "0"}, {"12": "0"}, {"12": "0"}]}, false]}]}]}, {"14": ["font-size", {"9": ["get", "(gap_base_font_size)", [{"10": "gap_base_font_size"}]]}, false]}]]}, {"25": ["line", [], [{"4": [{"8": [{"9": ["get", "(line_direction)", [{"10": "line_direction"}]]}, "==", {"6": "horizontal"}]}, [{"4": [{"8": [{"9": ["get", "(scroll_progress_enabled)", [{"10": "scroll_progress_enabled"}]]}, "==", {"10": true}]}, [{"14": ["width", {"9": ["calc", "(var(--x-body-scroll-percentage, 0) * 100%)", [{"8": [{"9": ["var", "(--x-body-scroll-percentage, 0)", [{"7": ["-", {"10": "-x-body-scroll-percentage"}]}, {"12": "0"}]]}, "*", {"22": ["100", "%"]}]}]]}, false]}], {"16": [{"14": ["width", {"9": ["get", "(line_width)", [{"10": "line_width"}]]}, false]}]}]}, {"14": ["max-width", {"9": ["get", "(line_max_width)", [{"10": "line_max_width"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(line_direction)", [{"10": "line_direction"}]]}, "==", {"6": "vertical"}]}, [{"4": [{"8": [{"9": ["get", "(scroll_progress_enabled)", [{"10": "scroll_progress_enabled"}]]}, "==", {"10": true}]}, [{"14": ["height", {"9": ["calc", "(var(--x-body-scroll-percentage, 0) * 100%)", [{"8": [{"9": ["var", "(--x-body-scroll-percentage, 0)", [{"7": ["-", {"10": "-x-body-scroll-percentage"}]}, {"12": "0"}]]}, "*", {"22": ["100", "%"]}]}]]}, false]}], {"16": [{"14": ["height", {"9": ["get", "(line_height)", [{"10": "line_height"}]]}, false]}]}]}, {"14": ["max-height", {"9": ["get", "(line_max_height)", [{"10": "line_max_height"}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", "(line_margin)", [{"10": "line_margin"}]]}, {"9": ["get", "(line_margin)", [{"10": "line_margin"}]]}]]}, {"4": [{"8": [{"9": ["get", "(line_direction)", [{"10": "line_direction"}]]}, "==", {"6": "horizontal"}]}, [{"14": ["border-width", {"21": [{"9": ["get", "(line_size)", [{"10": "line_size"}]]}, {"12": "0"}, {"12": "0"}, {"12": "0"}]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(line_direction)", [{"10": "line_direction"}]]}, "==", {"6": "vertical"}]}, [{"14": ["border-width", {"21": [{"12": "0"}, {"12": "0"}, {"12": "0"}, {"9": ["get", "(line_size)", [{"10": "line_size"}]]}]}, false]}]]}, {"14": ["border-style", {"9": ["get", "(line_style)", [{"10": "line_style"}]]}, false]}, {"14": ["border-color", {"9": ["global-color", "(get(line_color))", [{"9": ["get", "(line_color)", [{"10": "line_color"}]]}]]}, false]}, {"18": ["border-radius", [{"9": ["get-base", "(line_border_radius)", [{"10": "line_border_radius"}]]}, {"9": ["get", "(line_border_radius)", [{"10": "line_border_radius"}]]}]]}, {"14": ["font-size", {"9": ["get", "(line_base_font_size)", [{"10": "line_base_font_size"}]]}, false]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(line_box_shadow_dimensions)", [{"10": "line_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(line_box_shadow_color)", [{"10": "line_box_shadow_color"}]]}]}]]}]]}, {"25": ["global-block", [], []]}, {"25": ["layout-div", [], [{"23": [["&"], [{"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_div_overflow_x"}, {"6": "overflow-x"}]]}, {"18": ["changedmixin", [{"6": "visible"}, {"10": "layout_div_overflow_y"}, {"6": "overflow-y"}]]}, {"18": ["flexbox", [{"6": "layout_div"}]]}, {"14": ["flex", {"9": ["get", "(layout_div_flex)", [{"10": "layout_div_flex"}]]}, false]}, {"18": ["changedmixin", [{"6": "relative"}, {"10": "layout_div_position"}, {"6": "position"}]]}, {"4": [{"8": [{"9": ["changed", "('static', get(layout_div_position), get-base(layout_div_position))", [{"6": "static"}, {"9": ["get", "(layout_div_position)", [{"10": "layout_div_position"}]]}, {"9": ["get-base", "(layout_div_position)", [{"10": "layout_div_position"}]]}]]}, "or", {"9": ["get", "(\"_has_changes_layout_div_position\")", [{"19": "_has_changes_layout_div_position"}]]}]}, [{"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_top"}, {"6": "top"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_left"}, {"6": "left"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_right"}, {"6": "right"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_bottom"}, {"6": "bottom"}]]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_z_index"}, {"6": "z-index"}]]}, {"4": [{"8": [{"9": ["get", "(layout_div_global_container)", [{"10": "layout_div_global_container"}]]}, "==", {"10": false}]}, [{"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(layout_div_max_width), get-base(layout_div_max_width))", [{"9": ["get", "(layout_div_max_width)", [{"10": "layout_div_max_width"}]]}, {"9": ["get-base", "(layout_div_max_width)", [{"10": "layout_div_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(layout_div_max_width)", [{"10": "layout_div_max_width"}]]}, false]}]]}]]}, {"4": [{"9": ["is-set", "(get(layout_div_min_width), get-base(layout_div_min_width))", [{"9": ["get", "(layout_div_min_width)", [{"10": "layout_div_min_width"}]]}, {"9": ["get-base", "(layout_div_min_width)", [{"10": "layout_div_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(layout_div_min_width)", [{"10": "layout_div_min_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "layout_div_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(layout_div_min_height), get-base(layout_div_min_height))", [{"9": ["get", "(layout_div_min_height)", [{"10": "layout_div_min_height"}]]}, {"9": ["get-base", "(layout_div_min_height)", [{"10": "layout_div_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(layout_div_min_height)", [{"10": "layout_div_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(layout_div_max_height), get-base(layout_div_max_height))", [{"9": ["get", "(layout_div_max_height)", [{"10": "layout_div_max_height"}]]}, {"9": ["get-base", "(layout_div_max_height)", [{"10": "layout_div_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(layout_div_max_height)", [{"10": "layout_div_max_height"}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", "(layout_div_margin)", [{"10": "layout_div_margin"}]]}, {"9": ["get", "(layout_div_margin)", [{"10": "layout_div_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(layout_div_border_width)", [{"10": "layout_div_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_div_border_style)", [{"10": "layout_div_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_border_color)", [{"10": "layout_div_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_border_color_alt)", [{"10": "layout_div_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(layout_div_border_radius)", [{"10": "layout_div_border_radius"}]]}, {"9": ["get", "(layout_div_border_radius)", [{"10": "layout_div_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(layout_div_padding)", [{"10": "layout_div_padding"}]]}, {"9": ["get", "(layout_div_padding)", [{"10": "layout_div_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(layout_div_base_font_size)", [{"10": "layout_div_base_font_size"}]]}, false]}, {"4": [{"9": ["is-set", "(get(layout_div_text_align), get-base(layout_div_text_align))", [{"9": ["get", "(layout_div_text_align)", [{"10": "layout_div_text_align"}]]}, {"9": ["get-base", "(layout_div_text_align)", [{"10": "layout_div_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(layout_div_text_align)", [{"10": "layout_div_text_align"}]]}, false]}]]}, {"18": ["background-color", [{"10": "layout_div_bg_color"}, {"10": "layout_div_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(layout_div_box_shadow_dimensions)", [{"10": "layout_div_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_box_shadow_color)", [{"10": "layout_div_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_box_shadow_color_alt)", [{"10": "layout_div_box_shadow_color_alt"}]]}]}]]}, {"18": ["aspect-ratio", [{"6": "layout_div_"}]]}, {"23": [["> *"], [{"4": [{"8": [{"9": ["get", "(layout_div_pointer_events)", [{"10": "layout_div_pointer_events"}]]}, "==", {"6": "none-self"}]}, [{"14": ["pointer-events", {"10": "auto"}, false]}]]}]]}, {"23": [[{"15": ["%s& ", [{"9": ["get", "(layout_div_tag)", [{"10": "layout_div_tag"}]]}]]}], [{"4": [{"8": [{"8": [{"9": ["get", "(layout_div_pointer_events)", [{"10": "layout_div_pointer_events"}]]}, "==", {"6": "none-self"}]}, "or", {"8": [{"9": ["get", "(layout_div_pointer_events)", [{"10": "layout_div_pointer_events"}]]}, "==", {"6": "none-all"}]}]}, [{"14": ["pointer-events", {"10": "none"}, false]}]]}]]}]]}, {"23": [["&:hover", "&[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(layout_div_border_width)", [{"10": "layout_div_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(layout_div_border_style)", [{"10": "layout_div_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_border_color)", [{"10": "layout_div_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_border_color_alt)", [{"10": "layout_div_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(layout_div_bg_color)", [{"10": "layout_div_bg_color"}]]}, {"9": ["get", "(layout_div_bg_color_alt)", [{"10": "layout_div_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(layout_div_box_shadow_dimensions)", [{"10": "layout_div_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(layout_div_box_shadow_color)", [{"10": "layout_div_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(layout_div_box_shadow_color_alt)", [{"10": "layout_div_box_shadow_color_alt"}]]}]}]]}]]}, {"18": ["bg", []]}]]}, {"25": ["layout-dropdown", [], []]}, {"25": ["nav-collapsed", [], []]}, {"25": ["nav-layered", [], []]}, {"25": ["modal", [{"3": "isLayoutElement"}], [{"14": ["transition-duration", {"24": [{"9": ["get", "(modal_duration)", [{"10": "modal_duration"}]]}, {"22": ["0", "s"]}]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(modal_timing_function)", [{"10": "modal_timing_function"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(_offscreen)", [{"10": "_offscreen"}]]}, "!=", {"6": ""}]}, [{"14": ["z-index", {"9": ["calc", {"15": ["((99999999 + %s) * %s)", [{"9": ["get", "(_order)", [{"10": "_order"}]]}, {"9": ["get", "(_depth)", [{"10": "_depth"}]]}]]}, [{"8": [{"8": [{"12": "99999999"}, "+", {"10": {"15": ["%s", [{"9": ["get", "(_order)", [{"10": "_order"}]]}]]}}]}, "*", {"10": {"15": ["%s", [{"9": ["get", "(_depth)", [{"10": "_depth"}]]}]]}}]}]]}, false]}]]}, {"23": [[".x-modal-content-scroll-area"], [{"14": ["font-size", {"9": ["get", "(modal_base_font_size)", [{"10": "modal_base_font_size"}]]}, false]}, {"4": [{"8": [{"7": ["not", {"9": ["get", "(modal_content_ignore_side_padding)", [{"10": "modal_content_ignore_side_padding"}]]}]}, "and", {"9": ["get", "(modal_close_enabled)", [{"10": "modal_close_enabled"}]]}]}, [{"14": ["padding", {"21": [{"12": "0"}, {"9": ["calc", {"15": ["(%s * %s)", [{"9": ["get", "(modal_close_font_size)", [{"10": "modal_close_font_size"}]]}, {"9": ["get", "(modal_close_dimensions)", [{"10": "modal_close_dimensions"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(modal_close_font_size)", [{"10": "modal_close_font_size"}]]}]]}}, "*", {"10": {"15": ["%s", [{"9": ["get", "(modal_close_dimensions)", [{"10": "modal_close_dimensions"}]]}]]}}]}]]}]}, false]}], {"16": [{"14": ["padding", {"12": "0"}, false]}]}]}]]}, {"23": [["&:not(.x-active)"], [{"14": ["transition-delay", {"24": [{"22": ["0", "s"]}, {"9": ["get", "(modal_duration)", [{"10": "modal_duration"}]]}]}, false]}]]}, {"23": [[".x-modal-bg"], [{"18": ["background-color", [{"10": "modal_bg_color"}]]}]]}, {"4": [{"9": ["get", "(modal_close_enabled)", [{"10": "modal_close_enabled"}]]}, [{"23": [[".x-modal-close"], [{"14": ["width", {"9": ["calc", {"15": ["(1em * %s)", [{"9": ["get", "(modal_close_dimensions)", [{"10": "modal_close_dimensions"}]]}]]}, [{"8": [{"22": ["1", "em"]}, "*", {"10": {"15": ["%s", [{"9": ["get", "(modal_close_dimensions)", [{"10": "modal_close_dimensions"}]]}]]}}]}]]}, false]}, {"14": ["height", {"9": ["calc", {"15": ["(1em * %s)", [{"9": ["get", "(modal_close_dimensions)", [{"10": "modal_close_dimensions"}]]}]]}, [{"8": [{"22": ["1", "em"]}, "*", {"10": {"15": ["%s", [{"9": ["get", "(modal_close_dimensions)", [{"10": "modal_close_dimensions"}]]}]]}}]}]]}, false]}, {"14": ["font-size", {"9": ["get", "(modal_close_font_size)", [{"10": "modal_close_font_size"}]]}, false]}, {"18": ["text-color", [{"9": ["get", "(modal_close_color)", [{"10": "modal_close_color"}]]}, {"9": ["get", "(modal_close_color_alt)", [{"10": "modal_close_color_alt"}]]}]]}]]}, {"23": [[".x-modal-close:hover", ".x-modal-close:focus"], [{"18": ["text-color-alt", [{"9": ["get", "(modal_close_color)", [{"10": "modal_close_color"}]]}, {"9": ["get", "(modal_close_color_alt)", [{"10": "modal_close_color_alt"}]]}]]}]]}]]}, {"23": [[".x-modal-content"], [{"4": [{"3": "isLayoutElement"}, [{"18": ["changedmixin", [{"6": "visible"}, {"10": "modal_content_overflow"}, {"6": "overflow"}]]}, {"18": ["flexbox", [{"6": "modal_content"}]]}, {"18": ["changedmixin", [{"6": "100%"}, {"10": "modal_content_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(modal_content_min_width), get-base(modal_content_min_width))", [{"9": ["get", "(modal_content_min_width)", [{"10": "modal_content_min_width"}]]}, {"9": ["get-base", "(modal_content_min_width)", [{"10": "modal_content_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(modal_content_min_width)", [{"10": "modal_content_min_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(modal_content_max_width), get-base(modal_content_max_width))", [{"9": ["get", "(modal_content_max_width)", [{"10": "modal_content_max_width"}]]}, {"9": ["get-base", "(modal_content_max_width)", [{"10": "modal_content_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(modal_content_max_width)", [{"10": "modal_content_max_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "modal_content_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(modal_content_min_height), get-base(modal_content_min_height))", [{"9": ["get", "(modal_content_min_height)", [{"10": "modal_content_min_height"}]]}, {"9": ["get-base", "(modal_content_min_height)", [{"10": "modal_content_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(modal_content_min_height)", [{"10": "modal_content_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(modal_content_max_height), get-base(modal_content_max_height))", [{"9": ["get", "(modal_content_max_height)", [{"10": "modal_content_max_height"}]]}, {"9": ["get-base", "(modal_content_max_height)", [{"10": "modal_content_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(modal_content_max_height)", [{"10": "modal_content_max_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(modal_content_text_align), get-base(modal_content_text_align))", [{"9": ["get", "(modal_content_text_align)", [{"10": "modal_content_text_align"}]]}, {"9": ["get-base", "(modal_content_text_align)", [{"10": "modal_content_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(modal_content_text_align)", [{"10": "modal_content_text_align"}]]}, false]}]]}], {"16": [{"4": [{"9": ["is-set", "(get(modal_content_max_width), get-base(modal_content_max_width))", [{"9": ["get", "(modal_content_max_width)", [{"10": "modal_content_max_width"}]]}, {"9": ["get-base", "(modal_content_max_width)", [{"10": "modal_content_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(modal_content_max_width)", [{"10": "modal_content_max_width"}]]}, false]}]]}]}]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(modal_content_border_width)", [{"10": "modal_content_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(modal_content_border_style)", [{"10": "modal_content_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(modal_content_border_color)", [{"10": "modal_content_border_color"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(modal_content_border_radius)", [{"10": "modal_content_border_radius"}]]}, {"9": ["get", "(modal_content_border_radius)", [{"10": "modal_content_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(modal_content_padding)", [{"10": "modal_content_padding"}]]}, {"9": ["get", "(modal_content_padding)", [{"10": "modal_content_padding"}]]}]]}, {"18": ["background-color", [{"10": "modal_content_bg_color"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(modal_content_box_shadow_dimensions)", [{"10": "modal_content_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(modal_content_box_shadow_color)", [{"10": "modal_content_box_shadow_color"}]]}]}]]}, {"14": ["transition-duration", {"9": ["get", "(modal_duration)", [{"10": "modal_duration"}]]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(modal_timing_function)", [{"10": "modal_timing_function"}]]}, false]}, {"18": ["bg", []]}]]}]]}, {"25": ["frame", [], [{"23": [["&.x-frame"], [{"18": ["changedmixin", [{"6": "auto"}, {"10": "frame_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(frame_max_width), get-base(frame_max_width))", [{"9": ["get", "(frame_max_width)", [{"10": "frame_max_width"}]]}, {"9": ["get-base", "(frame_max_width)", [{"10": "frame_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(frame_max_width)", [{"10": "frame_max_width"}]]}, false]}]]}, {"4": [{"9": ["get", "(frame_overflow)", [{"10": "frame_overflow"}]]}, [{"14": ["overflow", {"10": "hidden"}, false]}], {"16": [{"14": ["overflow", {"10": "visible"}, false]}]}]}, {"18": ["margin", [{"9": ["get-base", "(frame_margin)", [{"10": "frame_margin"}]]}, {"9": ["get", "(frame_margin)", [{"10": "frame_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(frame_border_width)", [{"10": "frame_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(frame_border_style)", [{"10": "frame_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(frame_border_color)", [{"10": "frame_border_color"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(frame_border_radius)", [{"10": "frame_border_radius"}]]}, {"9": ["get", "(frame_border_radius)", [{"10": "frame_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(frame_padding)", [{"10": "frame_padding"}]]}, {"9": ["get", "(frame_padding)", [{"10": "frame_padding"}]]}]]}, {"14": ["font-size", {"9": ["get", "(frame_base_font_size)", [{"10": "frame_base_font_size"}]]}, false]}, {"18": ["background-color", [{"10": "frame_bg_color"}]]}, {"18": ["box-shadow", [{"9": ["get", "(frame_box_shadow_dimensions)", [{"10": "frame_box_shadow_dimensions"}]]}, {"9": ["get", "(frame_box_shadow_color)", [{"10": "frame_box_shadow_color"}]]}]]}]]}, {"23": [[".x-frame-inner"], [{"4": [{"8": [{"9": ["get", "(frame_content_sizing)", [{"10": "frame_content_sizing"}]]}, "==", {"6": "fixed-height"}]}, [{"14": ["padding-bottom", {"9": ["get", "(frame_content_height)", [{"10": "frame_content_height"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(frame_content_sizing)", [{"10": "frame_content_sizing"}]]}, "==", {"6": "aspect-ratio"}]}, [{"14": ["padding-bottom", {"9": ["calc", {"15": ["(\n        %s / %s * 100\\%\n      )", [{"9": ["get", "(frame_content_aspect_ratio_height)", [{"10": "frame_content_aspect_ratio_height"}]]}, {"9": ["get", "(frame_content_aspect_ratio_width)", [{"10": "frame_content_aspect_ratio_width"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(frame_content_aspect_ratio_height)", [{"10": "frame_content_aspect_ratio_height"}]]}]]}}, "/", {"8": [{"10": {"15": ["%s", [{"9": ["get", "(frame_content_aspect_ratio_width)", [{"10": "frame_content_aspect_ratio_width"}]]}]]}}, "*", {"22": ["100", "%"]}]}]}]]}, false]}]]}]]}]]}, {"25": ["dropdown", [{"3": "isLayoutElement"}, {"11": ["selector", {"19": "&"}]}], [{"23": [[{"15": ["%s ", [{"3": "selector"}]]}], [{"4": [{"3": "isLayoutElement"}, [{"18": ["changedmixin", [{"6": "visible"}, {"10": "dropdown_overflow"}, {"6": "overflow"}]]}, {"4": [{"8": [{"9": ["get", "(dropdown_display_inline)", [{"10": "dropdown_display_inline"}]]}, "and", {"9": ["get", "(dropdown_display_inline_fixed)", [{"10": "dropdown_display_inline_fixed"}]]}]}, [{"14": ["position", {"10": "fixed"}, false]}]]}, {"18": ["flexbox", [{"6": "dropdown"}]]}, {"4": [{"8": [{"9": ["get", "(_offscreen)", [{"10": "_offscreen"}]]}, "!=", {"6": ""}]}, [{"14": ["z-index", {"9": ["calc", {"15": ["((99999999 + %s) * %s)", [{"9": ["get", "(_order)", [{"10": "_order"}]]}, {"9": ["get", "(_depth)", [{"10": "_depth"}]]}]]}, [{"8": [{"8": [{"12": "99999999"}, "+", {"10": {"15": ["%s", [{"9": ["get", "(_order)", [{"10": "_order"}]]}]]}}]}, "*", {"10": {"15": ["%s", [{"9": ["get", "(_depth)", [{"10": "_depth"}]]}]]}}]}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "dropdown_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(dropdown_min_width), get-base(dropdown_min_width))", [{"9": ["get", "(dropdown_min_width)", [{"10": "dropdown_min_width"}]]}, {"9": ["get-base", "(dropdown_min_width)", [{"10": "dropdown_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(dropdown_min_width)", [{"10": "dropdown_min_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(dropdown_max_width), get-base(dropdown_max_width))", [{"9": ["get", "(dropdown_max_width)", [{"10": "dropdown_max_width"}]]}, {"9": ["get-base", "(dropdown_max_width)", [{"10": "dropdown_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(dropdown_max_width)", [{"10": "dropdown_max_width"}]]}, false]}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "dropdown_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(dropdown_min_height), get-base(dropdown_min_height))", [{"9": ["get", "(dropdown_min_height)", [{"10": "dropdown_min_height"}]]}, {"9": ["get-base", "(dropdown_min_height)", [{"10": "dropdown_min_height"}]]}]]}, [{"14": ["min-height", {"9": ["get", "(dropdown_min_height)", [{"10": "dropdown_min_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(dropdown_max_height), get-base(dropdown_max_height))", [{"9": ["get", "(dropdown_max_height)", [{"10": "dropdown_max_height"}]]}, {"9": ["get-base", "(dropdown_max_height)", [{"10": "dropdown_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(dropdown_max_height)", [{"10": "dropdown_max_height"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(dropdown_text_align), get-base(dropdown_text_align))", [{"9": ["get", "(dropdown_text_align)", [{"10": "dropdown_text_align"}]]}, {"9": ["get-base", "(dropdown_text_align)", [{"10": "dropdown_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(dropdown_text_align)", [{"10": "dropdown_text_align"}]]}, false]}]]}], {"16": [{"18": ["changedmixin", [{"6": "auto"}, {"10": "dropdown_width"}, {"6": "width"}]]}]}]}, {"14": ["font-size", {"9": ["get", "(dropdown_base_font_size)", [{"10": "dropdown_base_font_size"}]]}, false]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(dropdown_border_width)", [{"10": "dropdown_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(dropdown_border_style)", [{"10": "dropdown_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(dropdown_border_color)", [{"10": "dropdown_border_color"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(dropdown_border_radius)", [{"10": "dropdown_border_radius"}]]}, {"9": ["get", "(dropdown_border_radius)", [{"10": "dropdown_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(dropdown_padding)", [{"10": "dropdown_padding"}]]}, {"9": ["get", "(dropdown_padding)", [{"10": "dropdown_padding"}]]}]]}, {"18": ["background-color", [{"10": "dropdown_bg_color"}]]}, {"18": ["box-shadow", [{"9": ["get", "(dropdown_box_shadow_dimensions)", [{"10": "dropdown_box_shadow_dimensions"}]]}, {"9": ["get", "(dropdown_box_shadow_color)", [{"10": "dropdown_box_shadow_color"}]]}]]}, {"14": ["transition-duration", {"24": [{"9": ["get", "(dropdown_duration)", [{"10": "dropdown_duration"}]]}, {"9": ["get", "(dropdown_duration)", [{"10": "dropdown_duration"}]]}, {"22": ["0", "s"]}]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(dropdown_timing_function)", [{"10": "dropdown_timing_function"}]]}, false]}, {"18": ["bg", []]}]]}, {"23": [[{"15": ["%s:not(.x-active) ", [{"3": "selector"}]]}], [{"14": ["transition-delay", {"24": [{"22": ["0", "s"]}, {"22": ["0", "s"]}, {"9": ["get", "(dropdown_duration)", [{"10": "dropdown_duration"}]]}]}, false]}]]}, {"23": [[{"15": ["%s[data-x-stem-menu-top]", [{"3": "selector"}]]}, {"15": ["%s[data-x-stem-root] ", [{"3": "selector"}]]}], [{"18": ["margin", [{"9": ["get-base", "(dropdown_margin)", [{"10": "dropdown_margin"}]]}, {"9": ["get", "(dropdown_margin)", [{"10": "dropdown_margin"}]]}]]}]]}]]}, {"25": ["off-canvas", [{"3": "isLayoutElement"}], [{"14": ["font-size", {"9": ["get", "(off_canvas_base_font_size)", [{"10": "off_canvas_base_font_size"}]]}, false]}, {"14": ["transition-duration", {"9": ["get", "(off_canvas_duration)", [{"10": "off_canvas_duration"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(_offscreen)", [{"10": "_offscreen"}]]}, "!=", {"6": ""}]}, [{"14": ["z-index", {"9": ["calc", {"15": ["((99999999 + %s) * %s)", [{"9": ["get", "(_order)", [{"10": "_order"}]]}, {"9": ["get", "(_depth)", [{"10": "_depth"}]]}]]}, [{"8": [{"8": [{"12": "99999999"}, "+", {"10": {"15": ["%s", [{"9": ["get", "(_order)", [{"10": "_order"}]]}]]}}]}, "*", {"10": {"15": ["%s", [{"9": ["get", "(_depth)", [{"10": "_depth"}]]}]]}}]}]]}, false]}]]}, {"23": [[".x-off-canvas-bg"], [{"18": ["background-color", [{"10": "off_canvas_bg_color"}]]}, {"14": ["transition-duration", {"9": ["get", "(off_canvas_duration)", [{"10": "off_canvas_duration"}]]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(off_canvas_timing_function)", [{"10": "off_canvas_timing_function"}]]}, false]}]]}, {"4": [{"9": ["get", "(off_canvas_close_enabled)", [{"10": "off_canvas_close_enabled"}]]}, [{"23": [[".x-off-canvas-close"], [{"14": ["width", {"9": ["calc", {"15": ["(1em * %s)", [{"9": ["get", "(off_canvas_close_dimensions)", [{"10": "off_canvas_close_dimensions"}]]}]]}, [{"8": [{"22": ["1", "em"]}, "*", {"10": {"15": ["%s", [{"9": ["get", "(off_canvas_close_dimensions)", [{"10": "off_canvas_close_dimensions"}]]}]]}}]}]]}, false]}, {"14": ["height", {"9": ["calc", {"15": ["(1em * %s)", [{"9": ["get", "(off_canvas_close_dimensions)", [{"10": "off_canvas_close_dimensions"}]]}]]}, [{"8": [{"22": ["1", "em"]}, "*", {"10": {"15": ["%s", [{"9": ["get", "(off_canvas_close_dimensions)", [{"10": "off_canvas_close_dimensions"}]]}]]}}]}]]}, false]}, {"14": ["font-size", {"9": ["get", "(off_canvas_close_font_size)", [{"10": "off_canvas_close_font_size"}]]}, false]}, {"18": ["text-color", [{"9": ["get", "(off_canvas_close_color)", [{"10": "off_canvas_close_color"}]]}, {"9": ["get", "(off_canvas_close_color_alt)", [{"10": "off_canvas_close_color_alt"}]]}]]}, {"14": ["transition-duration", {"24": [{"22": ["0.3", "s"]}, {"9": ["get", "(off_canvas_duration)", [{"10": "off_canvas_duration"}]]}, {"9": ["get", "(off_canvas_duration)", [{"10": "off_canvas_duration"}]]}]}, false]}, {"14": ["transition-timing-function", {"24": [{"10": "ease-in-out"}, {"9": ["get", "(off_canvas_timing_function)", [{"10": "off_canvas_timing_function"}]]}, {"9": ["get", "(off_canvas_timing_function)", [{"10": "off_canvas_timing_function"}]]}]}, false]}]]}, {"23": [[".x-off-canvas-close:hover", ".x-off-canvas-close:focus"], [{"18": ["text-color-alt", [{"9": ["get", "(off_canvas_close_color)", [{"10": "off_canvas_close_color"}]]}, {"9": ["get", "(off_canvas_close_color_alt)", [{"10": "off_canvas_close_color_alt"}]]}]]}]]}]]}, {"23": [[".x-off-canvas-content"], [{"4": [{"3": "isLayoutElement"}, [{"4": [{"9": ["changed", "('visible',get(off_canvas_content_overflow),get-base(off_canvas_content_overflow))", [{"6": "visible"}, {"9": ["get", "(off_canvas_content_overflow)", [{"10": "off_canvas_content_overflow"}]]}, {"9": ["get-base", "(off_canvas_content_overflow)", [{"10": "off_canvas_content_overflow"}]]}]]}, [{"14": ["overflow-x", {"9": ["get", "(off_canvas_content_overflow)", [{"10": "off_canvas_content_overflow"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(off_canvas_content_overflow)", [{"10": "off_canvas_content_overflow"}]]}, "!=", {"6": "visible"}]}, [{"14": ["overflow-y", {"9": ["get", "(off_canvas_content_overflow)", [{"10": "off_canvas_content_overflow"}]]}, false]}]]}]]}, {"18": ["flexbox", [{"6": "off_canvas_content"}]]}, {"18": ["changedmixin", [{"6": "auto"}, {"10": "off_canvas_content_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(off_canvas_content_min_width), get-base(off_canvas_content_min_width))", [{"9": ["get", "(off_canvas_content_min_width)", [{"10": "off_canvas_content_min_width"}]]}, {"9": ["get-base", "(off_canvas_content_min_width)", [{"10": "off_canvas_content_min_width"}]]}]]}, [{"14": ["min-width", {"9": ["get", "(off_canvas_content_min_width)", [{"10": "off_canvas_content_min_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(off_canvas_content_max_width), get-base(off_canvas_content_max_width))", [{"9": ["get", "(off_canvas_content_max_width)", [{"10": "off_canvas_content_max_width"}]]}, {"9": ["get-base", "(off_canvas_content_max_width)", [{"10": "off_canvas_content_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(off_canvas_content_max_width)", [{"10": "off_canvas_content_max_width"}]]}, false]}]]}, {"4": [{"9": ["is-set", "(get(off_canvas_content_text_align), get-base(off_canvas_content_text_align))", [{"9": ["get", "(off_canvas_content_text_align)", [{"10": "off_canvas_content_text_align"}]]}, {"9": ["get-base", "(off_canvas_content_text_align)", [{"10": "off_canvas_content_text_align"}]]}]]}, [{"14": ["text-align", {"9": ["get", "(off_canvas_content_text_align)", [{"10": "off_canvas_content_text_align"}]]}, false]}]]}], {"16": [{"4": [{"9": ["is-set", "(get(off_canvas_content_max_width), get-base(off_canvas_content_max_width))", [{"9": ["get", "(off_canvas_content_max_width)", [{"10": "off_canvas_content_max_width"}]]}, {"9": ["get-base", "(off_canvas_content_max_width)", [{"10": "off_canvas_content_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(off_canvas_content_max_width)", [{"10": "off_canvas_content_max_width"}]]}, false]}]]}]}]}, {"4": [{"8": [{"9": ["get", "(off_canvas_close_offset)", [{"10": "off_canvas_close_offset"}]]}, "and", {"9": ["get", "(off_canvas_close_enabled)", [{"10": "off_canvas_close_enabled"}]]}]}, [{"14": ["padding", {"9": ["calc", {"15": ["(%s * %s)", [{"9": ["get", "(off_canvas_close_font_size)", [{"10": "off_canvas_close_font_size"}]]}, {"9": ["get", "(off_canvas_close_dimensions)", [{"10": "off_canvas_close_dimensions"}]]}]]}, [{"8": [{"10": {"15": ["%s", [{"9": ["get", "(off_canvas_close_font_size)", [{"10": "off_canvas_close_font_size"}]]}]]}}, "*", {"10": {"15": ["%s", [{"9": ["get", "(off_canvas_close_dimensions)", [{"10": "off_canvas_close_dimensions"}]]}]]}}]}]]}, false]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(off_canvas_content_border_width)", [{"10": "off_canvas_content_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(off_canvas_content_border_style)", [{"10": "off_canvas_content_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(off_canvas_content_border_color)", [{"10": "off_canvas_content_border_color"}]]}]}]]}, {"18": ["background-color", [{"10": "off_canvas_content_bg_color"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(off_canvas_content_box_shadow_dimensions)", [{"10": "off_canvas_content_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(off_canvas_content_box_shadow_color)", [{"10": "off_canvas_content_box_shadow_color"}]]}]}]]}, {"14": ["transition-duration", {"9": ["get", "(off_canvas_duration)", [{"10": "off_canvas_duration"}]]}, false]}, {"14": ["transition-timing-function", {"9": ["get", "(off_canvas_timing_function)", [{"10": "off_canvas_timing_function"}]]}, false]}, {"18": ["bg", []]}]]}]]}, {"25": ["search", [], [{"14": ["width", {"9": ["get", "(search_width)", [{"10": "search_width"}]]}, false]}, {"14": ["max-width", {"9": ["get", "(search_max_width)", [{"10": "search_max_width"}]]}, false]}, {"14": ["height", {"9": ["get", "(search_height)", [{"10": "search_height"}]]}, false]}, {"18": ["margin", [{"9": ["get-base", "(search_margin)", [{"10": "search_margin"}]]}, {"9": ["get", "(search_margin)", [{"10": "search_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(search_border_width)", [{"10": "search_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(search_border_style)", [{"10": "search_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(search_border_color)", [{"10": "search_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_border_color_alt)", [{"10": "search_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(search_border_radius)", [{"10": "search_border_radius"}]]}, {"9": ["get", "(search_border_radius)", [{"10": "search_border_radius"}]]}]]}, {"14": ["font-size", {"9": ["get", "(search_base_font_size)", [{"10": "search_base_font_size"}]]}, false]}, {"18": ["background-color", [{"10": "search_bg_color"}, {"10": "search_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(search_box_shadow_dimensions)", [{"10": "search_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(search_box_shadow_color)", [{"10": "search_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_box_shadow_color_alt)", [{"10": "search_box_shadow_color_alt"}]]}]}]]}, {"23": [["&.x-search-focused"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(search_border_width)", [{"10": "search_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(search_border_style)", [{"10": "search_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(search_border_color)", [{"10": "search_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_border_color_alt)", [{"10": "search_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(search_border_radius)", [{"10": "search_border_radius"}]]}, {"9": ["get", "(search_border_radius)", [{"10": "search_border_radius"}]]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(search_bg_color)", [{"10": "search_bg_color"}]]}, {"9": ["get", "(search_bg_color_alt)", [{"10": "search_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(search_box_shadow_dimensions)", [{"10": "search_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(search_box_shadow_color)", [{"10": "search_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_box_shadow_color_alt)", [{"10": "search_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [[".x-search-input"], [{"14": ["order", {"9": ["get", "(search_order_input)", [{"10": "search_order_input"}]]}, false]}, {"18": ["margin", [{"9": ["get-base", "(search_input_margin)", [{"10": "search_input_margin"}]]}, {"9": ["get", "(search_input_margin)", [{"10": "search_input_margin"}]]}, {"22": ["0", "px"]}]]}, {"18": ["linotype", [{"11": ["ff", {"9": ["get", "(search_input_font_family)", [{"10": "search_input_font_family"}]]}]}, {"11": ["fsize", {"9": ["get", "(search_input_font_size)", [{"10": "search_input_font_size"}]]}]}, {"11": ["fstyle", {"9": ["get", "(search_input_font_style)", [{"10": "search_input_font_style"}]]}]}, {"11": ["fw", {"9": ["get", "(search_input_font_weight)", [{"10": "search_input_font_weight"}]]}]}, {"11": ["lh", {"9": ["get", "(search_input_line_height)", [{"10": "search_input_line_height"}]]}]}, {"11": ["ls", {"9": ["get", "(search_input_letter_spacing)", [{"10": "search_input_letter_spacing"}]]}]}, {"11": ["ta", {"9": ["get", "(search_input_text_align)", [{"10": "search_input_text_align"}]]}]}, {"11": ["td", {"9": ["get", "(search_input_text_decoration)", [{"10": "search_input_text_decoration"}]]}]}, {"11": ["tt", {"9": ["get", "(search_input_text_transform)", [{"10": "search_input_text_transform"}]]}]}]]}, {"18": ["text-color", [{"9": ["get", "(search_input_text_color)", [{"10": "search_input_text_color"}]]}, {"9": ["get", "(search_input_text_color_alt)", [{"10": "search_input_text_color_alt"}]]}]]}]]}, {"23": [["&.x-search-has-content .x-search-input"], [{"18": ["text-color-alt", [{"9": ["get", "(search_input_text_color)", [{"10": "search_input_text_color"}]]}, {"9": ["get", "(search_input_text_color_alt)", [{"10": "search_input_text_color_alt"}]]}]]}]]}, {"23": [[".x-search-btn-submit"], [{"14": ["order", {"9": ["get", "(search_order_submit)", [{"10": "search_order_submit"}]]}, false]}, {"14": ["width", {"9": ["get", "(search_submit_width)", [{"10": "search_submit_width"}]]}, false]}, {"14": ["height", {"9": ["get", "(search_submit_height)", [{"10": "search_submit_height"}]]}, false]}, {"18": ["margin", [{"9": ["get-base", "(search_submit_margin)", [{"10": "search_submit_margin"}]]}, {"9": ["get", "(search_submit_margin)", [{"10": "search_submit_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(search_submit_border_width)", [{"10": "search_submit_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(search_submit_border_style)", [{"10": "search_submit_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(search_submit_border_color)", [{"10": "search_submit_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_submit_border_color_alt)", [{"10": "search_submit_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(search_submit_border_radius)", [{"10": "search_submit_border_radius"}]]}, {"9": ["get", "(search_submit_border_radius)", [{"10": "search_submit_border_radius"}]]}]]}, {"14": ["font-size", {"9": ["get", "(search_submit_font_size)", [{"10": "search_submit_font_size"}]]}, false]}, {"18": ["text-color", [{"9": ["get", "(search_submit_color)", [{"10": "search_submit_color"}]]}, {"9": ["get", "(search_submit_color_alt)", [{"10": "search_submit_color_alt"}]]}]]}, {"18": ["background-color", [{"10": "search_submit_bg_color"}, {"10": "search_submit_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(search_submit_box_shadow_dimensions)", [{"10": "search_submit_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(search_submit_box_shadow_color)", [{"10": "search_submit_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_submit_box_shadow_color_alt)", [{"10": "search_submit_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [[".x-search-btn-submit:hover", ".x-search-btn-submit:focus"], [{"18": ["text-color-alt", [{"9": ["get", "(search_submit_color)", [{"10": "search_submit_color"}]]}, {"9": ["get", "(search_submit_color_alt)", [{"10": "search_submit_color_alt"}]]}]]}, {"18": ["border-alt", [{"11": ["width", {"9": ["get", "(search_submit_border_width)", [{"10": "search_submit_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(search_submit_border_style)", [{"10": "search_submit_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(search_submit_border_color)", [{"10": "search_submit_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_submit_border_color_alt)", [{"10": "search_submit_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(search_submit_border_radius)", [{"10": "search_submit_border_radius"}]]}, {"9": ["get", "(search_submit_border_radius)", [{"10": "search_submit_border_radius"}]]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(search_submit_bg_color)", [{"10": "search_submit_bg_color"}]]}, {"9": ["get", "(search_submit_bg_color_alt)", [{"10": "search_submit_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(search_submit_box_shadow_dimensions)", [{"10": "search_submit_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(search_submit_box_shadow_color)", [{"10": "search_submit_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_submit_box_shadow_color_alt)", [{"10": "search_submit_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [[".x-search-btn-clear"], [{"14": ["order", {"9": ["get", "(search_order_clear)", [{"10": "search_order_clear"}]]}, false]}, {"14": ["width", {"9": ["get", "(search_clear_width)", [{"10": "search_clear_width"}]]}, false]}, {"14": ["height", {"9": ["get", "(search_clear_height)", [{"10": "search_clear_height"}]]}, false]}, {"18": ["margin", [{"9": ["get-base", "(search_clear_margin)", [{"10": "search_clear_margin"}]]}, {"9": ["get", "(search_clear_margin)", [{"10": "search_clear_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(search_clear_border_width)", [{"10": "search_clear_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(search_clear_border_style)", [{"10": "search_clear_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(search_clear_border_color)", [{"10": "search_clear_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_clear_border_color_alt)", [{"10": "search_clear_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(search_clear_border_radius)", [{"10": "search_clear_border_radius"}]]}, {"9": ["get", "(search_clear_border_radius)", [{"10": "search_clear_border_radius"}]]}]]}, {"18": ["text-color", [{"9": ["get", "(search_clear_color)", [{"10": "search_clear_color"}]]}, {"9": ["get", "(search_clear_color_alt)", [{"10": "search_clear_color_alt"}]]}]]}, {"14": ["font-size", {"9": ["get", "(search_clear_font_size)", [{"10": "search_clear_font_size"}]]}, false]}, {"18": ["background-color", [{"10": "search_clear_bg_color"}, {"10": "search_clear_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(search_clear_box_shadow_dimensions)", [{"10": "search_clear_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(search_clear_box_shadow_color)", [{"10": "search_clear_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_clear_box_shadow_color_alt)", [{"10": "search_clear_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [[".x-search-btn-clear:hover", ".x-search-btn-clear:focus"], [{"18": ["text-color-alt", [{"9": ["get", "(search_clear_color)", [{"10": "search_clear_color"}]]}, {"9": ["get", "(search_clear_color_alt)", [{"10": "search_clear_color_alt"}]]}]]}, {"18": ["border-alt", [{"11": ["width", {"9": ["get", "(search_clear_border_width)", [{"10": "search_clear_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(search_clear_border_style)", [{"10": "search_clear_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(search_clear_border_color)", [{"10": "search_clear_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_clear_border_color_alt)", [{"10": "search_clear_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(search_clear_border_radius)", [{"10": "search_clear_border_radius"}]]}, {"9": ["get", "(search_clear_border_radius)", [{"10": "search_clear_border_radius"}]]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(search_clear_bg_color)", [{"10": "search_clear_bg_color"}]]}, {"9": ["get", "(search_clear_bg_color_alt)", [{"10": "search_clear_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(search_clear_box_shadow_dimensions)", [{"10": "search_clear_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(search_clear_box_shadow_color)", [{"10": "search_clear_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(search_clear_box_shadow_color_alt)", [{"10": "search_clear_box_shadow_color_alt"}]]}]}]]}]]}]]}, {"25": ["icon", [], [{"18": ["changedmixin", [{"6": "auto"}, {"10": "icon_width"}, {"6": "width"}]]}, {"18": ["margin", [{"9": ["get-base", "(icon_margin)", [{"10": "icon_margin"}]]}, {"9": ["get", "(icon_margin)", [{"10": "icon_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(icon_border_width)", [{"10": "icon_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(icon_border_style)", [{"10": "icon_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(icon_border_color)", [{"10": "icon_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(icon_border_color_alt)", [{"10": "icon_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(icon_border_radius)", [{"10": "icon_border_radius"}]]}, {"9": ["get", "(icon_border_radius)", [{"10": "icon_border_radius"}]]}]]}, {"4": [{"9": ["changed", "('auto',get(icon_height),get-base(icon_height))", [{"6": "auto"}, {"9": ["get", "(icon_height)", [{"10": "icon_height"}]]}, {"9": ["get-base", "(icon_height)", [{"10": "icon_height"}]]}]]}, [{"14": ["height", {"9": ["get", "(icon_height)", [{"10": "icon_height"}]]}, false]}, {"14": ["line-height", {"9": ["get", "(icon_height)", [{"10": "icon_height"}]]}, false]}]]}, {"14": ["font-size", {"9": ["get", "(icon_font_size)", [{"10": "icon_font_size"}]]}, false]}, {"4": [{"8": [{"9": ["get", "(icon_type)", [{"10": "icon_type"}]]}, "==", {"19": "svg"}]}, [{"23": [["> svg"], [{"4": [{"9": ["is-gradient", "(get(icon_color))", [{"9": ["get", "(icon_color)", [{"10": "icon_color"}]]}]]}, [], {"16": [{"14": ["fill", {"9": ["global-color", "(get(icon_color))", [{"9": ["get", "(icon_color)", [{"10": "icon_color"}]]}]]}, false]}]}]}, {"18": ["icon-dropshadow", [{"10": "icon_text_shadow_dimensions"}, {"10": "icon_text_shadow_color"}]]}]]}], {"16": [{"18": ["text-color", [{"9": ["get", "(icon_color)", [{"10": "icon_color"}]]}, {"9": ["get", "(icon_color_alt)", [{"10": "icon_color_alt"}]]}]]}, {"18": ["text-shadow", [{"11": ["dimensions", {"9": ["get", "(icon_text_shadow_dimensions)", [{"10": "icon_text_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(icon_text_shadow_color)", [{"10": "icon_text_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(icon_text_shadow_color_alt)", [{"10": "icon_text_shadow_color_alt"}]]}]}]]}]}]}, {"18": ["background-color", [{"10": "icon_bg_color"}, {"10": "icon_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(icon_box_shadow_dimensions)", [{"10": "icon_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(icon_box_shadow_color)", [{"10": "icon_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(icon_box_shadow_color_alt)", [{"10": "icon_box_shadow_color_alt"}]]}]}]]}, {"23": [["&:hover", "&[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &"], [{"4": [{"8": [{"9": ["get", "(icon_type)", [{"10": "icon_type"}]]}, "==", {"19": "svg"}]}, [{"23": [["> svg"], [{"14": ["fill", {"9": ["global-color", "(get(icon_color_alt))", [{"9": ["get", "(icon_color_alt)", [{"10": "icon_color_alt"}]]}]]}, false]}, {"18": ["icon-dropshadow", [{"10": "icon_text_shadow_dimensions"}, {"10": "icon_text_shadow_color_alt"}]]}]]}], {"16": [{"18": ["text-color-alt", [{"9": ["get", "(icon_color)", [{"10": "icon_color"}]]}, {"9": ["get", "(icon_color_alt)", [{"10": "icon_color_alt"}]]}]]}, {"18": ["text-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(icon_text_shadow_dimensions)", [{"10": "icon_text_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(icon_text_shadow_color)", [{"10": "icon_text_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(icon_text_shadow_color_alt)", [{"10": "icon_text_shadow_color_alt"}]]}]}]]}]}]}, {"18": ["border-alt", [{"11": ["width", {"9": ["get", "(icon_border_width)", [{"10": "icon_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(icon_border_style)", [{"10": "icon_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(icon_border_color)", [{"10": "icon_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(icon_border_color_alt)", [{"10": "icon_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(icon_bg_color)", [{"10": "icon_bg_color"}]]}, {"9": ["get", "(icon_bg_color_alt)", [{"10": "icon_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(icon_box_shadow_dimensions)", [{"10": "icon_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(icon_box_shadow_color)", [{"10": "icon_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(icon_box_shadow_color_alt)", [{"10": "icon_box_shadow_color_alt"}]]}]}]]}]]}]]}, {"25": ["image", [], [{"23": [["&.x-image"], [{"18": ["changedmixin", [{"6": "inline-block"}, {"10": "image_display"}, {"6": "display"}]]}, {"14": ["font-size", {"9": ["get", "(image_font_size)", [{"10": "image_font_size"}]]}, false]}]]}, {"4": [{"8": [{"9": ["get", "(image_type)", [{"10": "image_type"}]]}, "!=", {"6": "scaling"}]}, [{"23": [["&.x-image"], [{"18": ["changedmixin", [{"6": "auto"}, {"10": "image_styled_width"}, {"6": "width"}]]}, {"4": [{"9": ["is-set", "(get(image_styled_max_width), get-base(image_styled_max_width))", [{"9": ["get", "(image_styled_max_width)", [{"10": "image_styled_max_width"}]]}, {"9": ["get-base", "(image_styled_max_width)", [{"10": "image_styled_max_width"}]]}]]}, [{"14": ["max-width", {"9": ["get", "(image_styled_max_width)", [{"10": "image_styled_max_width"}]]}, false]}]]}, {"18": ["margin", [{"9": ["get-base", "(image_margin)", [{"10": "image_margin"}]]}, {"9": ["get", "(image_margin)", [{"10": "image_margin"}]]}]]}, {"18": ["border", [{"11": ["width", {"9": ["get", "(image_border_width)", [{"10": "image_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(image_border_style)", [{"10": "image_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(image_border_color)", [{"10": "image_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(image_border_color_alt)", [{"10": "image_border_color_alt"}]]}]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(image_outer_border_radius)", [{"10": "image_outer_border_radius"}]]}, {"9": ["get", "(image_outer_border_radius)", [{"10": "image_outer_border_radius"}]]}]]}, {"18": ["padding", [{"9": ["get-base", "(image_padding)", [{"10": "image_padding"}]]}, {"9": ["get", "(image_padding)", [{"10": "image_padding"}]]}]]}, {"18": ["background-color", [{"10": "image_bg_color"}, {"10": "image_bg_color_alt"}]]}, {"18": ["box-shadow", [{"11": ["dimensions", {"9": ["get", "(image_box_shadow_dimensions)", [{"10": "image_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(image_box_shadow_color)", [{"10": "image_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(image_box_shadow_color_alt)", [{"10": "image_box_shadow_color_alt"}]]}]}]]}]]}, {"23": [["&.x-image img"], [{"4": [{"9": ["changed", "('auto',get(image_styled_width),get-base(image_styled_width))", [{"6": "auto"}, {"9": ["get", "(image_styled_width)", [{"10": "image_styled_width"}]]}, {"9": ["get-base", "(image_styled_width)", [{"10": "image_styled_width"}]]}]]}, [{"14": ["width", {"22": ["100", "%"]}, false]}]]}, {"18": ["border-radius", [{"9": ["get-base", "(image_inner_border_radius)", [{"10": "image_inner_border_radius"}]]}, {"9": ["get", "(image_inner_border_radius)", [{"10": "image_inner_border_radius"}]]}]]}, {"18": ["changedmixin", [{"6": "fill"}, {"10": "image_object_fit"}, {"6": "object-fit"}]]}, {"18": ["changedmixin", [{"6": "50% 50%"}, {"10": "image_object_position"}, {"6": "object-position"}]]}, {"18": ["aspect-ratio", [{"6": "image_"}]]}]]}, {"23": [["&.x-image", "&.x-image img"], [{"18": ["changedmixin", [{"6": "auto"}, {"10": "image_styled_height"}, {"6": "height"}]]}, {"4": [{"9": ["is-set", "(get(image_styled_max_height), get-base(image_styled_max_height))", [{"9": ["get", "(image_styled_max_height)", [{"10": "image_styled_max_height"}]]}, {"9": ["get-base", "(image_styled_max_height)", [{"10": "image_styled_max_height"}]]}]]}, [{"14": ["max-height", {"9": ["get", "(image_styled_max_height)", [{"10": "image_styled_max_height"}]]}, false]}]]}]]}]]}, {"23": [["&.x-image:hover", "&.x-image[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-image"], [{"18": ["border-alt", [{"11": ["width", {"9": ["get", "(image_border_width)", [{"10": "image_border_width"}]]}]}, {"11": ["style", {"9": ["get", "(image_border_style)", [{"10": "image_border_style"}]]}]}, {"11": ["base", {"9": ["get", "(image_border_color)", [{"10": "image_border_color"}]]}]}, {"11": ["alt", {"9": ["get", "(image_border_color_alt)", [{"10": "image_border_color_alt"}]]}]}]]}, {"18": ["background-color-alt", [{"9": ["get", "(image_bg_color)", [{"10": "image_bg_color"}]]}, {"9": ["get", "(image_bg_color_alt)", [{"10": "image_bg_color_alt"}]]}]]}, {"18": ["box-shadow-alt", [{"11": ["dimensions", {"9": ["get", "(image_box_shadow_dimensions)", [{"10": "image_box_shadow_dimensions"}]]}]}, {"11": ["base", {"9": ["get", "(image_box_shadow_color)", [{"10": "image_box_shadow_color"}]]}]}, {"11": ["alt", {"9": ["get", "(image_box_shadow_color_alt)", [{"10": "image_box_shadow_color_alt"}]]}]}]]}]]}]]}]}, {"document": 1, "function": 2, "variable": 3, "if": 4, "return": 5, "singleQuotedString": 6, "unary": 7, "operation": 8, "call": 9, "primitive": 10, "keywordArgument": 11, "number": 12, "mixin": 13, "assignProperty": 14, "interpolated": 15, "else": 16, "elseIf": 17, "include": 18, "doubleQuotedString": 19, "assignVariable": 20, "list": 21, "dimension": 22, "styleRule": 23, "list-comma": 24, "module": 25, "each": 26}]