(()=>{var{attach:k,util:v}=window.csGlobal.rivet;var w=new WeakMap,b=i=>{if(!w.has(i)){let g={markers:new Set,active:!1};w.set(i,g)}return w.get(i)};window.csGoogleMapsLoad=async function(){let{AdvancedMarkerElement:i,PinElement:g}=await window.google.maps.importLibrary("marker");k("[data-x-element-map_google_marker]",(t,e)=>{let o=b(t.closest("[data-x-element-map-google]"));f(o,e)},10);function f(t,{lat:e,lng:o,imageSrc:l,imageWidth:a,imageOffsetX:n,imageHeight:s,imageOffsetY:c,imageRetina:h,content:m,contentStart:y}){let{markers:C}=t;C.add(d=>{let u={map:d,position:new window.google.maps.LatLng(parseFloat(e),parseFloat(o))};if(l){let p={glyph:new URL(l)},I=Math.abs(a/(100/parseFloat(n))-a/2),L=Math.abs(s/(100/parseFloat(c))-s/2),r=document.createElement("img");r.src=l,r.width=a,r.height=s,h&&(r.width=a/2,r.height=s/2),u.content=r}let M=new i(u);if(m!==""){let p=new window.google.maps.InfoWindow({content:m,maxWidth:200});y==="open"&&p.open(d,M),window.google.maps.event.addListener(M,"click",function(){p.open(d,this)})}})}k("[data-x-element-map-google]",(t,e={})=>{if(!window.google||!window.google.maps)return;let o=b(t),l=t.getAttribute("data-x-map-markers");if(l&&JSON.parse(l).forEach(function(c){!c||typeof c!="object"||f(o,c)}),o.active)return;o.active=!0;let a=new window.google.maps.LatLng(e.lat,e.lng),n=new window.google.maps.Map(t,{mapTypeId:"roadmap",center:a,draggable:e.drag,zoomControl:e.zoom,zoom:parseInt(e.zoomLevel,10),clickableIcons:!1,disableDefaultUI:!0,disableDoubleClickZoom:!1,fullscreenControl:!1,mapTypeControl:!1,rotateControl:!1,scrollwheel:!1,streetViewControl:!1,backgroundColor:"transparent",mapId:e.mapId||"DEMO_MAP_ID"});n.mapTypes.set("map_google",new window.google.maps.StyledMapType(e.styles===""?null:JSON.parse(e.styles),{name:"Styled Map"})),n.setMapTypeId("map_google"),o.markers.forEach(s=>void s(n)),e.drag||n.addListener("center_changed",function(){n.panTo(a)})},100),window.csGoogleMapsClassic&&window.csGoogleMapsClassic()};})();
