(()=>{var{attach:b,util:u}=window.csGlobal.rivet,i=window.csGlobal.csHooks,{getTransitionTimingMS:R,oncePassive:_,onLoad:x,listenerPassive:y}=u,g,h,A=(h=(g=window.csJsData)==null?void 0:g.bp)==null?void 0:h.ranges[1];x(function(){b("[data-x-accordion-scroll-follow]",function(o,a){let s=o.querySelectorAll(".x-acc-header"),d=o.querySelectorAll("[data-x-toggle-collapse]"),r=[];for(var n=0;n<s.length;++n){let e=N(s[n],d[n],{mode:a});r.push(e)}return r})});function N(o,a,s={}){let r=(s.mode||"all")==="mobile";function n(e,c=0,p="smooth"){requestAnimationFrame(function(){let{top:t}=e.getBoundingClientRect(),l=i.apply("fixed_top_offset",0);window.scrollTo({top:window.pageYOffset+t-l-c,behavior:i.apply("accordion_scroll_behavior",p)})})}return y(o,"click",function(){let e=!o.classList.contains("x-active"),c=i.apply("accordion_scroll_padding",20);if(!e||r&&window.innerWidth>k())return;let t=u.toggle.getActiveInGroup(o)[0],l=!0;if(t){let f=[...t.parentNode.parentNode.children];l=f.indexOf(o.parentNode)<f.indexOf(t.parentNode)}if(l){setTimeout(function(){n(o,c)},25),_(a,"transitionend",function(){n(o,c)});return}let w=t.parentNode.children[t.parentNode.children.length-1].getBoundingClientRect(),{top:m}=o.getBoundingClientRect(),T=i.apply("fixed_top_offset",0),v=window.pageYOffset+(m-w.height-T-c);window.scrollTo({top:v,behavior:"smooth"})})}function k(){return i.apply("accordion_mobile_width",A)}})();
