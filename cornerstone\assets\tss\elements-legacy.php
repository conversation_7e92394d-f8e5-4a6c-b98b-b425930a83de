[{"1": [{"2": ["classic-column-v2", [], [{"3": [["&.x-column"], [{"4": ["border", [{"5": ["width", {"6": ["get", "(column_border_width)", [{"7": "column_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(column_border_style)", [{"7": "column_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(column_border_color)", [{"7": "column_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(column_border_radius)", [{"7": "column_border_radius"}]]}, {"6": ["get", "(column_border_radius)", [{"7": "column_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(column_padding)", [{"7": "column_padding"}]]}, {"6": ["get", "(column_padding)", [{"7": "column_padding"}]]}]]}, {"4": ["changedmixin", [{"8": "1em"}, {"7": "column_base_font_size"}, {"8": "font-size"}]]}, {"9": [{"6": ["is-set", "(get(column_text_align), get-base(column_text_align))", [{"6": ["get", "(column_text_align)", [{"7": "column_text_align"}]]}, {"6": ["get-base", "(column_text_align)", [{"7": "column_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(column_text_align)", [{"7": "column_text_align"}]]}, false]}]]}, {"4": ["background-color", [{"7": "column_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(column_box_shadow_dimensions)", [{"7": "column_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(column_box_shadow_color)", [{"7": "column_box_shadow_color"}]]}]}, {"5": ["fallback", {"7": true}]}]]}, {"10": ["z-index", {"6": ["get", "(column_z_index)", [{"7": "column_z_index"}]]}, false]}]]}]]}, {"2": ["classic-row-v2", [], [{"3": [["&.x-container"], [{"9": [{"11": ["not", {"6": ["get", "(row_inner_container)", [{"7": "row_inner_container"}]]}]}, [{"4": ["changedmixin", [{"8": "auto"}, {"7": "row_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(row_max_width), get-base(row_max_width))", [{"6": ["get", "(row_max_width)", [{"7": "row_max_width"}]]}, {"6": ["get-base", "(row_max_width)", [{"7": "row_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(row_max_width)", [{"7": "row_max_width"}]]}, false]}]]}]]}, {"4": ["margin", [{"6": ["get-base", "(row_margin)", [{"7": "row_margin"}]]}, {"6": ["get", "(row_margin)", [{"7": "row_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(row_border_width)", [{"7": "row_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(row_border_style)", [{"7": "row_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(row_border_color)", [{"7": "row_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(row_border_radius)", [{"7": "row_border_radius"}]]}, {"6": ["get", "(row_border_radius)", [{"7": "row_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(row_padding)", [{"7": "row_padding"}]]}, {"6": ["get", "(row_padding)", [{"7": "row_padding"}]]}]]}, {"4": ["changedmixin", [{"8": "1em"}, {"7": "row_base_font_size"}, {"8": "font-size"}]]}, {"9": [{"6": ["is-set", "(get(row_text_align), get-base(row_text_align))", [{"6": ["get", "(row_text_align)", [{"7": "row_text_align"}]]}, {"6": ["get-base", "(row_text_align)", [{"7": "row_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(row_text_align)", [{"7": "row_text_align"}]]}, false]}]]}, {"4": ["background-color", [{"7": "row_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(row_box_shadow_dimensions)", [{"7": "row_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(row_box_shadow_color)", [{"7": "row_box_shadow_color"}]]}]}, {"5": ["fallback", {"7": true}]}]]}, {"10": ["z-index", {"6": ["get", "(row_z_index)", [{"7": "row_z_index"}]]}, false]}, {"4": ["bg", []]}]]}]]}, {"2": ["accordion", [], [{"3": [["&.x-acc"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "accordion_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(accordion_max_width), get-base(accordion_max_width))", [{"6": ["get", "(accordion_max_width)", [{"7": "accordion_max_width"}]]}, {"6": ["get-base", "(accordion_max_width)", [{"7": "accordion_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(accordion_max_width)", [{"7": "accordion_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(accordion_margin)", [{"7": "accordion_margin"}]]}, {"6": ["get", "(accordion_margin)", [{"7": "accordion_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(accordion_border_width)", [{"7": "accordion_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(accordion_border_style)", [{"7": "accordion_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_border_color)", [{"7": "accordion_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(accordion_border_radius)", [{"7": "accordion_border_radius"}]]}, {"6": ["get", "(accordion_border_radius)", [{"7": "accordion_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(accordion_padding)", [{"7": "accordion_padding"}]]}, {"6": ["get", "(accordion_padding)", [{"7": "accordion_padding"}]]}]]}, {"10": ["font-size", {"6": ["get", "(accordion_base_font_size)", [{"7": "accordion_base_font_size"}]]}, false]}, {"4": ["background-color", [{"7": "accordion_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(accordion_box_shadow_dimensions)", [{"7": "accordion_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_box_shadow_color)", [{"7": "accordion_box_shadow_color"}]]}]}]]}]]}, {"3": [["&.x-acc > .x-acc-item"], [{"9": [{"6": ["get", "(accordion_item_overflow)", [{"7": "accordion_item_overflow"}]]}, [{"10": ["overflow", {"7": "hidden"}, false]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(accordion_item_border_width)", [{"7": "accordion_item_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(accordion_item_border_style)", [{"7": "accordion_item_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_item_border_color)", [{"7": "accordion_item_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(accordion_item_border_radius)", [{"7": "accordion_item_border_radius"}]]}, {"6": ["get", "(accordion_item_border_radius)", [{"7": "accordion_item_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(accordion_item_padding)", [{"7": "accordion_item_padding"}]]}, {"6": ["get", "(accordion_item_padding)", [{"7": "accordion_item_padding"}]]}]]}, {"4": ["background-color", [{"7": "accordion_item_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(accordion_item_box_shadow_dimensions)", [{"7": "accordion_item_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_item_box_shadow_color)", [{"7": "accordion_item_box_shadow_color"}]]}]}]]}]]}, {"3": [["&.x-acc > .x-acc-item + .x-acc-item"], [{"10": ["margin-top", {"6": ["get", "(accordion_item_spacing)", [{"7": "accordion_item_spacing"}]]}, false]}]]}, {"3": [["&.x-acc > .x-acc-item > .x-acc-header"], [{"4": ["margin", [{"6": ["get-base", "(accordion_header_margin)", [{"7": "accordion_header_margin"}]]}, {"6": ["get", "(accordion_header_margin)", [{"7": "accordion_header_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(accordion_header_border_width)", [{"7": "accordion_header_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(accordion_header_border_style)", [{"7": "accordion_header_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_header_border_color)", [{"7": "accordion_header_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_header_border_color_alt)", [{"7": "accordion_header_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(accordion_header_border_radius)", [{"7": "accordion_header_border_radius"}]]}, {"6": ["get", "(accordion_header_border_radius)", [{"7": "accordion_header_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(accordion_header_padding)", [{"7": "accordion_header_padding"}]]}, {"6": ["get", "(accordion_header_padding)", [{"7": "accordion_header_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(accordion_header_font_family))", [{"6": ["get", "(accordion_header_font_family)", [{"7": "accordion_header_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(accordion_header_font_size)", [{"7": "accordion_header_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(accordion_header_font_style)", [{"7": "accordion_header_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(accordion_header_font_family),get(accordion_header_font_weight))", [{"6": ["get", "(accordion_header_font_family)", [{"7": "accordion_header_font_family"}]]}, {"6": ["get", "(accordion_header_font_weight)", [{"7": "accordion_header_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(accordion_header_line_height)", [{"7": "accordion_header_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(accordion_header_letter_spacing), get-base(accordion_header_letter_spacing))", [{"6": ["get", "(accordion_header_letter_spacing)", [{"7": "accordion_header_letter_spacing"}]]}, {"6": ["get-base", "(accordion_header_letter_spacing)", [{"7": "accordion_header_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(accordion_header_letter_spacing)", [{"7": "accordion_header_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(accordion_header_text_align), get-base(accordion_header_text_align))", [{"6": ["get", "(accordion_header_text_align)", [{"7": "accordion_header_text_align"}]]}, {"6": ["get-base", "(accordion_header_text_align)", [{"7": "accordion_header_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(accordion_header_text_align)", [{"7": "accordion_header_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(accordion_header_text_decoration), get-base(accordion_header_text_decoration))", [{"6": ["get", "(accordion_header_text_decoration)", [{"7": "accordion_header_text_decoration"}]]}, {"6": ["get-base", "(accordion_header_text_decoration)", [{"7": "accordion_header_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(accordion_header_text_decoration)", [{"7": "accordion_header_text_decoration"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(accordion_header_text_transform), get-base(accordion_header_text_transform))", [{"6": ["get", "(accordion_header_text_transform)", [{"7": "accordion_header_text_transform"}]]}, {"6": ["get-base", "(accordion_header_text_transform)", [{"7": "accordion_header_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(accordion_header_text_transform)", [{"7": "accordion_header_text_transform"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(accordion_header_text_shadow_dimensions)", [{"7": "accordion_header_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_header_text_shadow_color)", [{"7": "accordion_header_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_header_text_shadow_color_alt)", [{"7": "accordion_header_text_shadow_color_alt"}]]}]}]]}, {"4": ["text-color", [{"6": ["get", "(accordion_header_text_color)", [{"7": "accordion_header_text_color"}]]}, {"6": ["get", "(accordion_header_text_color_alt)", [{"7": "accordion_header_text_color_alt"}]]}]]}, {"4": ["background-color", [{"7": "accordion_header_bg_color"}, {"7": "accordion_header_bg_color_alt"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(accordion_header_box_shadow_dimensions)", [{"7": "accordion_header_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_header_box_shadow_color)", [{"7": "accordion_header_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_header_box_shadow_color_alt)", [{"7": "accordion_header_box_shadow_color_alt"}]]}]}]]}]]}, {"3": [["&.x-acc > .x-acc-item > .x-acc-header:hover", "&.x-acc > .x-acc-item > .x-acc-header[class*=\"active\"]"], [{"4": ["text-color-alt", [{"6": ["get", "(accordion_header_text_color)", [{"7": "accordion_header_text_color"}]]}, {"6": ["get", "(accordion_header_text_color_alt)", [{"7": "accordion_header_text_color_alt"}]]}]]}, {"4": ["border-alt", [{"5": ["width", {"6": ["get", "(accordion_header_border_width)", [{"7": "accordion_header_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(accordion_header_border_style)", [{"7": "accordion_header_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_header_border_color)", [{"7": "accordion_header_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_header_border_color_alt)", [{"7": "accordion_header_border_color_alt"}]]}]}]]}, {"4": ["background-color-alt", [{"6": ["get", "(accordion_header_bg_color)", [{"7": "accordion_header_bg_color"}]]}, {"6": ["get", "(accordion_header_bg_color_alt)", [{"7": "accordion_header_bg_color_alt"}]]}]]}, {"4": ["text-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(accordion_header_text_shadow_dimensions)", [{"7": "accordion_header_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_header_text_shadow_color)", [{"7": "accordion_header_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_header_text_shadow_color_alt)", [{"7": "accordion_header_text_shadow_color_alt"}]]}]}]]}, {"4": ["box-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(accordion_header_box_shadow_dimensions)", [{"7": "accordion_header_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_header_box_shadow_color)", [{"7": "accordion_header_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_header_box_shadow_color_alt)", [{"7": "accordion_header_box_shadow_color_alt"}]]}]}]]}]]}, {"3": [["&.x-acc > .x-acc-item > .x-acc-header > .x-acc-header-content"], [{"9": [{"6": ["get", "(accordion_header_content_reverse)", [{"7": "accordion_header_content_reverse"}]]}, [{"10": ["flex-direction", {"7": "row-reverse"}, false]}]]}]]}, {"3": [["&.x-acc > .x-acc-item > .x-acc-header > .x-acc-header-content > .x-acc-header-indicator"], [{"9": [{"6": ["get", "(accordion_header_indicator)", [{"7": "accordion_header_indicator"}]]}, [{"10": ["width", {"6": ["get", "(accordion_header_indicator_width)", [{"7": "accordion_header_indicator_width"}]]}, false]}, {"10": ["height", {"6": ["get", "(accordion_header_indicator_height)", [{"7": "accordion_header_indicator_height"}]]}, false]}, {"10": ["font-size", {"6": ["get", "(accordion_header_indicator_font_size)", [{"7": "accordion_header_indicator_font_size"}]]}, false]}, {"4": ["text-color", [{"6": ["get", "(accordion_header_indicator_color)", [{"7": "accordion_header_indicator_color"}]]}, {"6": ["get", "(accordion_header_indicator_color_alt)", [{"7": "accordion_header_indicator_color_alt"}]]}]]}, {"10": ["transform", {"12": [{"6": ["translate3d", "(0, 0, 0)", [{"13": "0"}, {"13": "0"}, {"13": "0"}]]}, {"6": ["rotate", {"14": ["(%s)", [{"6": ["get", "(accordion_header_indicator_rotation_start)", [{"7": "accordion_header_indicator_rotation_start"}]]}]]}, [{"7": {"14": ["%s", [{"6": ["get", "(accordion_header_indicator_rotation_start)", [{"7": "accordion_header_indicator_rotation_start"}]]}]]}}]]}]}, false]}]]}]]}, {"3": [["&.x-acc > .x-acc-item > .x-acc-header:hover > .x-acc-header-content > .x-acc-header-indicator", "&.x-acc > .x-acc-item > .x-acc-header[class*=\"active\"] > .x-acc-header-content > .x-acc-header-indicator"], [{"9": [{"6": ["get", "(accordion_header_indicator)", [{"7": "accordion_header_indicator"}]]}, [{"4": ["text-color-alt", [{"6": ["get", "(accordion_header_indicator_color)", [{"7": "accordion_header_indicator_color"}]]}, {"6": ["get", "(accordion_header_indicator_color_alt)", [{"7": "accordion_header_indicator_color_alt"}]]}]]}]]}]]}, {"3": [["&.x-acc > .x-acc-item > .x-acc-header.x-active > .x-acc-header-content > .x-acc-header-indicator"], [{"9": [{"6": ["get", "(accordion_header_indicator)", [{"7": "accordion_header_indicator"}]]}, [{"10": ["transform", {"12": [{"6": ["translate3d", "(0, 0, 0)", [{"13": "0"}, {"13": "0"}, {"13": "0"}]]}, {"6": ["rotate", {"14": ["(%s)", [{"6": ["get", "(accordion_header_indicator_rotation_end)", [{"7": "accordion_header_indicator_rotation_end"}]]}]]}, [{"7": {"14": ["%s", [{"6": ["get", "(accordion_header_indicator_rotation_end)", [{"7": "accordion_header_indicator_rotation_end"}]]}]]}}]]}]}, false]}]]}]]}, {"3": [["&.x-acc > .x-acc-item > .x-acc-header > .x-acc-header-content > .x-acc-header-text"], [{"9": [{"6": ["get", "(accordion_header_indicator)", [{"7": "accordion_header_indicator"}]]}, [{"9": [{"6": ["is-set", "(get(accordion_header_content_spacing), get-base(accordion_header_content_spacing))", [{"6": ["get", "(accordion_header_content_spacing)", [{"7": "accordion_header_content_spacing"}]]}, {"6": ["get-base", "(accordion_header_content_spacing)", [{"7": "accordion_header_content_spacing"}]]}]]}, [{"9": [{"6": ["get", "(accordion_header_content_reverse)", [{"7": "accordion_header_content_reverse"}]]}, [{"10": ["margin-right", {"6": ["get", "(accordion_header_content_spacing)", [{"7": "accordion_header_content_spacing"}]]}, false]}], {"15": [{"10": ["margin-left", {"6": ["get", "(accordion_header_content_spacing)", [{"7": "accordion_header_content_spacing"}]]}, false]}]}]}]]}]]}, {"9": [{"6": ["get", "(accordion_header_text_overflow)", [{"7": "accordion_header_text_overflow"}]]}, [{"10": ["overflow", {"7": "hidden"}, false]}, {"10": ["text-overflow", {"7": "ellipsis"}, false]}, {"10": ["white-space", {"7": "nowrap"}, false]}]]}]]}, {"3": [["&.x-acc > .x-acc-item > div > .x-acc-content"], [{"4": ["margin", [{"6": ["get-base", "(accordion_content_margin)", [{"7": "accordion_content_margin"}]]}, {"6": ["get", "(accordion_content_margin)", [{"7": "accordion_content_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(accordion_content_border_width)", [{"7": "accordion_content_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(accordion_content_border_style)", [{"7": "accordion_content_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_content_border_color)", [{"7": "accordion_content_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_content_border_color_alt)", [{"7": "accordion_content_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(accordion_content_border_radius)", [{"7": "accordion_content_border_radius"}]]}, {"6": ["get", "(accordion_content_border_radius)", [{"7": "accordion_content_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(accordion_content_padding)", [{"7": "accordion_content_padding"}]]}, {"6": ["get", "(accordion_content_padding)", [{"7": "accordion_content_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(accordion_content_font_family))", [{"6": ["get", "(accordion_content_font_family)", [{"7": "accordion_content_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(accordion_content_font_size)", [{"7": "accordion_content_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(accordion_content_font_style)", [{"7": "accordion_content_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(accordion_content_font_family),get(accordion_content_font_weight))", [{"6": ["get", "(accordion_content_font_family)", [{"7": "accordion_content_font_family"}]]}, {"6": ["get", "(accordion_content_font_weight)", [{"7": "accordion_content_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(accordion_content_line_height)", [{"7": "accordion_content_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(accordion_content_letter_spacing), get-base(accordion_content_letter_spacing))", [{"6": ["get", "(accordion_content_letter_spacing)", [{"7": "accordion_content_letter_spacing"}]]}, {"6": ["get-base", "(accordion_content_letter_spacing)", [{"7": "accordion_content_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(accordion_content_letter_spacing)", [{"7": "accordion_content_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(accordion_content_text_align), get-base(accordion_content_text_align))", [{"6": ["get", "(accordion_content_text_align)", [{"7": "accordion_content_text_align"}]]}, {"6": ["get-base", "(accordion_content_text_align)", [{"7": "accordion_content_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(accordion_content_text_align)", [{"7": "accordion_content_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(accordion_content_text_decoration), get-base(accordion_content_text_decoration))", [{"6": ["get", "(accordion_content_text_decoration)", [{"7": "accordion_content_text_decoration"}]]}, {"6": ["get-base", "(accordion_content_text_decoration)", [{"7": "accordion_content_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(accordion_content_text_decoration)", [{"7": "accordion_content_text_decoration"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(accordion_content_text_transform), get-base(accordion_content_text_transform))", [{"6": ["get", "(accordion_content_text_transform)", [{"7": "accordion_content_text_transform"}]]}, {"6": ["get-base", "(accordion_content_text_transform)", [{"7": "accordion_content_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(accordion_content_text_transform)", [{"7": "accordion_content_text_transform"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(accordion_content_text_shadow_dimensions)", [{"7": "accordion_content_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_content_text_shadow_color)", [{"7": "accordion_content_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_content_text_shadow_color_alt)", [{"7": "accordion_content_text_shadow_color_alt"}]]}]}]]}, {"4": ["text-color", [{"6": ["get", "(accordion_content_text_color)", [{"7": "accordion_content_text_color"}]]}, {"6": ["get", "(accordion_content_text_color_alt)", [{"7": "accordion_content_text_color_alt"}]]}]]}, {"4": ["background-color", [{"7": "accordion_content_bg_color"}, {"7": "accordion_content_bg_color_alt"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(accordion_content_box_shadow_dimensions)", [{"7": "accordion_content_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(accordion_content_box_shadow_color)", [{"7": "accordion_content_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(accordion_content_box_shadow_color_alt)", [{"7": "accordion_content_box_shadow_color_alt"}]]}]}]]}]]}]]}, {"2": ["alert", [], [{"3": [["&.x-alert"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "alert_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(alert_max_width), get-base(alert_max_width))", [{"6": ["get", "(alert_max_width)", [{"7": "alert_max_width"}]]}, {"6": ["get-base", "(alert_max_width)", [{"7": "alert_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(alert_max_width)", [{"7": "alert_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(alert_margin)", [{"7": "alert_margin"}]]}, {"6": ["get", "(alert_margin)", [{"7": "alert_margin"}]]}, {"16": ["0", "px"]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(alert_border_width)", [{"7": "alert_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(alert_border_style)", [{"7": "alert_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(alert_border_color)", [{"7": "alert_border_color"}]]}]}, {"5": ["fallback", {"7": true}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(alert_border_radius)", [{"7": "alert_border_radius"}]]}, {"6": ["get", "(alert_border_radius)", [{"7": "alert_border_radius"}]]}, {"13": "0"}]]}, {"4": ["padding", [{"6": ["get-base", "(alert_padding)", [{"7": "alert_padding"}]]}, {"6": ["get", "(alert_padding)", [{"7": "alert_padding"}]]}, {"16": ["0", "px"]}]]}, {"4": ["linotype", [{"5": ["ff", {"6": ["get", "(alert_font_family)", [{"7": "alert_font_family"}]]}]}, {"5": ["fsize", {"6": ["get", "(alert_font_size)", [{"7": "alert_font_size"}]]}]}, {"5": ["fstyle", {"6": ["get", "(alert_font_style)", [{"7": "alert_font_style"}]]}]}, {"5": ["fw", {"6": ["get", "(alert_font_weight)", [{"7": "alert_font_weight"}]]}]}, {"5": ["lh", {"6": ["get", "(alert_line_height)", [{"7": "alert_line_height"}]]}]}, {"5": ["ls", {"6": ["get", "(alert_letter_spacing)", [{"7": "alert_letter_spacing"}]]}]}, {"5": ["ta", {"6": ["get", "(alert_text_align)", [{"7": "alert_text_align"}]]}]}, {"5": ["td", {"6": ["get", "(alert_text_decoration)", [{"7": "alert_text_decoration"}]]}]}, {"5": ["tt", {"6": ["get", "(alert_text_transform)", [{"7": "alert_text_transform"}]]}]}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(alert_box_shadow_dimensions)", [{"7": "alert_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(alert_box_shadow_color)", [{"7": "alert_box_shadow_color"}]]}]}, {"5": ["fallback", {"7": true}]}]]}, {"4": ["text-color", [{"6": ["get", "(alert_text_color)", [{"7": "alert_text_color"}]]}]]}, {"4": ["background-color", [{"7": "alert_bg_color"}]]}, {"4": ["text-shadow", [{"6": ["get", "(alert_text_shadow_dimensions)", [{"7": "alert_text_shadow_dimensions"}]]}, {"6": ["get", "(alert_text_shadow_color)", [{"7": "alert_text_shadow_color"}]]}]]}]]}, {"9": [{"6": ["get", "(alert_close)", [{"7": "alert_close"}]]}, [{"3": [[".close"], [{"10": ["position", {"7": "absolute"}, false]}, {"10": ["top", {"6": ["get", "(alert_close_offset_top)", [{"7": "alert_close_offset_top"}]]}, false]}, {"9": [{"17": [{"6": ["get", "(alert_close_location)", [{"7": "alert_close_location"}]]}, "==", {"8": "left"}]}, [{"10": ["left", {"6": ["get", "(alert_close_offset_side)", [{"7": "alert_close_offset_side"}]]}, false]}, {"10": ["right", {"7": "auto"}, false]}]]}, {"9": [{"17": [{"6": ["get", "(alert_close_location)", [{"7": "alert_close_location"}]]}, "==", {"8": "right"}]}, [{"10": ["left", {"7": "auto"}, false]}, {"10": ["right", {"6": ["get", "(alert_close_offset_side)", [{"7": "alert_close_offset_side"}]]}, false]}]]}, {"10": ["bottom", {"7": "auto"}, false]}, {"10": ["width", {"16": ["1", "em"]}, false]}, {"10": ["height", {"16": ["1", "em"]}, false]}, {"10": ["font-size", {"6": ["get", "(alert_close_font_size)", [{"7": "alert_close_font_size"}]]}, false]}, {"10": ["text-shadow", {"7": "none"}, false]}, {"4": ["text-color", [{"6": ["get", "(alert_close_color)", [{"7": "alert_close_color"}]]}]]}, {"10": ["opacity", {"13": "1"}, false]}]]}, {"3": [[".close:hover", ".close:focus"], [{"4": ["text-color", [{"6": ["get", "(alert_close_color_alt)", [{"7": "alert_close_color_alt"}]]}]]}, {"10": ["opacity", {"13": "1"}, false]}]]}]]}]]}, {"2": ["card", [], [{"10": ["font-size", {"6": ["get", "(card_base_font_size)", [{"7": "card_base_font_size"}]]}, false]}, {"9": [{"6": ["is-set", "(get(card_width), get-base(card_width))", [{"6": ["get", "(card_width)", [{"7": "card_width"}]]}, {"6": ["get-base", "(card_width)", [{"7": "card_width"}]]}]]}, [{"10": ["width", {"6": ["get", "(card_width)", [{"7": "card_width"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(card_max_width), get-base(card_max_width))", [{"6": ["get", "(card_max_width)", [{"7": "card_max_width"}]]}, {"6": ["get-base", "(card_max_width)", [{"7": "card_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(card_max_width)", [{"7": "card_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(card_margin)", [{"7": "card_margin"}]]}, {"6": ["get", "(card_margin)", [{"7": "card_margin"}]]}]]}, {"10": ["perspective", {"6": ["get", "(card_perspective)", [{"7": "card_perspective"}]]}, false]}, {"3": [[".x-card-face"], [{"10": ["justify-content", {"6": ["get", "(card_content_justify)", [{"7": "card_content_justify"}]]}, false]}, {"4": ["border-radius", [{"6": ["get-base", "(card_border_radius)", [{"7": "card_border_radius"}]]}, {"6": ["get", "(card_border_radius)", [{"7": "card_border_radius"}]]}]]}, {"10": ["transition-duration", {"6": ["get", "(effects_duration)", [{"7": "effects_duration"}]]}, false]}]]}, {"3": [[".x-card-face.is-front"], [{"4": ["border", [{"5": ["width", {"6": ["get", "(card_front_border_width)", [{"7": "card_front_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(card_front_border_style)", [{"7": "card_front_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(card_front_border_color)", [{"7": "card_front_border_color"}]]}]}]]}, {"4": ["padding", [{"6": ["get-base", "(card_front_padding)", [{"7": "card_front_padding"}]]}, {"6": ["get", "(card_front_padding)", [{"7": "card_front_padding"}]]}]]}, {"4": ["background-color", [{"7": "card_front_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(card_front_box_shadow_dimensions)", [{"7": "card_front_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(card_front_box_shadow_color)", [{"7": "card_front_box_shadow_color"}]]}]}]]}, {"4": ["bg", [{"8": "card_front_"}]]}]]}, {"3": [[".x-card-face.is-back"], [{"4": ["border", [{"5": ["width", {"6": ["get", "(card_back_border_width)", [{"7": "card_back_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(card_back_border_style)", [{"7": "card_back_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(card_back_border_color)", [{"7": "card_back_border_color"}]]}]}]]}, {"4": ["padding", [{"6": ["get-base", "(card_back_padding)", [{"7": "card_back_padding"}]]}, {"6": ["get", "(card_back_padding)", [{"7": "card_back_padding"}]]}]]}, {"4": ["background-color", [{"7": "card_back_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(card_back_box_shadow_dimensions)", [{"7": "card_back_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(card_back_box_shadow_color)", [{"7": "card_back_box_shadow_color"}]]}]}]]}, {"4": ["bg", [{"8": "card_back_"}]]}]]}]]}, {"2": ["countdown", [], [{"3": [["&.x-countdown"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "countdown_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(countdown_max_width), get-base(countdown_max_width))", [{"6": ["get", "(countdown_max_width)", [{"7": "countdown_max_width"}]]}, {"6": ["get-base", "(countdown_max_width)", [{"7": "countdown_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(countdown_max_width)", [{"7": "countdown_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(countdown_margin)", [{"7": "countdown_margin"}]]}, {"6": ["get", "(countdown_margin)", [{"7": "countdown_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(countdown_border_width)", [{"7": "countdown_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(countdown_border_style)", [{"7": "countdown_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_border_color)", [{"7": "countdown_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(countdown_border_radius)", [{"7": "countdown_border_radius"}]]}, {"6": ["get", "(countdown_border_radius)", [{"7": "countdown_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(countdown_padding)", [{"7": "countdown_padding"}]]}, {"6": ["get", "(countdown_padding)", [{"7": "countdown_padding"}]]}, {"18": "1px"}]]}, {"10": ["font-size", {"6": ["get", "(countdown_base_font_size)", [{"7": "countdown_base_font_size"}]]}, false]}, {"4": ["background-color", [{"7": "countdown_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_box_shadow_dimensions)", [{"7": "countdown_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_box_shadow_color)", [{"7": "countdown_box_shadow_color"}]]}]}]]}]]}, {"3": [[".x-countdown-units"], [{"9": [{"17": [{"6": ["empty", "(get(countdown_padding))", [{"6": ["get", "(countdown_padding)", [{"7": "countdown_padding"}]]}]]}, "and", {"6": ["off", "(get-base(countdown_padding))", [{"6": ["get-base", "(countdown_padding)", [{"7": "countdown_padding"}]]}]]}]}, [{"10": ["margin", {"12": [{"6": ["calc", {"14": ["(((%s / 2) + 1px) * -1)", [{"6": ["get", "(countdown_unit_gap_row)", [{"7": "countdown_unit_gap_row"}]]}]]}, [{"17": [{"17": [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_unit_gap_row)", [{"7": "countdown_unit_gap_row"}]]}]]}}, "/", {"13": "2"}]}, "+", {"16": ["1", "px"]}]}, "*", {"11": ["-", {"13": "1"}]}]}]]}, {"6": ["calc", {"14": ["(((%s / 2) + 1px) * -1)", [{"6": ["get", "(countdown_unit_gap_column)", [{"7": "countdown_unit_gap_column"}]]}]]}, [{"17": [{"17": [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_unit_gap_column)", [{"7": "countdown_unit_gap_column"}]]}]]}}, "/", {"13": "2"}]}, "+", {"16": ["1", "px"]}]}, "*", {"11": ["-", {"13": "1"}]}]}]]}]}, false]}], {"15": [{"10": ["margin", {"12": [{"6": ["calc", {"14": ["((%s / 2) * -1)", [{"6": ["get", "(countdown_unit_gap_row)", [{"7": "countdown_unit_gap_row"}]]}]]}, [{"17": [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_unit_gap_row)", [{"7": "countdown_unit_gap_row"}]]}]]}}, "/", {"13": "2"}]}, "*", {"11": ["-", {"13": "1"}]}]}]]}, {"6": ["calc", {"14": ["((%s / 2) * -1)", [{"6": ["get", "(countdown_unit_gap_column)", [{"7": "countdown_unit_gap_column"}]]}]]}, [{"17": [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_unit_gap_column)", [{"7": "countdown_unit_gap_column"}]]}]]}}, "/", {"13": "2"}]}, "*", {"11": ["-", {"13": "1"}]}]}]]}]}, false]}]}]}]]}, {"3": [[".x-countdown-unit-content"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "countdown_unit_width"}, {"8": "width"}]]}, {"10": ["margin", {"12": [{"6": ["calc", {"14": ["(%s / 2)", [{"6": ["get", "(countdown_unit_gap_row)", [{"7": "countdown_unit_gap_row"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_unit_gap_row)", [{"7": "countdown_unit_gap_row"}]]}]]}}, "/", {"13": "2"}]}]]}, {"6": ["calc", {"14": ["(%s / 2)", [{"6": ["get", "(countdown_unit_gap_column)", [{"7": "countdown_unit_gap_column"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_unit_gap_column)", [{"7": "countdown_unit_gap_column"}]]}]]}}, "/", {"13": "2"}]}]]}]}, false]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(countdown_unit_border_width)", [{"7": "countdown_unit_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(countdown_unit_border_style)", [{"7": "countdown_unit_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_unit_border_color)", [{"7": "countdown_unit_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(countdown_unit_border_radius)", [{"7": "countdown_unit_border_radius"}]]}, {"6": ["get", "(countdown_unit_border_radius)", [{"7": "countdown_unit_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(countdown_unit_padding)", [{"7": "countdown_unit_padding"}]]}, {"6": ["get", "(countdown_unit_padding)", [{"7": "countdown_unit_padding"}]]}]]}, {"4": ["background-color", [{"7": "countdown_unit_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_unit_box_shadow_dimensions)", [{"7": "countdown_unit_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_unit_box_shadow_color)", [{"7": "countdown_unit_box_shadow_color"}]]}]}]]}]]}, {"3": [[".x-countdown-number"], [{"4": ["margin", [{"6": ["get-base", "(countdown_number_margin)", [{"7": "countdown_number_margin"}]]}, {"6": ["get", "(countdown_number_margin)", [{"7": "countdown_number_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(countdown_number_border_width)", [{"7": "countdown_number_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(countdown_number_border_style)", [{"7": "countdown_number_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_number_border_color)", [{"7": "countdown_number_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(countdown_number_border_radius)", [{"7": "countdown_number_border_radius"}]]}, {"6": ["get", "(countdown_number_border_radius)", [{"7": "countdown_number_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(countdown_number_padding)", [{"7": "countdown_number_padding"}]]}, {"6": ["get", "(countdown_number_padding)", [{"7": "countdown_number_padding"}]]}]]}, {"4": ["background-color", [{"7": "countdown_number_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_number_box_shadow_dimensions)", [{"7": "countdown_number_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_number_box_shadow_color)", [{"7": "countdown_number_box_shadow_color"}]]}]}]]}]]}, {"3": [[".x-countdown-digit"], [{"4": ["margin", [{"6": ["get-base", "(countdown_digit_margin)", [{"7": "countdown_digit_margin"}]]}, {"6": ["get", "(countdown_digit_margin)", [{"7": "countdown_digit_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(countdown_digit_border_width)", [{"7": "countdown_digit_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(countdown_digit_border_style)", [{"7": "countdown_digit_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_digit_border_color)", [{"7": "countdown_digit_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(countdown_digit_border_radius)", [{"7": "countdown_digit_border_radius"}]]}, {"6": ["get", "(countdown_digit_border_radius)", [{"7": "countdown_digit_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(countdown_digit_padding)", [{"7": "countdown_digit_padding"}]]}, {"6": ["get", "(countdown_digit_padding)", [{"7": "countdown_digit_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(countdown_digit_font_family))", [{"6": ["get", "(countdown_digit_font_family)", [{"7": "countdown_digit_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(countdown_digit_font_size)", [{"7": "countdown_digit_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(countdown_digit_font_style)", [{"7": "countdown_digit_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(countdown_digit_font_family),get(countdown_digit_font_weight))", [{"6": ["get", "(countdown_digit_font_family)", [{"7": "countdown_digit_font_family"}]]}, {"6": ["get", "(countdown_digit_font_weight)", [{"7": "countdown_digit_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(countdown_digit_line_height)", [{"7": "countdown_digit_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(countdown_digit_text_align), get-base(countdown_digit_text_align))", [{"6": ["get", "(countdown_digit_text_align)", [{"7": "countdown_digit_text_align"}]]}, {"6": ["get-base", "(countdown_digit_text_align)", [{"7": "countdown_digit_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(countdown_digit_text_align)", [{"7": "countdown_digit_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(countdown_digit_text_decoration), get-base(countdown_digit_text_decoration))", [{"6": ["get", "(countdown_digit_text_decoration)", [{"7": "countdown_digit_text_decoration"}]]}, {"6": ["get-base", "(countdown_digit_text_decoration)", [{"7": "countdown_digit_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(countdown_digit_text_decoration)", [{"7": "countdown_digit_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_digit_text_shadow_dimensions)", [{"7": "countdown_digit_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_digit_text_shadow_color)", [{"7": "countdown_digit_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(countdown_digit_text_transform), get-base(countdown_digit_text_transform))", [{"6": ["get", "(countdown_digit_text_transform)", [{"7": "countdown_digit_text_transform"}]]}, {"6": ["get-base", "(countdown_digit_text_transform)", [{"7": "countdown_digit_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(countdown_digit_text_transform)", [{"7": "countdown_digit_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(countdown_digit_text_color)", [{"7": "countdown_digit_text_color"}]]}]]}, {"4": ["background-color", [{"7": "countdown_digit_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_digit_box_shadow_dimensions)", [{"7": "countdown_digit_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_digit_box_shadow_color)", [{"7": "countdown_digit_box_shadow_color"}]]}]}]]}]]}, {"3": [[".x-countdown-label"], [{"9": [{"6": ["is-set", "(get(countdown_label_spacing), get-base(countdown_label_spacing))", [{"6": ["get", "(countdown_label_spacing)", [{"7": "countdown_label_spacing"}]]}, {"6": ["get-base", "(countdown_label_spacing)", [{"7": "countdown_label_spacing"}]]}]]}, [{"9": [{"17": [{"6": ["get", "(countdown_labels_output)", [{"7": "countdown_labels_output"}]]}, "==", {"8": "compact"}]}, [{"10": ["margin-left", {"6": ["get", "(countdown_label_spacing)", [{"7": "countdown_label_spacing"}]]}, false]}]]}, {"9": [{"17": [{"6": ["get", "(countdown_labels_output)", [{"7": "countdown_labels_output"}]]}, "==", {"8": "top"}]}, [{"10": ["margin-bottom", {"6": ["get", "(countdown_label_spacing)", [{"7": "countdown_label_spacing"}]]}, false]}]]}, {"9": [{"17": [{"6": ["get", "(countdown_labels_output)", [{"7": "countdown_labels_output"}]]}, "==", {"8": "bottom"}]}, [{"10": ["margin-top", {"6": ["get", "(countdown_label_spacing)", [{"7": "countdown_label_spacing"}]]}, false]}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(countdown_label_font_family))", [{"6": ["get", "(countdown_label_font_family)", [{"7": "countdown_label_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(countdown_label_font_size)", [{"7": "countdown_label_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(countdown_label_font_style)", [{"7": "countdown_label_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(countdown_label_font_family),get(countdown_label_font_weight))", [{"6": ["get", "(countdown_label_font_family)", [{"7": "countdown_label_font_family"}]]}, {"6": ["get", "(countdown_label_font_weight)", [{"7": "countdown_label_font_weight"}]]}]]}, false]}, {"9": [{"6": ["is-set", "(get(countdown_label_letter_spacing), get-base(countdown_label_letter_spacing))", [{"6": ["get", "(countdown_label_letter_spacing)", [{"7": "countdown_label_letter_spacing"}]]}, {"6": ["get-base", "(countdown_label_letter_spacing)", [{"7": "countdown_label_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(countdown_label_letter_spacing)", [{"7": "countdown_label_letter_spacing"}]]}, false]}, {"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(countdown_label_letter_spacing)", [{"7": "countdown_label_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_label_letter_spacing)", [{"7": "countdown_label_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}, {"10": ["line-height", {"6": ["get", "(countdown_label_line_height)", [{"7": "countdown_label_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(countdown_label_text_align), get-base(countdown_label_text_align))", [{"6": ["get", "(countdown_label_text_align)", [{"7": "countdown_label_text_align"}]]}, {"6": ["get-base", "(countdown_label_text_align)", [{"7": "countdown_label_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(countdown_label_text_align)", [{"7": "countdown_label_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(countdown_label_text_decoration), get-base(countdown_label_text_decoration))", [{"6": ["get", "(countdown_label_text_decoration)", [{"7": "countdown_label_text_decoration"}]]}, {"6": ["get-base", "(countdown_label_text_decoration)", [{"7": "countdown_label_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(countdown_label_text_decoration)", [{"7": "countdown_label_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_label_text_shadow_dimensions)", [{"7": "countdown_label_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_label_text_shadow_color)", [{"7": "countdown_label_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(countdown_label_text_transform), get-base(countdown_label_text_transform))", [{"6": ["get", "(countdown_label_text_transform)", [{"7": "countdown_label_text_transform"}]]}, {"6": ["get-base", "(countdown_label_text_transform)", [{"7": "countdown_label_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(countdown_label_text_transform)", [{"7": "countdown_label_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(countdown_label_text_color)", [{"7": "countdown_label_text_color"}]]}]]}]]}, {"3": [[".x-countdown-complete"], [{"10": ["font-family", {"6": ["global-ff", "(get(countdown_complete_font_family))", [{"6": ["get", "(countdown_complete_font_family)", [{"7": "countdown_complete_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(countdown_complete_font_size)", [{"7": "countdown_complete_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(countdown_complete_font_style)", [{"7": "countdown_complete_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(countdown_complete_font_family),get(countdown_complete_font_weight))", [{"6": ["get", "(countdown_complete_font_family)", [{"7": "countdown_complete_font_family"}]]}, {"6": ["get", "(countdown_complete_font_weight)", [{"7": "countdown_complete_font_weight"}]]}]]}, false]}, {"9": [{"6": ["is-set", "(get(countdown_complete_letter_spacing), get-base(countdown_complete_letter_spacing))", [{"6": ["get", "(countdown_complete_letter_spacing)", [{"7": "countdown_complete_letter_spacing"}]]}, {"6": ["get-base", "(countdown_complete_letter_spacing)", [{"7": "countdown_complete_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(countdown_complete_letter_spacing)", [{"7": "countdown_complete_letter_spacing"}]]}, false]}, {"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(countdown_complete_letter_spacing)", [{"7": "countdown_complete_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(countdown_complete_letter_spacing)", [{"7": "countdown_complete_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}, {"10": ["line-height", {"6": ["get", "(countdown_complete_line_height)", [{"7": "countdown_complete_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(countdown_complete_text_align), get-base(countdown_complete_text_align))", [{"6": ["get", "(countdown_complete_text_align)", [{"7": "countdown_complete_text_align"}]]}, {"6": ["get-base", "(countdown_complete_text_align)", [{"7": "countdown_complete_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(countdown_complete_text_align)", [{"7": "countdown_complete_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(countdown_complete_text_decoration), get-base(countdown_complete_text_decoration))", [{"6": ["get", "(countdown_complete_text_decoration)", [{"7": "countdown_complete_text_decoration"}]]}, {"6": ["get-base", "(countdown_complete_text_decoration)", [{"7": "countdown_complete_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(countdown_complete_text_decoration)", [{"7": "countdown_complete_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_complete_text_shadow_dimensions)", [{"7": "countdown_complete_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_complete_text_shadow_color)", [{"7": "countdown_complete_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(countdown_complete_text_transform), get-base(countdown_complete_text_transform))", [{"6": ["get", "(countdown_complete_text_transform)", [{"7": "countdown_complete_text_transform"}]]}, {"6": ["get-base", "(countdown_complete_text_transform)", [{"7": "countdown_complete_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(countdown_complete_text_transform)", [{"7": "countdown_complete_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(countdown_complete_text_color)", [{"7": "countdown_complete_text_color"}]]}]]}]]}, {"9": [{"6": ["get", "(countdown_delimiter)", [{"7": "countdown_delimiter"}]]}, [{"3": [[".x-countdown-delimiter"], [{"10": ["margin-top", {"6": ["get", "(countdown_delimiter_vertical_adjustment)", [{"7": "countdown_delimiter_vertical_adjustment"}]]}, false]}, {"10": ["font-family", {"6": ["global-ff", "(get(countdown_delimiter_font_family))", [{"6": ["get", "(countdown_delimiter_font_family)", [{"7": "countdown_delimiter_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(countdown_delimiter_font_size)", [{"7": "countdown_delimiter_font_size"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(countdown_delimiter_font_family),get(countdown_delimiter_font_weight))", [{"6": ["get", "(countdown_delimiter_font_family)", [{"7": "countdown_delimiter_font_family"}]]}, {"6": ["get", "(countdown_delimiter_font_weight)", [{"7": "countdown_delimiter_font_weight"}]]}]]}, false]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(countdown_delimiter_text_shadow_dimensions)", [{"7": "countdown_delimiter_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(countdown_delimiter_text_shadow_color)", [{"7": "countdown_delimiter_text_shadow_color"}]]}]}]]}, {"4": ["text-color", [{"6": ["get", "(countdown_delimiter_text_color)", [{"7": "countdown_delimiter_text_color"}]]}]]}]]}]]}]]}, {"2": ["counter", [], [{"3": [["&.x-counter"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "counter_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(counter_max_width), get-base(counter_max_width))", [{"6": ["get", "(counter_max_width)", [{"7": "counter_max_width"}]]}, {"6": ["get-base", "(counter_max_width)", [{"7": "counter_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(counter_max_width)", [{"7": "counter_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(counter_margin)", [{"7": "counter_margin"}]]}, {"6": ["get", "(counter_margin)", [{"7": "counter_margin"}]]}]]}, {"10": ["font-size", {"6": ["get", "(counter_base_font_size)", [{"7": "counter_base_font_size"}]]}, false]}]]}, {"3": [[".x-counter-number-wrap"], [{"4": ["margin", [{"6": ["get-base", "(counter_number_margin)", [{"7": "counter_number_margin"}]]}, {"6": ["get", "(counter_number_margin)", [{"7": "counter_number_margin"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(counter_number_font_family))", [{"6": ["get", "(counter_number_font_family)", [{"7": "counter_number_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(counter_number_font_size)", [{"7": "counter_number_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(counter_number_font_style)", [{"7": "counter_number_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(counter_number_font_family),get(counter_number_font_weight))", [{"6": ["get", "(counter_number_font_family)", [{"7": "counter_number_font_family"}]]}, {"6": ["get", "(counter_number_font_weight)", [{"7": "counter_number_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(counter_number_line_height)", [{"7": "counter_number_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(counter_number_letter_spacing), get-base(counter_number_letter_spacing))", [{"6": ["get", "(counter_number_letter_spacing)", [{"7": "counter_number_letter_spacing"}]]}, {"6": ["get-base", "(counter_number_letter_spacing)", [{"7": "counter_number_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(counter_number_letter_spacing)", [{"7": "counter_number_letter_spacing"}]]}, false]}, {"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(counter_number_letter_spacing)", [{"7": "counter_number_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(counter_number_letter_spacing)", [{"7": "counter_number_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(counter_number_text_align), get-base(counter_number_text_align))", [{"6": ["get", "(counter_number_text_align)", [{"7": "counter_number_text_align"}]]}, {"6": ["get-base", "(counter_number_text_align)", [{"7": "counter_number_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(counter_number_text_align)", [{"7": "counter_number_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(counter_number_text_decoration), get-base(counter_number_text_decoration))", [{"6": ["get", "(counter_number_text_decoration)", [{"7": "counter_number_text_decoration"}]]}, {"6": ["get-base", "(counter_number_text_decoration)", [{"7": "counter_number_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(counter_number_text_decoration)", [{"7": "counter_number_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(counter_number_text_shadow_dimensions)", [{"7": "counter_number_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(counter_number_text_shadow_color)", [{"7": "counter_number_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(counter_number_text_transform), get-base(counter_number_text_transform))", [{"6": ["get", "(counter_number_text_transform)", [{"7": "counter_number_text_transform"}]]}, {"6": ["get-base", "(counter_number_text_transform)", [{"7": "counter_number_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(counter_number_text_transform)", [{"7": "counter_number_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(counter_number_text_color)", [{"7": "counter_number_text_color"}]]}]]}]]}, {"9": [{"6": ["get", "(counter_before_after)", [{"7": "counter_before_after"}]]}, [{"3": [[".x-counter-before", ".x-counter-after"], [{"10": ["font-family", {"6": ["global-ff", "(get(counter_before_after_font_family))", [{"6": ["get", "(counter_before_after_font_family)", [{"7": "counter_before_after_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(counter_before_after_font_size)", [{"7": "counter_before_after_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(counter_before_after_font_style)", [{"7": "counter_before_after_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(counter_before_after_font_family),get(counter_before_after_font_weight))", [{"6": ["get", "(counter_before_after_font_family)", [{"7": "counter_before_after_font_family"}]]}, {"6": ["get", "(counter_before_after_font_weight)", [{"7": "counter_before_after_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(counter_before_after_line_height)", [{"7": "counter_before_after_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(counter_before_after_letter_spacing), get-base(counter_before_after_letter_spacing))", [{"6": ["get", "(counter_before_after_letter_spacing)", [{"7": "counter_before_after_letter_spacing"}]]}, {"6": ["get-base", "(counter_before_after_letter_spacing)", [{"7": "counter_before_after_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(counter_before_after_letter_spacing)", [{"7": "counter_before_after_letter_spacing"}]]}, false]}, {"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(counter_before_after_letter_spacing)", [{"7": "counter_before_after_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(counter_before_after_letter_spacing)", [{"7": "counter_before_after_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(counter_before_after_text_align), get-base(counter_before_after_text_align))", [{"6": ["get", "(counter_before_after_text_align)", [{"7": "counter_before_after_text_align"}]]}, {"6": ["get-base", "(counter_before_after_text_align)", [{"7": "counter_before_after_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(counter_before_after_text_align)", [{"7": "counter_before_after_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(counter_before_after_text_decoration), get-base(counter_before_after_text_decoration))", [{"6": ["get", "(counter_before_after_text_decoration)", [{"7": "counter_before_after_text_decoration"}]]}, {"6": ["get-base", "(counter_before_after_text_decoration)", [{"7": "counter_before_after_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(counter_before_after_text_decoration)", [{"7": "counter_before_after_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(counter_before_after_text_shadow_dimensions)", [{"7": "counter_before_after_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(counter_before_after_text_shadow_color)", [{"7": "counter_before_after_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(counter_before_after_text_transform), get-base(counter_before_after_text_transform))", [{"6": ["get", "(counter_before_after_text_transform)", [{"7": "counter_before_after_text_transform"}]]}, {"6": ["get-base", "(counter_before_after_text_transform)", [{"7": "counter_before_after_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(counter_before_after_text_transform)", [{"7": "counter_before_after_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(counter_before_after_text_color)", [{"7": "counter_before_after_text_color"}]]}]]}]]}]]}]]}, {"2": ["text-headline", [], [{"3": [["&.x-text"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "text_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(text_max_width), get-base(text_max_width))", [{"6": ["get", "(text_max_width)", [{"7": "text_max_width"}]]}, {"6": ["get-base", "(text_max_width)", [{"7": "text_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(text_max_width)", [{"7": "text_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(text_margin)", [{"7": "text_margin"}]]}, {"6": ["get", "(text_margin)", [{"7": "text_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(text_border_width)", [{"7": "text_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(text_border_style)", [{"7": "text_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(text_border_color)", [{"7": "text_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_border_color_alt)", [{"7": "text_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(text_border_radius)", [{"7": "text_border_radius"}]]}, {"6": ["get", "(text_border_radius)", [{"7": "text_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(text_padding)", [{"7": "text_padding"}]]}, {"6": ["get", "(text_padding)", [{"7": "text_padding"}]]}]]}, {"10": ["font-size", {"6": ["get", "(text_base_font_size)", [{"7": "text_base_font_size"}]]}, false]}, {"4": ["background-color", [{"7": "text_bg_color"}, {"7": "text_bg_color_alt"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(text_box_shadow_dimensions)", [{"7": "text_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_box_shadow_color)", [{"7": "text_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_box_shadow_color_alt)", [{"7": "text_box_shadow_color_alt"}]]}]}]]}]]}, {"3": [["&.x-text:hover", "&.x-text[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-text"], [{"4": ["border-alt", [{"5": ["width", {"6": ["get", "(text_border_width)", [{"7": "text_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(text_border_style)", [{"7": "text_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(text_border_color)", [{"7": "text_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_border_color_alt)", [{"7": "text_border_color_alt"}]]}]}]]}, {"4": ["background-color-alt", [{"6": ["get", "(text_bg_color)", [{"7": "text_bg_color"}]]}, {"6": ["get", "(text_bg_color_alt)", [{"7": "text_bg_color_alt"}]]}]]}, {"4": ["box-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(text_box_shadow_dimensions)", [{"7": "text_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_box_shadow_color)", [{"7": "text_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_box_shadow_color_alt)", [{"7": "text_box_shadow_color_alt"}]]}]}]]}]]}, {"3": [["&.x-text .x-text-content"], [{"9": [{"6": ["get", "(text_graphic)", [{"7": "text_graphic"}]]}, [{"4": ["flexbox", [{"8": "text"}]]}]]}]]}, {"3": [["&.x-text .x-text-content-text"], [{"9": [{"17": [{"6": ["get", "(text_graphic)", [{"7": "text_graphic"}]]}, "and", {"17": [{"6": ["get", "(text_overflow)", [{"7": "text_overflow"}]]}, "and", {"17": [{"6": ["get", "(text_flex_direction)", [{"7": "text_flex_direction"}]]}, "==", {"8": "column"}]}]}]}, [{"10": ["width", {"16": ["100", "%"]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(text_content_margin)", [{"7": "text_content_margin"}]]}, {"6": ["get", "(text_content_margin)", [{"7": "text_content_margin"}]]}]]}]]}, {"3": [["&.x-text .x-text-content-text-primary"], [{"4": ["linotype", [{"5": ["ff", {"6": ["get", "(text_font_family)", [{"7": "text_font_family"}]]}]}, {"5": ["fsize", {"6": ["get", "(text_font_size)", [{"7": "text_font_size"}]]}]}, {"5": ["fstyle", {"6": ["get", "(text_font_style)", [{"7": "text_font_style"}]]}]}, {"5": ["fw", {"6": ["get", "(text_font_weight)", [{"7": "text_font_weight"}]]}]}, {"5": ["lh", {"6": ["get", "(text_line_height)", [{"7": "text_line_height"}]]}]}, {"5": ["ls", {"6": ["get", "(text_letter_spacing)", [{"7": "text_letter_spacing"}]]}]}, {"5": ["ta", {"6": ["get", "(text_text_align)", [{"7": "text_text_align"}]]}]}, {"5": ["td", {"6": ["get", "(text_text_decoration)", [{"7": "text_text_decoration"}]]}]}, {"5": ["tt", {"6": ["get", "(text_text_transform)", [{"7": "text_text_transform"}]]}]}, {"5": ["lsForce", {"7": true}]}, {"5": ["ttForce", {"7": true}]}, {"5": ["lsHasOffset", {"7": true}]}]]}, {"4": ["text-color", [{"6": ["get", "(text_text_color)", [{"7": "text_text_color"}]]}, {"6": ["get", "(text_text_color_alt)", [{"7": "text_text_color_alt"}]]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(text_text_shadow_dimensions)", [{"7": "text_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_text_shadow_color)", [{"7": "text_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_text_shadow_color_alt)", [{"7": "text_text_shadow_color_alt"}]]}]}]]}, {"9": [{"6": ["get", "(text_overflow)", [{"7": "text_overflow"}]]}, [{"10": ["overflow", {"7": "hidden"}, false]}, {"10": ["text-overflow", {"7": "ellipsis"}, false]}, {"10": ["white-space", {"7": "nowrap"}, false]}]]}]]}, {"3": [["&.x-text:hover .x-text-content-text-primary", "&.x-text[class*=\"active\"] .x-text-content-text-primary", "[data-x-effect-provider*=\"colors\"]:hover &.x-text .x-text-content-text-primary"], [{"4": ["text-color-alt", [{"6": ["get", "(text_text_color)", [{"7": "text_text_color"}]]}, {"6": ["get", "(text_text_color_alt)", [{"7": "text_text_color_alt"}]]}]]}, {"4": ["text-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(text_text_shadow_dimensions)", [{"7": "text_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_text_shadow_color)", [{"7": "text_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_text_shadow_color_alt)", [{"7": "text_text_shadow_color_alt"}]]}]}]]}]]}, {"9": [{"6": ["get", "(text_typing)", [{"7": "text_typing"}]]}, [{"9": [{"6": ["changed", "('inherit',get(text_typing_color),get-base(text_typing_color))", [{"8": "inherit"}, {"6": ["get", "(text_typing_color)", [{"7": "text_typing_color"}]]}, {"6": ["get-base", "(text_typing_color)", [{"7": "text_typing_color"}]]}]]}, [{"3": [["&.x-text .x-text-typing"], [{"4": ["text-color", [{"6": ["get", "(text_typing_color)", [{"7": "text_typing_color"}]]}, {"6": ["get", "(text_typing_color_alt)", [{"7": "text_typing_color_alt"}]]}]]}]]}]]}, {"3": [["[data-x-effect-provider*=\"colors\"]:hover &.x-text .x-text-typing", "&.x-text:hover .x-text-typing"], [{"4": ["text-color-alt", [{"6": ["get", "(text_typing_color)", [{"7": "text_typing_color"}]]}, {"6": ["get", "(text_typing_color_alt)", [{"7": "text_typing_color_alt"}]]}]]}]]}, {"9": [{"6": ["get", "(text_typing_cursor)", [{"7": "text_typing_cursor"}]]}, [{"9": [{"6": ["changed", "('inherit',get(text_typing_cursor_color),get-base(text_typing_cursor_color))", [{"8": "inherit"}, {"6": ["get", "(text_typing_cursor_color)", [{"7": "text_typing_cursor_color"}]]}, {"6": ["get-base", "(text_typing_cursor_color)", [{"7": "text_typing_cursor_color"}]]}]]}, [{"3": [["&.x-text .x-typed-cursor"], [{"4": ["text-color", [{"6": ["get", "(text_typing_cursor_color)", [{"7": "text_typing_cursor_color"}]]}, {"6": ["get", "(text_typing_cursor_color_alt)", [{"7": "text_typing_cursor_color_alt"}]]}]]}]]}]]}, {"3": [["&.x-text:hover .x-typed-cursor", "&.x-text:focus .x-typed-cursor", "&.x-text[class*=\"active\"] .x-typed-cursor", "[data-x-effect-provider*=\"colors\"]:hover &.x-text .x-typed-cursor", "[data-x-effect-provider*=\"colors\"]:focus &.x-text .x-typed-cursor"], [{"4": ["text-color-alt", [{"6": ["get", "(text_typing_cursor_color)", [{"7": "text_typing_cursor_color"}]]}, {"6": ["get", "(text_typing_cursor_color_alt)", [{"7": "text_typing_cursor_color_alt"}]]}]]}]]}]]}]]}, {"9": [{"6": ["get", "(text_subheadline)", [{"7": "text_subheadline"}]]}, [{"3": [["&.x-text .x-text-content-text-subheadline"], [{"9": [{"17": [{"6": ["get", "(text_subheadline_reverse)", [{"7": "text_subheadline_reverse"}]]}, "==", {"7": false}]}, [{"10": ["margin-top", {"6": ["get", "(text_subheadline_spacing)", [{"7": "text_subheadline_spacing"}]]}, false]}]]}, {"9": [{"6": ["get", "(text_subheadline_reverse)", [{"7": "text_subheadline_reverse"}]]}, [{"10": ["margin-bottom", {"6": ["get", "(text_subheadline_spacing)", [{"7": "text_subheadline_spacing"}]]}, false]}]]}, {"4": ["linotype", [{"5": ["ff", {"6": ["get", "(text_subheadline_font_family)", [{"7": "text_subheadline_font_family"}]]}]}, {"5": ["fsize", {"6": ["get", "(text_subheadline_font_size)", [{"7": "text_subheadline_font_size"}]]}]}, {"5": ["fstyle", {"6": ["get", "(text_subheadline_font_style)", [{"7": "text_subheadline_font_style"}]]}]}, {"5": ["fw", {"6": ["get", "(text_subheadline_font_weight)", [{"7": "text_subheadline_font_weight"}]]}]}, {"5": ["lh", {"6": ["get", "(text_subheadline_line_height)", [{"7": "text_subheadline_line_height"}]]}]}, {"5": ["ls", {"6": ["get", "(text_subheadline_letter_spacing)", [{"7": "text_subheadline_letter_spacing"}]]}]}, {"5": ["ta", {"6": ["get", "(text_subheadline_text_align)", [{"7": "text_subheadline_text_align"}]]}]}, {"5": ["td", {"6": ["get", "(text_subheadline_text_decoration)", [{"7": "text_subheadline_text_decoration"}]]}]}, {"5": ["tt", {"6": ["get", "(text_subheadline_text_transform)", [{"7": "text_subheadline_text_transform"}]]}]}, {"5": ["lsForce", {"7": true}]}, {"5": ["ttForce", {"7": true}]}, {"5": ["lsHasOffset", {"7": true}]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(text_subheadline_text_shadow_dimensions)", [{"7": "text_subheadline_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_subheadline_text_shadow_color)", [{"7": "text_subheadline_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_subheadline_text_shadow_color_alt)", [{"7": "text_subheadline_text_shadow_color_alt"}]]}]}]]}, {"4": ["text-color", [{"6": ["get", "(text_subheadline_text_color)", [{"7": "text_subheadline_text_color"}]]}, {"6": ["get", "(text_subheadline_text_color_alt)", [{"7": "text_subheadline_text_color_alt"}]]}]]}, {"9": [{"6": ["get", "(text_overflow)", [{"7": "text_overflow"}]]}, [{"10": ["overflow", {"7": "hidden"}, false]}, {"10": ["text-overflow", {"7": "ellipsis"}, false]}, {"10": ["white-space", {"7": "nowrap"}, false]}]]}]]}, {"3": [["&.x-text:hover .x-text-content-text-subheadline", "&.x-text[class*=\"active\"] .x-text-content-text-subheadline", "[data-x-effect-provider*=\"colors\"]:hover &.x-text .x-text-content-text-subheadline"], [{"4": ["text-color-alt", [{"6": ["get", "(text_subheadline_text_color)", [{"7": "text_subheadline_text_color"}]]}, {"6": ["get", "(text_subheadline_text_color_alt)", [{"7": "text_subheadline_text_color_alt"}]]}]]}, {"4": ["text-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(text_subheadline_text_shadow_dimensions)", [{"7": "text_subheadline_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_subheadline_text_shadow_color)", [{"7": "text_subheadline_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_subheadline_text_shadow_color_alt)", [{"7": "text_subheadline_text_shadow_color_alt"}]]}]}]]}]]}]]}, {"9": [{"6": ["get", "(text_graphic)", [{"7": "text_graphic"}]]}, [{"4": ["graphic", [{"8": "&.x-text"}, {"8": "text"}]]}]]}]]}, {"2": ["text-standard", [], [{"3": [["&.x-text"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "text_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(text_max_width), get-base(text_max_width))", [{"6": ["get", "(text_max_width)", [{"7": "text_max_width"}]]}, {"6": ["get-base", "(text_max_width)", [{"7": "text_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(text_max_width)", [{"7": "text_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(text_margin)", [{"7": "text_margin"}]]}, {"6": ["get", "(text_margin)", [{"7": "text_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(text_border_width)", [{"7": "text_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(text_border_style)", [{"7": "text_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(text_border_color)", [{"7": "text_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_border_color_alt)", [{"7": "text_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(text_border_radius)", [{"7": "text_border_radius"}]]}, {"6": ["get", "(text_border_radius)", [{"7": "text_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(text_padding)", [{"7": "text_padding"}]]}, {"6": ["get", "(text_padding)", [{"7": "text_padding"}]]}]]}, {"4": ["linotype", [{"5": ["ff", {"6": ["get", "(text_font_family)", [{"7": "text_font_family"}]]}]}, {"5": ["fsize", {"6": ["get", "(text_font_size)", [{"7": "text_font_size"}]]}]}, {"5": ["fstyle", {"6": ["get", "(text_font_style)", [{"7": "text_font_style"}]]}]}, {"5": ["fw", {"6": ["get", "(text_font_weight)", [{"7": "text_font_weight"}]]}]}, {"5": ["lh", {"6": ["get", "(text_line_height)", [{"7": "text_line_height"}]]}]}, {"5": ["ls", {"6": ["get", "(text_letter_spacing)", [{"7": "text_letter_spacing"}]]}]}, {"5": ["ta", {"6": ["get", "(text_text_align)", [{"7": "text_text_align"}]]}]}, {"5": ["td", {"6": ["get", "(text_text_decoration)", [{"7": "text_text_decoration"}]]}]}, {"5": ["tt", {"6": ["get", "(text_text_transform)", [{"7": "text_text_transform"}]]}]}, {"5": ["lsForce", {"7": true}]}, {"5": ["ttForce", {"7": true}]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(text_text_shadow_dimensions)", [{"7": "text_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_text_shadow_color)", [{"7": "text_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_text_shadow_color_alt)", [{"7": "text_text_shadow_color_alt"}]]}]}]]}, {"4": ["text-color", [{"6": ["get", "(text_text_color)", [{"7": "text_text_color"}]]}, {"6": ["get", "(text_text_color_alt)", [{"7": "text_text_color_alt"}]]}]]}, {"4": ["background-color", [{"7": "text_bg_color"}, {"7": "text_bg_color_alt"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(text_box_shadow_dimensions)", [{"7": "text_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_box_shadow_color)", [{"7": "text_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_box_shadow_color_alt)", [{"7": "text_box_shadow_color_alt"}]]}]}]]}, {"9": [{"6": ["get", "(text_columns)", [{"7": "text_columns"}]]}, [{"10": ["columns", {"12": [{"6": ["get", "(text_columns_width)", [{"7": "text_columns_width"}]]}, {"6": ["get", "(text_columns_count)", [{"7": "text_columns_count"}]]}]}, false]}, {"10": ["column-gap", {"6": ["get", "(text_columns_gap)", [{"7": "text_columns_gap"}]]}, false]}, {"10": ["column-rule", {"12": [{"6": ["get", "(text_columns_rule_width)", [{"7": "text_columns_rule_width"}]]}, {"6": ["get", "(text_columns_rule_style)", [{"7": "text_columns_rule_style"}]]}, {"6": ["global-color", "(get(text_columns_rule_color))", [{"6": ["get", "(text_columns_rule_color)", [{"7": "text_columns_rule_color"}]]}]]}]}, false]}]]}]]}, {"3": [["&.x-text > :first-child"], [{"10": ["margin-top", {"13": "0"}, false]}]]}, {"3": [["&.x-text > :last-child"], [{"10": ["margin-bottom", {"13": "0"}, false]}]]}, {"9": [{"17": [{"6": ["get", "(text_columns)", [{"7": "text_columns"}]]}, "and", {"17": [{"6": ["get", "(text_columns_break_inside)", [{"7": "text_columns_break_inside"}]]}, "==", {"8": "avoid"}]}]}, [{"3": [["&.x-text > *"], [{"10": ["-webkit-column-break-inside", {"7": "avoid"}, false]}, {"10": ["page-break-inside", {"7": "avoid"}, false]}, {"10": ["break-inside", {"7": "avoid"}, false]}]]}]]}, {"3": [["&.x-text:hover", "&.x-text[class*=\"active\"]", "[data-x-effect-provider*=\"colors\"]:hover &.x-text"], [{"4": ["text-color-alt", [{"6": ["get", "(text_text_color)", [{"7": "text_text_color"}]]}, {"6": ["get", "(text_text_color_alt)", [{"7": "text_text_color_alt"}]]}]]}, {"4": ["border-alt", [{"5": ["width", {"6": ["get", "(text_border_width)", [{"7": "text_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(text_border_style)", [{"7": "text_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(text_border_color)", [{"7": "text_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_border_color_alt)", [{"7": "text_border_color_alt"}]]}]}]]}, {"4": ["background-color-alt", [{"6": ["get", "(text_bg_color)", [{"7": "text_bg_color"}]]}, {"6": ["get", "(text_bg_color_alt)", [{"7": "text_bg_color_alt"}]]}]]}, {"4": ["box-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(text_box_shadow_dimensions)", [{"7": "text_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_box_shadow_color)", [{"7": "text_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_box_shadow_color_alt)", [{"7": "text_box_shadow_color_alt"}]]}]}]]}, {"4": ["text-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(text_text_shadow_dimensions)", [{"7": "text_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(text_text_shadow_color)", [{"7": "text_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(text_text_shadow_color_alt)", [{"7": "text_text_shadow_color_alt"}]]}]}]]}, {"9": [{"6": ["get", "(text_columns)", [{"7": "text_columns"}]]}, [{"9": [{"11": ["not", {"17": [{"6": ["contains", "(get(text_columns_rule_color_alt),'transparent')", [{"6": ["get", "(text_columns_rule_color_alt)", [{"7": "text_columns_rule_color_alt"}]]}, {"8": "transparent"}]]}, "or", {"17": [{"17": [{"6": ["get", "(text_columns_rule_color_alt)", [{"7": "text_columns_rule_color_alt"}]]}, "==", {"8": ""}]}, "and", {"17": [{"6": ["get", "(text_columns_rule_color_alt)", [{"7": "text_columns_rule_color_alt"}]]}, "!=", {"6": ["get", "(text_columns_rule_color)", [{"7": "text_columns_rule_color"}]]}]}]}]}]}, [{"10": ["-webkit-column-rule", {"12": [{"6": ["get", "(text_columns_rule_width)", [{"7": "text_columns_rule_width"}]]}, {"6": ["get", "(text_columns_rule_style)", [{"7": "text_columns_rule_style"}]]}, {"6": ["get", "(text_columns_rule_color_alt)", [{"7": "text_columns_rule_color_alt"}]]}]}, false]}, {"10": ["column-rule", {"12": [{"6": ["get", "(text_columns_rule_width)", [{"7": "text_columns_rule_width"}]]}, {"6": ["get", "(text_columns_rule_style)", [{"7": "text_columns_rule_style"}]]}, {"6": ["get", "(text_columns_rule_color_alt)", [{"7": "text_columns_rule_color_alt"}]]}]}, false]}]]}]]}]]}]]}, {"2": ["quote", [], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "quote_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(quote_max_width), get-base(quote_max_width))", [{"6": ["get", "(quote_max_width)", [{"7": "quote_max_width"}]]}, {"6": ["get-base", "(quote_max_width)", [{"7": "quote_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(quote_max_width)", [{"7": "quote_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(quote_margin)", [{"7": "quote_margin"}]]}, {"6": ["get", "(quote_margin)", [{"7": "quote_margin"}]]}, {"13": "0"}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(quote_border_width)", [{"7": "quote_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(quote_border_style)", [{"7": "quote_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(quote_border_color)", [{"7": "quote_border_color"}]]}]}, {"5": ["fallback", {"7": true}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(quote_border_radius)", [{"7": "quote_border_radius"}]]}, {"6": ["get", "(quote_border_radius)", [{"7": "quote_border_radius"}]]}, {"13": "0"}]]}, {"4": ["padding", [{"6": ["get-base", "(quote_padding)", [{"7": "quote_padding"}]]}, {"6": ["get", "(quote_padding)", [{"7": "quote_padding"}]]}, {"13": "0"}]]}, {"10": ["font-size", {"6": ["get", "(quote_base_font_size)", [{"7": "quote_base_font_size"}]]}, false]}, {"4": ["background-color", [{"7": "quote_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(quote_box_shadow_dimensions)", [{"7": "quote_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(quote_box_shadow_color)", [{"7": "quote_box_shadow_color"}]]}]}]]}, {"9": [{"17": [{"6": ["get", "(quote_marks_opening_graphic)", [{"7": "quote_marks_opening_graphic"}]]}, "or", {"6": ["get", "(quote_marks_closing_graphic)", [{"7": "quote_marks_closing_graphic"}]]}]}, [{"10": ["flex-direction", {"6": ["get", "(quote_marks_graphic_direction)", [{"7": "quote_marks_graphic_direction"}]]}, false]}]]}, {"9": [{"6": ["get", "(quote_marks_opening_graphic)", [{"7": "quote_marks_opening_graphic"}]]}, [{"3": [[".x-quote-mark-opening"], [{"10": ["align-self", {"6": ["get", "(quote_marks_graphic_opening_align)", [{"7": "quote_marks_graphic_opening_align"}]]}, false]}]]}, {"4": ["graphic", [{"18": ".x-quote-mark-opening"}, {"18": "quote_marks_opening"}, {"7": true}]]}]]}, {"9": [{"6": ["get", "(quote_marks_closing_graphic)", [{"7": "quote_marks_closing_graphic"}]]}, [{"3": [[".x-quote-mark-closing"], [{"10": ["align-self", {"6": ["get", "(quote_marks_graphic_closing_align)", [{"7": "quote_marks_graphic_closing_align"}]]}, false]}]]}, {"4": ["graphic", [{"18": ".x-quote-mark-closing"}, {"18": "quote_marks_closing"}, {"7": true}]]}]]}, {"3": [[".x-quote-text"], [{"10": ["font-family", {"6": ["global-ff", "(get(quote_text_font_family))", [{"6": ["get", "(quote_text_font_family)", [{"7": "quote_text_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(quote_text_font_size)", [{"7": "quote_text_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(quote_text_font_style)", [{"7": "quote_text_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(quote_text_font_family),get(quote_text_font_weight))", [{"6": ["get", "(quote_text_font_family)", [{"7": "quote_text_font_family"}]]}, {"6": ["get", "(quote_text_font_weight)", [{"7": "quote_text_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(quote_text_line_height)", [{"7": "quote_text_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(quote_text_letter_spacing), get-base(quote_text_letter_spacing))", [{"6": ["get", "(quote_text_letter_spacing)", [{"7": "quote_text_letter_spacing"}]]}, {"6": ["get-base", "(quote_text_letter_spacing)", [{"7": "quote_text_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(quote_text_letter_spacing)", [{"7": "quote_text_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(quote_text_text_align), get-base(quote_text_text_align))", [{"6": ["get", "(quote_text_text_align)", [{"7": "quote_text_text_align"}]]}, {"6": ["get-base", "(quote_text_text_align)", [{"7": "quote_text_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(quote_text_text_align)", [{"7": "quote_text_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(quote_text_text_decoration), get-base(quote_text_text_decoration))", [{"6": ["get", "(quote_text_text_decoration)", [{"7": "quote_text_text_decoration"}]]}, {"6": ["get-base", "(quote_text_text_decoration)", [{"7": "quote_text_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(quote_text_text_decoration)", [{"7": "quote_text_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(quote_text_text_shadow_dimensions)", [{"7": "quote_text_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(quote_text_text_shadow_color)", [{"7": "quote_text_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(quote_text_text_transform), get-base(quote_text_text_transform))", [{"6": ["get", "(quote_text_text_transform)", [{"7": "quote_text_text_transform"}]]}, {"6": ["get-base", "(quote_text_text_transform)", [{"7": "quote_text_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(quote_text_text_transform)", [{"7": "quote_text_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(quote_text_text_color)", [{"7": "quote_text_text_color"}]]}]]}]]}, {"9": [{"17": [{"6": ["get", "(quote_cite_content)", [{"7": "quote_cite_content"}]]}, "!=", {"8": ""}]}, [{"3": [[".x-quote-cite"], [{"4": ["flexbox", [{"8": "quote_cite"}]]}, {"4": ["margin", [{"6": ["get-base", "(quote_cite_margin)", [{"7": "quote_cite_margin"}]]}, {"6": ["get", "(quote_cite_margin)", [{"7": "quote_cite_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(quote_cite_border_width)", [{"7": "quote_cite_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(quote_cite_border_style)", [{"7": "quote_cite_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(quote_cite_border_color)", [{"7": "quote_cite_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(quote_cite_border_radius)", [{"7": "quote_cite_border_radius"}]]}, {"6": ["get", "(quote_cite_border_radius)", [{"7": "quote_cite_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(quote_cite_padding)", [{"7": "quote_cite_padding"}]]}, {"6": ["get", "(quote_cite_padding)", [{"7": "quote_cite_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(quote_cite_font_family))", [{"6": ["get", "(quote_cite_font_family)", [{"7": "quote_cite_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(quote_cite_font_size)", [{"7": "quote_cite_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(quote_cite_font_style)", [{"7": "quote_cite_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(quote_cite_font_family),get(quote_cite_font_weight))", [{"6": ["get", "(quote_cite_font_family)", [{"7": "quote_cite_font_family"}]]}, {"6": ["get", "(quote_cite_font_weight)", [{"7": "quote_cite_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(quote_cite_line_height)", [{"7": "quote_cite_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(quote_cite_letter_spacing), get-base(quote_cite_letter_spacing))", [{"6": ["get", "(quote_cite_letter_spacing)", [{"7": "quote_cite_letter_spacing"}]]}, {"6": ["get-base", "(quote_cite_letter_spacing)", [{"7": "quote_cite_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(quote_cite_letter_spacing)", [{"7": "quote_cite_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(quote_cite_text_align), get-base(quote_cite_text_align))", [{"6": ["get", "(quote_cite_text_align)", [{"7": "quote_cite_text_align"}]]}, {"6": ["get-base", "(quote_cite_text_align)", [{"7": "quote_cite_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(quote_cite_text_align)", [{"7": "quote_cite_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(quote_cite_text_decoration), get-base(quote_cite_text_decoration))", [{"6": ["get", "(quote_cite_text_decoration)", [{"7": "quote_cite_text_decoration"}]]}, {"6": ["get-base", "(quote_cite_text_decoration)", [{"7": "quote_cite_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(quote_cite_text_decoration)", [{"7": "quote_cite_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(quote_cite_text_shadow_dimensions)", [{"7": "quote_cite_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(quote_cite_text_shadow_color)", [{"7": "quote_cite_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(quote_cite_text_transform), get-base(quote_cite_text_transform))", [{"6": ["get", "(quote_cite_text_transform)", [{"7": "quote_cite_text_transform"}]]}, {"6": ["get-base", "(quote_cite_text_transform)", [{"7": "quote_cite_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(quote_cite_text_transform)", [{"7": "quote_cite_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(quote_cite_text_color)", [{"7": "quote_cite_text_color"}]]}]]}, {"4": ["background-color", [{"7": "quote_cite_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(quote_cite_box_shadow_dimensions)", [{"7": "quote_cite_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(quote_cite_box_shadow_color)", [{"7": "quote_cite_box_shadow_color"}]]}]}]]}]]}, {"3": [[".x-quote-cite-text"], [{"9": [{"6": ["is-set", "(get(quote_cite_letter_spacing), get-base(quote_cite_letter_spacing))", [{"6": ["get", "(quote_cite_letter_spacing)", [{"7": "quote_cite_letter_spacing"}]]}, {"6": ["get-base", "(quote_cite_letter_spacing)", [{"7": "quote_cite_letter_spacing"}]]}]]}, [{"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(quote_cite_letter_spacing)", [{"7": "quote_cite_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(quote_cite_letter_spacing)", [{"7": "quote_cite_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}]]}, {"9": [{"6": ["get", "(quote_cite_graphic)", [{"7": "quote_cite_graphic"}]]}, [{"4": ["graphic", [{"5": ["selector", {"18": " .x-quote-cite-mark"}]}, {"5": ["keyPrefix", {"18": "quote_cite"}]}, {"5": ["noBase", {"7": true}]}]]}]]}, {"9": [{"17": [{"6": ["get", "(quote_cite_position)", [{"7": "quote_cite_position"}]]}, "==", {"8": "before"}]}, [{"3": [[".x-quote-content"], [{"10": ["flex-direction", {"7": "column-reverse"}, false]}]]}]]}]]}]]}, {"2": ["statbar", [], [{"3": [["&.x-statbar"], [{"9": [{"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "!=", {"8": "row"}]}, [{"10": ["flex-direction", {"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, false]}]]}, {"9": [{"17": [{"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "row"}]}, "or", {"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "row-reverse"}]}]}, [{"4": ["changedmixin", [{"8": "auto"}, {"7": "statbar_width_row"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(statbar_max_width_row), get-base(statbar_max_width_row))", [{"6": ["get", "(statbar_max_width_row)", [{"7": "statbar_max_width_row"}]]}, {"6": ["get-base", "(statbar_max_width_row)", [{"7": "statbar_max_width_row"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(statbar_max_width_row)", [{"7": "statbar_max_width_row"}]]}, false]}]]}, {"4": ["changedmixin", [{"8": "auto"}, {"7": "statbar_height_row"}, {"8": "height"}]]}, {"9": [{"6": ["is-set", "(get(statbar_max_height_row), get-base(statbar_max_height_row))", [{"6": ["get", "(statbar_max_height_row)", [{"7": "statbar_max_height_row"}]]}, {"6": ["get-base", "(statbar_max_height_row)", [{"7": "statbar_max_height_row"}]]}]]}, [{"10": ["max-height", {"6": ["get", "(statbar_max_height_row)", [{"7": "statbar_max_height_row"}]]}, false]}]]}]]}, {"9": [{"17": [{"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "column"}]}, "or", {"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "column-reverse"}]}]}, [{"4": ["changedmixin", [{"8": "auto"}, {"7": "statbar_width_column"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(statbar_max_width_column), get-base(statbar_max_width_column))", [{"6": ["get", "(statbar_max_width_column)", [{"7": "statbar_max_width_column"}]]}, {"6": ["get-base", "(statbar_max_width_column)", [{"7": "statbar_max_width_column"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(statbar_max_width_column)", [{"7": "statbar_max_width_column"}]]}, false]}]]}, {"4": ["changedmixin", [{"8": "auto"}, {"7": "statbar_height_column"}, {"8": "height"}]]}, {"9": [{"6": ["is-set", "(get(statbar_max_height_column), get-base(statbar_max_height_column))", [{"6": ["get", "(statbar_max_height_column)", [{"7": "statbar_max_height_column"}]]}, {"6": ["get-base", "(statbar_max_height_column)", [{"7": "statbar_max_height_column"}]]}]]}, [{"10": ["max-height", {"6": ["get", "(statbar_max_height_column)", [{"7": "statbar_max_height_column"}]]}, false]}]]}]]}, {"4": ["margin", [{"6": ["get-base", "(statbar_margin)", [{"7": "statbar_margin"}]]}, {"6": ["get", "(statbar_margin)", [{"7": "statbar_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(statbar_border_width)", [{"7": "statbar_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(statbar_border_style)", [{"7": "statbar_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(statbar_border_color)", [{"7": "statbar_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(statbar_border_radius)", [{"7": "statbar_border_radius"}]]}, {"6": ["get", "(statbar_border_radius)", [{"7": "statbar_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(statbar_padding)", [{"7": "statbar_padding"}]]}, {"6": ["get", "(statbar_padding)", [{"7": "statbar_padding"}]]}]]}, {"10": ["font-size", {"6": ["get", "(statbar_base_font_size)", [{"7": "statbar_base_font_size"}]]}, false]}, {"4": ["background-color", [{"7": "statbar_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(statbar_box_shadow_dimensions)", [{"7": "statbar_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(statbar_box_shadow_color)", [{"7": "statbar_box_shadow_color"}]]}]}]]}]]}, {"3": [["&.x-statbar .x-statbar-bar"], [{"9": [{"17": [{"6": ["get", "(statbar_label)", [{"7": "statbar_label"}]]}, "==", {"7": true}]}, [{"10": ["justify-content", {"6": ["get", "(statbar_label_justify)", [{"7": "statbar_label_justify"}]]}, false]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(statbar_bar_border_radius)", [{"7": "statbar_bar_border_radius"}]]}, {"6": ["get", "(statbar_bar_border_radius)", [{"7": "statbar_bar_border_radius"}]]}]]}, {"4": ["background-color", [{"7": "statbar_bar_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(statbar_bar_box_shadow_dimensions)", [{"7": "statbar_bar_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(statbar_bar_box_shadow_color)", [{"7": "statbar_bar_box_shadow_color"}]]}]}]]}]]}, {"9": [{"17": [{"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "row"}]}, "or", {"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "row-reverse"}]}]}, [{"3": [["&.x-statbar .x-statbar-bar"], [{"10": ["width", {"13": "0"}, false]}]]}, {"3": [["&.x-statbar .x-statbar-bar.x-active"], [{"10": ["width", {"6": ["get", "(statbar_bar_length)", [{"7": "statbar_bar_length"}]]}, false]}, {"10": ["height", {"16": ["100", "%"]}, false]}]]}]]}, {"9": [{"17": [{"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "column"}]}, "or", {"17": [{"6": ["get", "(statbar_direction)", [{"7": "statbar_direction"}]]}, "==", {"8": "column-reverse"}]}]}, [{"3": [["&.x-statbar .x-statbar-bar"], [{"10": ["height", {"13": "0"}, false]}]]}, {"3": [["&.x-statbar .x-statbar-bar.x-active"], [{"10": ["width", {"16": ["100", "%"]}, false]}, {"10": ["height", {"6": ["get", "(statbar_bar_length)", [{"7": "statbar_bar_length"}]]}, false]}]]}]]}, {"9": [{"6": ["get", "(statbar_label)", [{"7": "statbar_label"}]]}, [{"3": [["&.x-statbar .x-statbar-label"], [{"9": [{"6": ["changed", "('auto',get(statbar_label_width),get-base(statbar_label_width))", [{"8": "auto"}, {"6": ["get", "(statbar_label_width)", [{"7": "statbar_label_width"}]]}, {"6": ["get-base", "(statbar_label_width)", [{"7": "statbar_label_width"}]]}]]}, [{"10": ["width", {"6": ["get", "(statbar_label_width)", [{"7": "statbar_label_width"}]]}, false]}, {"10": ["min-width", {"6": ["get", "(statbar_label_width)", [{"7": "statbar_label_width"}]]}, false]}]]}, {"9": [{"6": ["changed", "('auto',get(statbar_label_height),get-base(statbar_label_height))", [{"8": "auto"}, {"6": ["get", "(statbar_label_height)", [{"7": "statbar_label_height"}]]}, {"6": ["get-base", "(statbar_label_height)", [{"7": "statbar_label_height"}]]}]]}, [{"10": ["height", {"6": ["get", "(statbar_label_height)", [{"7": "statbar_label_height"}]]}, false]}, {"10": ["min-height", {"6": ["get", "(statbar_label_height)", [{"7": "statbar_label_height"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(statbar_label_margin)", [{"7": "statbar_label_margin"}]]}, {"6": ["get", "(statbar_label_margin)", [{"7": "statbar_label_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(statbar_label_border_width)", [{"7": "statbar_label_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(statbar_label_border_style)", [{"7": "statbar_label_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(statbar_label_border_color)", [{"7": "statbar_label_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(statbar_label_border_radius)", [{"7": "statbar_label_border_radius"}]]}, {"6": ["get", "(statbar_label_border_radius)", [{"7": "statbar_label_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(statbar_label_padding)", [{"7": "statbar_label_padding"}]]}, {"6": ["get", "(statbar_label_padding)", [{"7": "statbar_label_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(statbar_label_font_family))", [{"6": ["get", "(statbar_label_font_family)", [{"7": "statbar_label_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(statbar_label_font_size)", [{"7": "statbar_label_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(statbar_label_font_style)", [{"7": "statbar_label_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(statbar_label_font_family),get(statbar_label_font_weight))", [{"6": ["get", "(statbar_label_font_family)", [{"7": "statbar_label_font_family"}]]}, {"6": ["get", "(statbar_label_font_weight)", [{"7": "statbar_label_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(statbar_label_line_height)", [{"7": "statbar_label_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(statbar_label_letter_spacing), get-base(statbar_label_letter_spacing))", [{"6": ["get", "(statbar_label_letter_spacing)", [{"7": "statbar_label_letter_spacing"}]]}, {"6": ["get-base", "(statbar_label_letter_spacing)", [{"7": "statbar_label_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(statbar_label_letter_spacing)", [{"7": "statbar_label_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(statbar_label_text_align), get-base(statbar_label_text_align))", [{"6": ["get", "(statbar_label_text_align)", [{"7": "statbar_label_text_align"}]]}, {"6": ["get-base", "(statbar_label_text_align)", [{"7": "statbar_label_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(statbar_label_text_align)", [{"7": "statbar_label_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(statbar_label_text_decoration), get-base(statbar_label_text_decoration))", [{"6": ["get", "(statbar_label_text_decoration)", [{"7": "statbar_label_text_decoration"}]]}, {"6": ["get-base", "(statbar_label_text_decoration)", [{"7": "statbar_label_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(statbar_label_text_decoration)", [{"7": "statbar_label_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(statbar_label_text_shadow_dimensions)", [{"7": "statbar_label_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(statbar_label_text_shadow_color)", [{"7": "statbar_label_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(statbar_label_text_transform), get-base(statbar_label_text_transform))", [{"6": ["get", "(statbar_label_text_transform)", [{"7": "statbar_label_text_transform"}]]}, {"6": ["get-base", "(statbar_label_text_transform)", [{"7": "statbar_label_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(statbar_label_text_transform)", [{"7": "statbar_label_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(statbar_label_text_color)", [{"7": "statbar_label_text_color"}]]}]]}, {"4": ["background-color", [{"7": "statbar_label_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(statbar_label_box_shadow_dimensions)", [{"7": "statbar_label_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(statbar_label_box_shadow_color)", [{"7": "statbar_label_box_shadow_color"}]]}]}]]}, {"10": ["transform", {"6": ["translate3d", {"14": ["(%s, %s, 0)", [{"6": ["get", "(statbar_label_translate_x)", [{"7": "statbar_label_translate_x"}]]}, {"6": ["get", "(statbar_label_translate_y)", [{"7": "statbar_label_translate_y"}]]}]]}, [{"7": {"14": ["%s", [{"6": ["get", "(statbar_label_translate_x)", [{"7": "statbar_label_translate_x"}]]}]]}}, {"7": {"14": ["%s", [{"6": ["get", "(statbar_label_translate_y)", [{"7": "statbar_label_translate_y"}]]}]]}}, {"13": "0"}]]}, false]}]]}, {"3": [["&.x-statbar .x-statbar-label span"], [{"9": [{"6": ["is-set", "(get(statbar_label_letter_spacing), get-base(statbar_label_letter_spacing))", [{"6": ["get", "(statbar_label_letter_spacing)", [{"7": "statbar_label_letter_spacing"}]]}, {"6": ["get-base", "(statbar_label_letter_spacing)", [{"7": "statbar_label_letter_spacing"}]]}]]}, [{"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(statbar_label_letter_spacing)", [{"7": "statbar_label_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(statbar_label_letter_spacing)", [{"7": "statbar_label_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}]]}]]}]]}, {"2": ["tabs", [], [{"3": [["&.x-tabs"], [{"4": ["changedmixin", [{"8": "auto"}, {"7": "tabs_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(tabs_max_width), get-base(tabs_max_width))", [{"6": ["get", "(tabs_max_width)", [{"7": "tabs_max_width"}]]}, {"6": ["get-base", "(tabs_max_width)", [{"7": "tabs_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(tabs_max_width)", [{"7": "tabs_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(tabs_margin)", [{"7": "tabs_margin"}]]}, {"6": ["get", "(tabs_margin)", [{"7": "tabs_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(tabs_border_width)", [{"7": "tabs_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(tabs_border_style)", [{"7": "tabs_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_border_color)", [{"7": "tabs_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(tabs_border_radius)", [{"7": "tabs_border_radius"}]]}, {"6": ["get", "(tabs_border_radius)", [{"7": "tabs_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(tabs_padding)", [{"7": "tabs_padding"}]]}, {"6": ["get", "(tabs_padding)", [{"7": "tabs_padding"}]]}]]}, {"10": ["font-size", {"6": ["get", "(tabs_base_font_size)", [{"7": "tabs_base_font_size"}]]}, false]}, {"4": ["background-color", [{"7": "tabs_bg_color"}]]}, {"4": ["box-shadow", [{"6": ["get", "(tabs_box_shadow_dimensions)", [{"7": "tabs_box_shadow_dimensions"}]]}, {"6": ["get", "(tabs_box_shadow_color)", [{"7": "tabs_box_shadow_color"}]]}]]}]]}, {"3": [["& > .x-tabs-list"], [{"4": ["margin", [{"6": ["get-base", "(tabs_tablist_margin)", [{"7": "tabs_tablist_margin"}]]}, {"6": ["get", "(tabs_tablist_margin)", [{"7": "tabs_tablist_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(tabs_tablist_border_width)", [{"7": "tabs_tablist_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(tabs_tablist_border_style)", [{"7": "tabs_tablist_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_tablist_border_color)", [{"7": "tabs_tablist_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(tabs_tablist_border_radius)", [{"7": "tabs_tablist_border_radius"}]]}, {"6": ["get", "(tabs_tablist_border_radius)", [{"7": "tabs_tablist_border_radius"}]]}]]}, {"4": ["background-color", [{"7": "tabs_tablist_bg_color"}]]}, {"4": ["box-shadow", [{"6": ["get", "(tabs_tablist_box_shadow_dimensions)", [{"7": "tabs_tablist_box_shadow_dimensions"}]]}, {"6": ["get", "(tabs_tablist_box_shadow_color)", [{"7": "tabs_tablist_box_shadow_color"}]]}]]}]]}, {"3": [["& > .x-tabs-list ul"], [{"10": ["justify-content", {"6": ["get", "(tabs_tabs_justify_content)", [{"7": "tabs_tabs_justify_content"}]]}, false]}, {"4": ["padding", [{"6": ["get-base", "(tabs_tablist_padding)", [{"7": "tabs_tablist_padding"}]]}, {"6": ["get", "(tabs_tablist_padding)", [{"7": "tabs_tablist_padding"}]]}]]}, {"4": ["changedmixin", [{"8": "row"}, {"7": "tabs_tablist_direction"}, {"8": "flex-direction"}]]}]]}, {"3": [["& > .x-tabs-list li"], [{"9": [{"6": ["is-set", "(get(tabs_tabs_min_width), get-base(tabs_tabs_min_width))", [{"6": ["get", "(tabs_tabs_min_width)", [{"7": "tabs_tabs_min_width"}]]}, {"6": ["get-base", "(tabs_tabs_min_width)", [{"7": "tabs_tabs_min_width"}]]}]]}, [{"10": ["min-width", {"6": ["get", "(tabs_tabs_min_width)", [{"7": "tabs_tabs_min_width"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(tabs_tabs_max_width), get-base(tabs_tabs_max_width))", [{"6": ["get", "(tabs_tabs_max_width)", [{"7": "tabs_tabs_max_width"}]]}, {"6": ["get-base", "(tabs_tabs_max_width)", [{"7": "tabs_tabs_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(tabs_tabs_max_width)", [{"7": "tabs_tabs_max_width"}]]}, false]}]]}, {"9": [{"6": ["get", "(tabs_tabs_fill_space)", [{"7": "tabs_tabs_fill_space"}]]}, [{"10": ["flex", {"12": [{"13": "1"}, {"13": "0"}, {"16": ["0", "%"]}]}, false]}]]}]]}, {"3": [["& > .x-tabs-list button"], [{"4": ["margin", [{"6": ["get-base", "(tabs_tabs_margin)", [{"7": "tabs_tabs_margin"}]]}, {"6": ["get", "(tabs_tabs_margin)", [{"7": "tabs_tabs_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(tabs_tabs_border_width)", [{"7": "tabs_tabs_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(tabs_tabs_border_style)", [{"7": "tabs_tabs_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_tabs_border_color)", [{"7": "tabs_tabs_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(tabs_tabs_border_color_alt)", [{"7": "tabs_tabs_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(tabs_tabs_border_radius)", [{"7": "tabs_tabs_border_radius"}]]}, {"6": ["get", "(tabs_tabs_border_radius)", [{"7": "tabs_tabs_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(tabs_tabs_padding)", [{"7": "tabs_tabs_padding"}]]}, {"6": ["get", "(tabs_tabs_padding)", [{"7": "tabs_tabs_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(tabs_tabs_font_family))", [{"6": ["get", "(tabs_tabs_font_family)", [{"7": "tabs_tabs_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(tabs_tabs_font_size)", [{"7": "tabs_tabs_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(tabs_tabs_font_style)", [{"7": "tabs_tabs_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(tabs_tabs_font_family),get(tabs_tabs_font_weight))", [{"6": ["get", "(tabs_tabs_font_family)", [{"7": "tabs_tabs_font_family"}]]}, {"6": ["get", "(tabs_tabs_font_weight)", [{"7": "tabs_tabs_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(tabs_tabs_line_height)", [{"7": "tabs_tabs_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(tabs_tabs_letter_spacing), get-base(tabs_tabs_letter_spacing))", [{"6": ["get", "(tabs_tabs_letter_spacing)", [{"7": "tabs_tabs_letter_spacing"}]]}, {"6": ["get-base", "(tabs_tabs_letter_spacing)", [{"7": "tabs_tabs_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(tabs_tabs_letter_spacing)", [{"7": "tabs_tabs_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(tabs_tabs_text_align), get-base(tabs_tabs_text_align))", [{"6": ["get", "(tabs_tabs_text_align)", [{"7": "tabs_tabs_text_align"}]]}, {"6": ["get-base", "(tabs_tabs_text_align)", [{"7": "tabs_tabs_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(tabs_tabs_text_align)", [{"7": "tabs_tabs_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(tabs_tabs_text_decoration), get-base(tabs_tabs_text_decoration))", [{"6": ["get", "(tabs_tabs_text_decoration)", [{"7": "tabs_tabs_text_decoration"}]]}, {"6": ["get-base", "(tabs_tabs_text_decoration)", [{"7": "tabs_tabs_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(tabs_tabs_text_decoration)", [{"7": "tabs_tabs_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(tabs_tabs_text_shadow_dimensions)", [{"7": "tabs_tabs_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_tabs_text_shadow_color)", [{"7": "tabs_tabs_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(tabs_tabs_text_shadow_color_alt)", [{"7": "tabs_tabs_text_shadow_color_alt"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(tabs_tabs_text_transform), get-base(tabs_tabs_text_transform))", [{"6": ["get", "(tabs_tabs_text_transform)", [{"7": "tabs_tabs_text_transform"}]]}, {"6": ["get-base", "(tabs_tabs_text_transform)", [{"7": "tabs_tabs_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(tabs_tabs_text_transform)", [{"7": "tabs_tabs_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(tabs_tabs_text_color)", [{"7": "tabs_tabs_text_color"}]]}, {"6": ["get", "(tabs_tabs_text_color_alt)", [{"7": "tabs_tabs_text_color_alt"}]]}]]}, {"4": ["background-color", [{"7": "tabs_tabs_bg_color"}, {"7": "tabs_tabs_bg_color_alt"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(tabs_tabs_box_shadow_dimensions)", [{"7": "tabs_tabs_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_tabs_box_shadow_color)", [{"7": "tabs_tabs_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(tabs_tabs_box_shadow_color_alt)", [{"7": "tabs_tabs_box_shadow_color_alt"}]]}]}]]}]]}, {"3": [["& > .x-tabs-list button span"], [{"9": [{"6": ["is-set", "(get(tabs_tabs_letter_spacing), get-base(tabs_tabs_letter_spacing))", [{"6": ["get", "(tabs_tabs_letter_spacing)", [{"7": "tabs_tabs_letter_spacing"}]]}, {"6": ["get-base", "(tabs_tabs_letter_spacing)", [{"7": "tabs_tabs_letter_spacing"}]]}]]}, [{"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(tabs_tabs_letter_spacing)", [{"7": "tabs_tabs_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(tabs_tabs_letter_spacing)", [{"7": "tabs_tabs_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}]]}, {"3": [["& > .x-tabs-list button:hover", "& > .x-tabs-list button[class*=\"active\"]"], [{"4": ["text-color-alt", [{"6": ["get", "(tabs_tabs_text_color)", [{"7": "tabs_tabs_text_color"}]]}, {"6": ["get", "(tabs_tabs_text_color_alt)", [{"7": "tabs_tabs_text_color_alt"}]]}]]}, {"4": ["border-alt", [{"5": ["width", {"6": ["get", "(tabs_tabs_border_width)", [{"7": "tabs_tabs_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(tabs_tabs_border_style)", [{"7": "tabs_tabs_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_tabs_border_color)", [{"7": "tabs_tabs_border_color"}]]}]}, {"5": ["alt", {"6": ["get", "(tabs_tabs_border_color_alt)", [{"7": "tabs_tabs_border_color_alt"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(tabs_tabs_border_radius)", [{"7": "tabs_tabs_border_radius"}]]}, {"6": ["get", "(tabs_tabs_border_radius)", [{"7": "tabs_tabs_border_radius"}]]}]]}, {"4": ["background-color-alt", [{"6": ["get", "(tabs_tabs_bg_color)", [{"7": "tabs_tabs_bg_color"}]]}, {"6": ["get", "(tabs_tabs_bg_color_alt)", [{"7": "tabs_tabs_bg_color_alt"}]]}]]}, {"4": ["box-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(tabs_tabs_box_shadow_dimensions)", [{"7": "tabs_tabs_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_tabs_box_shadow_color)", [{"7": "tabs_tabs_box_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(tabs_tabs_box_shadow_color_alt)", [{"7": "tabs_tabs_box_shadow_color_alt"}]]}]}]]}, {"4": ["text-shadow-alt", [{"5": ["dimensions", {"6": ["get", "(tabs_tabs_text_shadow_dimensions)", [{"7": "tabs_tabs_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_tabs_text_shadow_color)", [{"7": "tabs_tabs_text_shadow_color"}]]}]}, {"5": ["alt", {"6": ["get", "(tabs_tabs_text_shadow_color_alt)", [{"7": "tabs_tabs_text_shadow_color_alt"}]]}]}]]}]]}, {"3": [["& > .x-tabs-panels > .x-tabs-panel"], [{"4": ["margin", [{"6": ["get-base", "(tabs_panels_margin)", [{"7": "tabs_panels_margin"}]]}, {"6": ["get", "(tabs_panels_margin)", [{"7": "tabs_panels_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(tabs_panels_border_width)", [{"7": "tabs_panels_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(tabs_panels_border_style)", [{"7": "tabs_panels_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_panels_border_color)", [{"7": "tabs_panels_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(tabs_panels_border_radius)", [{"7": "tabs_panels_border_radius"}]]}, {"6": ["get", "(tabs_panels_border_radius)", [{"7": "tabs_panels_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(tabs_panels_padding)", [{"7": "tabs_panels_padding"}]]}, {"6": ["get", "(tabs_panels_padding)", [{"7": "tabs_panels_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(tabs_panels_font_family))", [{"6": ["get", "(tabs_panels_font_family)", [{"7": "tabs_panels_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(tabs_panels_font_size)", [{"7": "tabs_panels_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(tabs_panels_font_style)", [{"7": "tabs_panels_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(tabs_panels_font_family),get(tabs_panels_font_weight))", [{"6": ["get", "(tabs_panels_font_family)", [{"7": "tabs_panels_font_family"}]]}, {"6": ["get", "(tabs_panels_font_weight)", [{"7": "tabs_panels_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(tabs_panels_line_height)", [{"7": "tabs_panels_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(tabs_panels_letter_spacing), get-base(tabs_panels_letter_spacing))", [{"6": ["get", "(tabs_panels_letter_spacing)", [{"7": "tabs_panels_letter_spacing"}]]}, {"6": ["get-base", "(tabs_panels_letter_spacing)", [{"7": "tabs_panels_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(tabs_panels_letter_spacing)", [{"7": "tabs_panels_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(tabs_panels_text_align), get-base(tabs_panels_text_align))", [{"6": ["get", "(tabs_panels_text_align)", [{"7": "tabs_panels_text_align"}]]}, {"6": ["get-base", "(tabs_panels_text_align)", [{"7": "tabs_panels_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(tabs_panels_text_align)", [{"7": "tabs_panels_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(tabs_panels_text_decoration), get-base(tabs_panels_text_decoration))", [{"6": ["get", "(tabs_panels_text_decoration)", [{"7": "tabs_panels_text_decoration"}]]}, {"6": ["get-base", "(tabs_panels_text_decoration)", [{"7": "tabs_panels_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(tabs_panels_text_decoration)", [{"7": "tabs_panels_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(tabs_panels_text_shadow_dimensions)", [{"7": "tabs_panels_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_panels_text_shadow_color)", [{"7": "tabs_panels_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(tabs_panels_text_transform), get-base(tabs_panels_text_transform))", [{"6": ["get", "(tabs_panels_text_transform)", [{"7": "tabs_panels_text_transform"}]]}, {"6": ["get-base", "(tabs_panels_text_transform)", [{"7": "tabs_panels_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(tabs_panels_text_transform)", [{"7": "tabs_panels_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(tabs_panels_text_color)", [{"7": "tabs_panels_text_color"}]]}]]}, {"4": ["background-color", [{"7": "tabs_panels_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(tabs_panels_box_shadow_dimensions)", [{"7": "tabs_panels_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(tabs_panels_box_shadow_color)", [{"7": "tabs_panels_box_shadow_color"}]]}]}]]}]]}, {"9": [{"6": ["get", "(tabs_panels_equal_height)", [{"7": "tabs_panels_equal_height"}]]}, [{"3": [["& > .x-tabs-panels > .x-tabs-panel.x-active"], [{"10": ["display", {"7": "flex"}, false]}, {"10": ["flex-flow", {"12": [{"7": "column"}, {"7": "nowrap"}]}, false]}, {"10": ["justify-content", {"6": ["get", "(tabs_panels_flex_justify)", [{"7": "tabs_panels_flex_justify"}]]}, false]}, {"10": ["align-items", {"6": ["get", "(tabs_panels_flex_align)", [{"7": "tabs_panels_flex_align"}]]}, false]}]]}]]}]]}, {"2": ["testimonial", [], [{"10": ["flex-direction", {"6": ["get", "(testimonial_graphic_flex_direction)", [{"7": "testimonial_graphic_flex_direction"}]]}, false]}, {"10": ["align-items", {"6": ["get", "(testimonial_graphic_flex_align)", [{"7": "testimonial_graphic_flex_align"}]]}, false]}, {"4": ["changedmixin", [{"8": "auto"}, {"7": "testimonial_width"}, {"8": "width"}]]}, {"9": [{"6": ["is-set", "(get(testimonial_max_width), get-base(testimonial_max_width))", [{"6": ["get", "(testimonial_max_width)", [{"7": "testimonial_max_width"}]]}, {"6": ["get-base", "(testimonial_max_width)", [{"7": "testimonial_max_width"}]]}]]}, [{"10": ["max-width", {"6": ["get", "(testimonial_max_width)", [{"7": "testimonial_max_width"}]]}, false]}]]}, {"4": ["margin", [{"6": ["get-base", "(testimonial_margin)", [{"7": "testimonial_margin"}]]}, {"6": ["get", "(testimonial_margin)", [{"7": "testimonial_margin"}]]}, {"13": "0"}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(testimonial_border_width)", [{"7": "testimonial_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(testimonial_border_style)", [{"7": "testimonial_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_border_color)", [{"7": "testimonial_border_color"}]]}]}, {"5": ["fallback", {"7": true}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(testimonial_border_radius)", [{"7": "testimonial_border_radius"}]]}, {"6": ["get", "(testimonial_border_radius)", [{"7": "testimonial_border_radius"}]]}, {"13": "0"}]]}, {"4": ["padding", [{"6": ["get-base", "(testimonial_padding)", [{"7": "testimonial_padding"}]]}, {"6": ["get", "(testimonial_padding)", [{"7": "testimonial_padding"}]]}, {"13": "0"}]]}, {"10": ["font-size", {"6": ["get", "(testimonial_base_font_size)", [{"7": "testimonial_base_font_size"}]]}, false]}, {"4": ["background-color", [{"7": "testimonial_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(testimonial_box_shadow_dimensions)", [{"7": "testimonial_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_box_shadow_color)", [{"7": "testimonial_box_shadow_color"}]]}]}]]}, {"3": [[".x-testimonial-content"], [{"4": ["margin", [{"6": ["get-base", "(testimonial_content_margin)", [{"7": "testimonial_content_margin"}]]}, {"6": ["get", "(testimonial_content_margin)", [{"7": "testimonial_content_margin"}]]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(testimonial_content_border_width)", [{"7": "testimonial_content_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(testimonial_content_border_style)", [{"7": "testimonial_content_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_content_border_color)", [{"7": "testimonial_content_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(testimonial_content_border_radius)", [{"7": "testimonial_content_border_radius"}]]}, {"6": ["get", "(testimonial_content_border_radius)", [{"7": "testimonial_content_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(testimonial_content_padding)", [{"7": "testimonial_content_padding"}]]}, {"6": ["get", "(testimonial_content_padding)", [{"7": "testimonial_content_padding"}]]}]]}, {"4": ["background-color", [{"7": "testimonial_content_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(testimonial_content_box_shadow_dimensions)", [{"7": "testimonial_content_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_content_box_shadow_color)", [{"7": "testimonial_content_box_shadow_color"}]]}]}]]}]]}, {"3": [[".x-testimonial-text"], [{"10": ["font-family", {"6": ["global-ff", "(get(testimonial_text_font_family))", [{"6": ["get", "(testimonial_text_font_family)", [{"7": "testimonial_text_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(testimonial_text_font_size)", [{"7": "testimonial_text_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(testimonial_text_font_style)", [{"7": "testimonial_text_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(testimonial_text_font_family),get(testimonial_text_font_weight))", [{"6": ["get", "(testimonial_text_font_family)", [{"7": "testimonial_text_font_family"}]]}, {"6": ["get", "(testimonial_text_font_weight)", [{"7": "testimonial_text_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(testimonial_text_line_height)", [{"7": "testimonial_text_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(testimonial_text_letter_spacing), get-base(testimonial_text_letter_spacing))", [{"6": ["get", "(testimonial_text_letter_spacing)", [{"7": "testimonial_text_letter_spacing"}]]}, {"6": ["get-base", "(testimonial_text_letter_spacing)", [{"7": "testimonial_text_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(testimonial_text_letter_spacing)", [{"7": "testimonial_text_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(testimonial_text_text_align), get-base(testimonial_text_text_align))", [{"6": ["get", "(testimonial_text_text_align)", [{"7": "testimonial_text_text_align"}]]}, {"6": ["get-base", "(testimonial_text_text_align)", [{"7": "testimonial_text_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(testimonial_text_text_align)", [{"7": "testimonial_text_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(testimonial_text_text_decoration), get-base(testimonial_text_text_decoration))", [{"6": ["get", "(testimonial_text_text_decoration)", [{"7": "testimonial_text_text_decoration"}]]}, {"6": ["get-base", "(testimonial_text_text_decoration)", [{"7": "testimonial_text_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(testimonial_text_text_decoration)", [{"7": "testimonial_text_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(testimonial_text_text_shadow_dimensions)", [{"7": "testimonial_text_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_text_text_shadow_color)", [{"7": "testimonial_text_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(testimonial_text_text_transform), get-base(testimonial_text_text_transform))", [{"6": ["get", "(testimonial_text_text_transform)", [{"7": "testimonial_text_text_transform"}]]}, {"6": ["get-base", "(testimonial_text_text_transform)", [{"7": "testimonial_text_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(testimonial_text_text_transform)", [{"7": "testimonial_text_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(testimonial_text_text_color)", [{"7": "testimonial_text_text_color"}]]}]]}]]}, {"9": [{"17": [{"17": [{"6": ["get", "(testimonial_cite_content)", [{"7": "testimonial_cite_content"}]]}, "!=", {"8": ""}]}, "or", {"17": [{"6": ["get", "(testimonial_graphic)", [{"7": "testimonial_graphic"}]]}, "or", {"6": ["get", "(testimonial_rating)", [{"7": "testimonial_rating"}]]}]}]}, [{"3": [[".x-testimonial-cite"], [{"10": ["flex-direction", {"6": ["get", "(testimonial_graphic_flex_direction)", [{"7": "testimonial_graphic_flex_direction"}]]}, false]}, {"10": ["align-items", {"6": ["get", "(testimonial_graphic_flex_align)", [{"7": "testimonial_graphic_flex_align"}]]}, false]}, {"10": ["align-self", {"6": ["get", "(testimonial_cite_align_self)", [{"7": "testimonial_cite_align_self"}]]}, false]}, {"9": [{"17": [{"6": ["get", "(testimonial_cite_position)", [{"7": "testimonial_cite_position"}]]}, "==", {"8": "before"}]}, [{"10": ["margin-bottom", {"6": ["get", "(testimonial_cite_spacing)", [{"7": "testimonial_cite_spacing"}]]}, false]}]]}, {"9": [{"17": [{"6": ["get", "(testimonial_cite_position)", [{"7": "testimonial_cite_position"}]]}, "==", {"8": "after"}]}, [{"10": ["margin-top", {"6": ["get", "(testimonial_cite_spacing)", [{"7": "testimonial_cite_spacing"}]]}, false]}]]}, {"4": ["border", [{"5": ["width", {"6": ["get", "(testimonial_cite_border_width)", [{"7": "testimonial_cite_border_width"}]]}]}, {"5": ["style", {"6": ["get", "(testimonial_cite_border_style)", [{"7": "testimonial_cite_border_style"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_cite_border_color)", [{"7": "testimonial_cite_border_color"}]]}]}]]}, {"4": ["border-radius", [{"6": ["get-base", "(testimonial_cite_border_radius)", [{"7": "testimonial_cite_border_radius"}]]}, {"6": ["get", "(testimonial_cite_border_radius)", [{"7": "testimonial_cite_border_radius"}]]}]]}, {"4": ["padding", [{"6": ["get-base", "(testimonial_cite_padding)", [{"7": "testimonial_cite_padding"}]]}, {"6": ["get", "(testimonial_cite_padding)", [{"7": "testimonial_cite_padding"}]]}]]}, {"10": ["font-family", {"6": ["global-ff", "(get(testimonial_cite_font_family))", [{"6": ["get", "(testimonial_cite_font_family)", [{"7": "testimonial_cite_font_family"}]]}]]}, false]}, {"10": ["font-size", {"6": ["get", "(testimonial_cite_font_size)", [{"7": "testimonial_cite_font_size"}]]}, false]}, {"10": ["font-style", {"6": ["get", "(testimonial_cite_font_style)", [{"7": "testimonial_cite_font_style"}]]}, false]}, {"10": ["font-weight", {"6": ["global-fw", "(get(testimonial_cite_font_family),get(testimonial_cite_font_weight))", [{"6": ["get", "(testimonial_cite_font_family)", [{"7": "testimonial_cite_font_family"}]]}, {"6": ["get", "(testimonial_cite_font_weight)", [{"7": "testimonial_cite_font_weight"}]]}]]}, false]}, {"10": ["line-height", {"6": ["get", "(testimonial_cite_line_height)", [{"7": "testimonial_cite_line_height"}]]}, false]}, {"9": [{"6": ["is-set", "(get(testimonial_cite_letter_spacing), get-base(testimonial_cite_letter_spacing))", [{"6": ["get", "(testimonial_cite_letter_spacing)", [{"7": "testimonial_cite_letter_spacing"}]]}, {"6": ["get-base", "(testimonial_cite_letter_spacing)", [{"7": "testimonial_cite_letter_spacing"}]]}]]}, [{"10": ["letter-spacing", {"6": ["get", "(testimonial_cite_letter_spacing)", [{"7": "testimonial_cite_letter_spacing"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(testimonial_cite_text_align), get-base(testimonial_cite_text_align))", [{"6": ["get", "(testimonial_cite_text_align)", [{"7": "testimonial_cite_text_align"}]]}, {"6": ["get-base", "(testimonial_cite_text_align)", [{"7": "testimonial_cite_text_align"}]]}]]}, [{"10": ["text-align", {"6": ["get", "(testimonial_cite_text_align)", [{"7": "testimonial_cite_text_align"}]]}, false]}]]}, {"9": [{"6": ["is-set", "(get(testimonial_cite_text_decoration), get-base(testimonial_cite_text_decoration))", [{"6": ["get", "(testimonial_cite_text_decoration)", [{"7": "testimonial_cite_text_decoration"}]]}, {"6": ["get-base", "(testimonial_cite_text_decoration)", [{"7": "testimonial_cite_text_decoration"}]]}]]}, [{"10": ["text-decoration", {"6": ["get", "(testimonial_cite_text_decoration)", [{"7": "testimonial_cite_text_decoration"}]]}, false]}]]}, {"4": ["text-shadow", [{"5": ["dimensions", {"6": ["get", "(testimonial_cite_text_shadow_dimensions)", [{"7": "testimonial_cite_text_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_cite_text_shadow_color)", [{"7": "testimonial_cite_text_shadow_color"}]]}]}]]}, {"9": [{"6": ["is-set", "(get(testimonial_cite_text_transform), get-base(testimonial_cite_text_transform))", [{"6": ["get", "(testimonial_cite_text_transform)", [{"7": "testimonial_cite_text_transform"}]]}, {"6": ["get-base", "(testimonial_cite_text_transform)", [{"7": "testimonial_cite_text_transform"}]]}]]}, [{"10": ["text-transform", {"6": ["get", "(testimonial_cite_text_transform)", [{"7": "testimonial_cite_text_transform"}]]}, false]}]]}, {"4": ["text-color", [{"6": ["get", "(testimonial_cite_text_color)", [{"7": "testimonial_cite_text_color"}]]}]]}, {"4": ["background-color", [{"7": "testimonial_cite_bg_color"}]]}, {"4": ["box-shadow", [{"5": ["dimensions", {"6": ["get", "(testimonial_cite_box_shadow_dimensions)", [{"7": "testimonial_cite_box_shadow_dimensions"}]]}]}, {"5": ["base", {"6": ["get", "(testimonial_cite_box_shadow_color)", [{"7": "testimonial_cite_box_shadow_color"}]]}]}]]}]]}, {"3": [[".x-testimonial-cite-text"], [{"9": [{"6": ["is-set", "(get(testimonial_cite_letter_spacing), get-base(testimonial_cite_letter_spacing))", [{"6": ["get", "(testimonial_cite_letter_spacing)", [{"7": "testimonial_cite_letter_spacing"}]]}, {"6": ["get-base", "(testimonial_cite_letter_spacing)", [{"7": "testimonial_cite_letter_spacing"}]]}]]}, [{"10": ["margin-right", {"6": ["calc", {"14": ["(%s * -1)", [{"6": ["get", "(testimonial_cite_letter_spacing)", [{"7": "testimonial_cite_letter_spacing"}]]}]]}, [{"17": [{"7": {"14": ["%s", [{"6": ["get", "(testimonial_cite_letter_spacing)", [{"7": "testimonial_cite_letter_spacing"}]]}]]}}, "*", {"11": ["-", {"13": "1"}]}]}]]}, false]}]]}]]}, {"9": [{"17": [{"6": ["get", "(testimonial_cite_position)", [{"7": "testimonial_cite_position"}]]}, "==", {"8": "before"}]}, [{"3": [[".x-testimonial-content"], [{"10": ["flex-direction", {"7": "column-reverse"}, false]}]]}]]}]]}]]}, {"2": ["content-area", [], [{"4": ["margin", [{"6": ["get-base", "(content_margin)", [{"7": "content_margin"}]]}, {"6": ["get", "(content_margin)", [{"7": "content_margin"}]]}]]}]]}]}, {"document": 1, "module": 2, "styleRule": 3, "include": 4, "keywordArgument": 5, "call": 6, "primitive": 7, "singleQuotedString": 8, "if": 9, "assignProperty": 10, "unary": 11, "list": 12, "number": 13, "interpolated": 14, "else": 15, "dimension": 16, "operation": 17, "doubleQuotedString": 18}]